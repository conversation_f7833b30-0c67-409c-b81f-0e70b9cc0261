"""
Inventory service for business logic operations.
"""
from typing import Optional, List, Tuple, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from decimal import Decimal
import logging

from app.models.inventory import Inventory
from app.models.part import Part
from app.models.dealership import Dealership
from app.repositories.inventory import InventoryRepository
from app.repositories.part import PartRepository
from app.repositories.dealership import DealershipRepository
from app.schemas.inventory import (
    InventoryCreate,
    InventoryUpdate,
    InventorySearchFilters,
    InventoryWithDetails,
    InventoryStats,
    DealershipInventoryStats,
    InventoryValuation,
    BulkInventoryUpdate,
    BulkInventoryResponse
)
from app.schemas.part import PartSummary
from app.schemas.dealership import DealershipSummary
from app.core.cache_decorators import cached, cache_invalidate_pattern, CacheService
from app.core.cache import CacheTTL, CacheKeys

logger = logging.getLogger(__name__)


class InventoryService:
    """
    Service class for inventory management operations.
    """

    def __init__(self, db: Session):
        self.db = db
        self.inventory_repo = InventoryRepository(db)
        self.part_repo = PartRepository(db)
        self.dealership_repo = DealershipRepository(db)

    @cache_invalidate_pattern("inventory:*")
    def create_inventory_item(
        self,
        inventory_data: InventoryCreate,
        user_dealership_id: Optional[UUID] = None
    ) -> Inventory:
        """
        Create a new inventory item.
        
        Args:
            inventory_data: Inventory creation data
            user_dealership_id: User's dealership ID for permission check
            
        Returns:
            Created inventory instance
            
        Raises:
            ValueError: If validation fails
            PermissionError: If user doesn't have permission
        """
        # Check permissions for dealership users
        if user_dealership_id and inventory_data.dealership_id != user_dealership_id:
            raise PermissionError("Cannot create inventory for other dealerships")
        
        # Verify dealership exists
        dealership = self.dealership_repo.get_by_id(inventory_data.dealership_id)
        if not dealership:
            raise ValueError("Dealership not found")
        
        # Verify part exists
        part = self.part_repo.get_by_id(inventory_data.part_id)
        if not part:
            raise ValueError("Part not found")
        
        # Check if inventory item already exists
        existing = self.inventory_repo.get_by_dealership_and_part(
            inventory_data.dealership_id,
            inventory_data.part_id
        )
        if existing:
            raise ValueError("Inventory item already exists for this part and dealership")
        
        # Create inventory item
        inventory_dict = inventory_data.dict()
        inventory_dict['last_updated'] = datetime.utcnow()
        
        inventory = self.inventory_repo.create(inventory_dict)
        
        logger.info(f"Created inventory item {inventory.id} for dealership {inventory_data.dealership_id}")
        return inventory

    def update_inventory_item(
        self,
        inventory_id: UUID,
        update_data: InventoryUpdate,
        user_dealership_id: Optional[UUID] = None
    ) -> Optional[Inventory]:
        """
        Update inventory item.
        
        Args:
            inventory_id: Inventory UUID
            update_data: Updated inventory data
            user_dealership_id: User's dealership ID for permission check
            
        Returns:
            Updated inventory instance or None if not found
            
        Raises:
            PermissionError: If user doesn't have permission
        """
        inventory = self.inventory_repo.get_by_id(inventory_id)
        if not inventory:
            return None
        
        # Check permissions for dealership users
        if user_dealership_id and inventory.dealership_id != user_dealership_id:
            raise PermissionError("Cannot update inventory for other dealerships")
        
        # Update inventory
        update_dict = update_data.dict(exclude_unset=True)
        update_dict['last_updated'] = datetime.utcnow()
        
        updated_inventory = self.inventory_repo.update(inventory_id, update_dict)
        
        if updated_inventory:
            logger.info(f"Updated inventory item {inventory_id}")
        
        return updated_inventory

    def get_inventory_by_id(
        self,
        inventory_id: UUID,
        user_dealership_id: Optional[UUID] = None
    ) -> Optional[InventoryWithDetails]:
        """
        Get inventory item by ID with details.
        
        Args:
            inventory_id: Inventory UUID
            user_dealership_id: User's dealership ID for permission check
            
        Returns:
            Inventory with details or None if not found
            
        Raises:
            PermissionError: If user doesn't have permission
        """
        inventory = self.inventory_repo.get_by_id(inventory_id)
        if not inventory:
            return None
        
        # Check permissions for dealership users
        if user_dealership_id and inventory.dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")
        
        return self._enrich_inventory_with_details(inventory)

    def search_inventory(
        self,
        filters: InventorySearchFilters,
        user_dealership_id: Optional[UUID] = None
    ) -> Tuple[List[InventoryWithDetails], int]:
        """
        Search inventory with advanced filtering.
        
        Args:
            filters: Search filters
            user_dealership_id: User's dealership ID for permission check
            
        Returns:
            Tuple of (inventory list with details, total count)
        """
        # Apply dealership filter for non-admin users
        if user_dealership_id:
            filters.dealership_id = user_dealership_id
        
        skip = (filters.page - 1) * filters.per_page
        
        # Perform search
        inventories, total = self.inventory_repo.search_inventory(
            part_code=filters.part_code,
            part_name=filters.part_name,
            dealership_id=filters.dealership_id,
            brand=filters.brand,
            model=filters.model,
            year=filters.year,
            category=filters.category,
            condition=filters.condition,
            min_price=filters.min_price,
            max_price=filters.max_price,
            min_quantity=filters.min_quantity,
            available_only=filters.available_only,
            in_stock_only=filters.in_stock_only,
            low_stock_only=filters.low_stock_only,
            city=filters.city,
            state=filters.state,
            skip=skip,
            limit=filters.per_page,
            sort_by=filters.sort_by,
            sort_order=filters.sort_order
        )
        
        # Enrich with details
        enriched_inventories = [
            self._enrich_inventory_with_details(inventory)
            for inventory in inventories
        ]
        
        return enriched_inventories, total

    def get_dealership_inventory(
        self,
        dealership_id: UUID,
        skip: int = 0,
        limit: int = 100,
        available_only: bool = True,
        user_dealership_id: Optional[UUID] = None
    ) -> Tuple[List[InventoryWithDetails], int]:
        """
        Get inventory for a specific dealership.
        
        Args:
            dealership_id: Dealership UUID
            skip: Number of records to skip
            limit: Maximum number of records to return
            available_only: Only return available items
            user_dealership_id: User's dealership ID for permission check
            
        Returns:
            Tuple of (inventory list with details, total count)
            
        Raises:
            PermissionError: If user doesn't have permission
        """
        # Check permissions for dealership users
        if user_dealership_id and dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")
        
        inventories, total = self.inventory_repo.get_by_dealership(
            dealership_id, skip, limit, available_only
        )
        
        # Enrich with details
        enriched_inventories = [
            self._enrich_inventory_with_details(inventory)
            for inventory in inventories
        ]
        
        return enriched_inventories, total

    def get_part_inventory(
        self,
        part_id: UUID,
        skip: int = 0,
        limit: int = 100,
        available_only: bool = True
    ) -> Tuple[List[InventoryWithDetails], int]:
        """
        Get inventory for a specific part across all dealerships.
        
        Args:
            part_id: Part UUID
            skip: Number of records to skip
            limit: Maximum number of records to return
            available_only: Only return available items
            
        Returns:
            Tuple of (inventory list with details, total count)
        """
        inventories, total = self.inventory_repo.get_by_part(
            part_id, skip, limit, available_only
        )
        
        # Enrich with details
        enriched_inventories = [
            self._enrich_inventory_with_details(inventory)
            for inventory in inventories
        ]
        
        return enriched_inventories, total

    def _enrich_inventory_with_details(self, inventory: Inventory) -> InventoryWithDetails:
        """
        Enrich inventory with part and dealership details.
        
        Args:
            inventory: Inventory instance
            
        Returns:
            Inventory with details
        """
        # Get part details
        part = self.part_repo.get_by_id(inventory.part_id)
        part_summary = PartSummary.from_orm(part) if part else None
        
        # Get dealership details
        dealership = self.dealership_repo.get_by_id(inventory.dealership_id)
        dealership_summary = DealershipSummary(
            id=dealership.id,
            name=dealership.name,
            city=dealership.city,
            state=dealership.state,
            phone=dealership.phone,
            email=dealership.email,
            is_active=dealership.is_active
        ) if dealership else None
        
        return InventoryWithDetails(
            **inventory.__dict__,
            part=part_summary,
            dealership=dealership_summary,
            is_low_stock=inventory.is_low_stock,
            is_in_stock=inventory.is_in_stock
        )

    def get_low_stock_items(
        self,
        dealership_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100,
        user_dealership_id: Optional[UUID] = None
    ) -> Tuple[List[InventoryWithDetails], int]:
        """
        Get items with low stock levels.

        Args:
            dealership_id: Optional dealership filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Tuple of (inventory list with details, total count)

        Raises:
            PermissionError: If user doesn't have permission
        """
        # Apply dealership filter for non-admin users
        if user_dealership_id:
            dealership_id = user_dealership_id
        elif user_dealership_id and dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")

        inventories, total = self.inventory_repo.get_low_stock_items(
            dealership_id, skip, limit
        )

        # Enrich with details
        enriched_inventories = [
            self._enrich_inventory_with_details(inventory)
            for inventory in inventories
        ]

        return enriched_inventories, total

    def get_out_of_stock_items(
        self,
        dealership_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100,
        user_dealership_id: Optional[UUID] = None
    ) -> Tuple[List[InventoryWithDetails], int]:
        """
        Get items that are out of stock.

        Args:
            dealership_id: Optional dealership filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Tuple of (inventory list with details, total count)

        Raises:
            PermissionError: If user doesn't have permission
        """
        # Apply dealership filter for non-admin users
        if user_dealership_id:
            dealership_id = user_dealership_id
        elif user_dealership_id and dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")

        inventories, total = self.inventory_repo.get_out_of_stock_items(
            dealership_id, skip, limit
        )

        # Enrich with details
        enriched_inventories = [
            self._enrich_inventory_with_details(inventory)
            for inventory in inventories
        ]

        return enriched_inventories, total

    @cached(
        key_prefix=CacheKeys.INVENTORY_STATS,
        ttl=CacheTTL.INVENTORY_STATS,
        key_params=['dealership_id']
    )
    def get_inventory_stats(
        self,
        dealership_id: Optional[UUID] = None,
        user_dealership_id: Optional[UUID] = None
    ) -> InventoryStats:
        """
        Get inventory statistics.

        Args:
            dealership_id: Optional dealership filter
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Inventory statistics

        Raises:
            PermissionError: If user doesn't have permission
        """
        # Apply dealership filter for non-admin users
        if user_dealership_id:
            dealership_id = user_dealership_id
        elif user_dealership_id and dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")

        stats_data = self.inventory_repo.get_inventory_stats(dealership_id)

        # Get top dealerships (only for admin/global stats)
        top_dealerships = []
        if not dealership_id:
            # Get top dealerships by inventory count
            top_dealerships_data = self.db.query(
                Dealership.id,
                Dealership.name,
                func.count(Inventory.id).label('inventory_count')
            ).join(Inventory).group_by(
                Dealership.id, Dealership.name
            ).order_by(
                func.count(Inventory.id).desc()
            ).limit(10).all()

            top_dealerships = [
                {
                    'dealership_id': str(d.id),
                    'name': d.name,
                    'inventory_count': d.inventory_count
                }
                for d in top_dealerships_data
            ]

        return InventoryStats(
            total_items=stats_data['total_items'],
            total_value=Decimal(str(stats_data['total_value'])),
            low_stock_items=stats_data['low_stock_items'],
            out_of_stock_items=stats_data['out_of_stock_items'],
            items_by_condition=stats_data['items_by_condition'],
            items_by_category=stats_data['items_by_category'],
            top_dealerships=top_dealerships,
            recent_updates=stats_data['recent_updates']
        )

    def get_dealership_inventory_stats(
        self,
        dealership_id: UUID,
        user_dealership_id: Optional[UUID] = None
    ) -> DealershipInventoryStats:
        """
        Get inventory statistics for a specific dealership.

        Args:
            dealership_id: Dealership UUID
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Dealership inventory statistics

        Raises:
            PermissionError: If user doesn't have permission
        """
        # Check permissions for dealership users
        if user_dealership_id and dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")

        stats_data = self.inventory_repo.get_inventory_stats(dealership_id)

        # Calculate average price
        average_price = 0
        if stats_data['total_items'] > 0:
            total_inventory_value = self.db.query(
                func.sum(Inventory.price * Inventory.quantity)
            ).filter(Inventory.dealership_id == dealership_id).scalar() or 0

            total_quantity = self.db.query(
                func.sum(Inventory.quantity)
            ).filter(Inventory.dealership_id == dealership_id).scalar() or 0

            if total_quantity > 0:
                average_price = float(total_inventory_value) / total_quantity

        # Get last update
        last_update = self.db.query(
            func.max(Inventory.last_updated)
        ).filter(Inventory.dealership_id == dealership_id).scalar()

        return DealershipInventoryStats(
            dealership_id=dealership_id,
            total_items=stats_data['total_items'],
            total_value=Decimal(str(stats_data['total_value'])),
            average_price=Decimal(str(average_price)),
            low_stock_items=stats_data['low_stock_items'],
            out_of_stock_items=stats_data['out_of_stock_items'],
            items_by_condition=stats_data['items_by_condition'],
            items_by_category=stats_data['items_by_category'],
            last_update=last_update
        )

    def get_inventory_valuation(
        self,
        dealership_id: UUID,
        user_dealership_id: Optional[UUID] = None
    ) -> InventoryValuation:
        """
        Get inventory valuation for a dealership.

        Args:
            dealership_id: Dealership UUID
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Inventory valuation

        Raises:
            PermissionError: If user doesn't have permission
        """
        # Check permissions for dealership users
        if user_dealership_id and dealership_id != user_dealership_id:
            raise PermissionError("Cannot access inventory for other dealerships")

        valuation_data = self.inventory_repo.get_inventory_valuation(dealership_id)

        return InventoryValuation(
            dealership_id=dealership_id,
            total_cost=Decimal(str(valuation_data['total_cost'])),
            total_selling_value=Decimal(str(valuation_data['total_selling_value'])),
            potential_profit=Decimal(str(valuation_data['potential_profit'])),
            margin_percentage=valuation_data['margin_percentage'],
            items_count=valuation_data['items_count'],
            valuation_date=valuation_data['valuation_date']
        )

    def update_inventory_quantity(
        self,
        inventory_id: UUID,
        new_quantity: int,
        reason: Optional[str] = None,
        user_dealership_id: Optional[UUID] = None
    ) -> Optional[Inventory]:
        """
        Update inventory quantity.

        Args:
            inventory_id: Inventory UUID
            new_quantity: New quantity value
            reason: Reason for the change
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Updated inventory instance or None if not found

        Raises:
            PermissionError: If user doesn't have permission
        """
        inventory = self.inventory_repo.get_by_id(inventory_id)
        if not inventory:
            return None

        # Check permissions for dealership users
        if user_dealership_id and inventory.dealership_id != user_dealership_id:
            raise PermissionError("Cannot update inventory for other dealerships")

        updated_inventory = self.inventory_repo.update_quantity(
            inventory_id, new_quantity, reason
        )

        if updated_inventory:
            logger.info(f"Updated quantity for inventory {inventory_id}: {new_quantity}")

        return updated_inventory

    def bulk_update_inventory(
        self,
        bulk_data: BulkInventoryUpdate,
        user_dealership_id: Optional[UUID] = None
    ) -> BulkInventoryResponse:
        """
        Perform bulk inventory updates.

        Args:
            bulk_data: Bulk update data
            user_dealership_id: User's dealership ID for permission check

        Returns:
            Bulk operation response
        """
        processed = 0
        updated = 0
        errors = []

        for update_item in bulk_data.updates:
            try:
                inventory_id = UUID(update_item.get('inventory_id'))

                # Check if inventory exists and user has permission
                inventory = self.inventory_repo.get_by_id(inventory_id)
                if not inventory:
                    errors.append({
                        'inventory_id': str(inventory_id),
                        'error': 'Inventory item not found'
                    })
                    continue

                # Check permissions for dealership users
                if user_dealership_id and inventory.dealership_id != user_dealership_id:
                    errors.append({
                        'inventory_id': str(inventory_id),
                        'error': 'Permission denied'
                    })
                    continue

                # Perform operation based on type
                if bulk_data.operation == "update":
                    field = update_item.get('field')
                    value = update_item.get('value')

                    if field and value is not None:
                        update_dict = {field: value, 'last_updated': datetime.utcnow()}
                        self.inventory_repo.update(inventory_id, update_dict)
                        updated += 1

                elif bulk_data.operation == "adjust_quantity":
                    adjustment = update_item.get('adjustment', 0)
                    new_quantity = max(0, inventory.quantity + adjustment)
                    self.inventory_repo.update_quantity(inventory_id, new_quantity)
                    updated += 1

                elif bulk_data.operation == "set_availability":
                    is_available = update_item.get('is_available', True)
                    update_dict = {
                        'is_available': is_available,
                        'last_updated': datetime.utcnow()
                    }
                    self.inventory_repo.update(inventory_id, update_dict)
                    updated += 1

                elif bulk_data.operation == "delete":
                    self.inventory_repo.delete(inventory_id)
                    updated += 1

                processed += 1

            except Exception as e:
                errors.append({
                    'inventory_id': update_item.get('inventory_id', 'Unknown'),
                    'error': str(e)
                })

        logger.info(f"Bulk operation completed: {updated} updated, {len(errors)} errors")

        return BulkInventoryResponse(
            processed=processed,
            updated=updated,
            errors=errors,
            total_requested=len(bulk_data.updates)
        )

    def delete_inventory_item(
        self,
        inventory_id: UUID,
        user_dealership_id: Optional[UUID] = None
    ) -> bool:
        """
        Delete an inventory item.

        Args:
            inventory_id: Inventory UUID
            user_dealership_id: User's dealership ID for permission check

        Returns:
            True if deleted successfully, False if not found

        Raises:
            PermissionError: If user doesn't have permission
        """
        inventory = self.inventory_repo.get_by_id(inventory_id)
        if not inventory:
            return False

        # Check permissions for dealership users
        if user_dealership_id and inventory.dealership_id != user_dealership_id:
            raise PermissionError("Cannot delete inventory for other dealerships")

        success = self.inventory_repo.delete(inventory_id)

        if success:
            logger.info(f"Deleted inventory item {inventory_id}")

        return success
