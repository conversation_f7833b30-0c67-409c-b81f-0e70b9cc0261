/**
 * Authentication Store Unit Tests
 * Tests for Zustand authentication store
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { useAuthStore } from '../../lib/stores/auth-store';
import { authService } from '../../lib/api/auth';
import { UserRole } from '../../lib/types/api';

// Mock the auth service
vi.mock('../../lib/api/auth');
const mockAuthService = vi.mocked(authService);

// Mock user data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'Test User',
  role: UserRole.DEALERSHIP,
  status: 'active' as const,
  is_active: true,
  email_verified: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockLoginResponse = {
  success: true,
  data: {
    access_token: 'access_token',
    refresh_token: 'refresh_token',
    token_type: 'Bearer',
    expires_in: 3600,
    user: mockUser,
  },
};

describe('AuthStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset store state
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useAuthStore.getState();
      
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
  });

  describe('Login', () => {
    it('should login successfully', async () => {
      mockAuthService.login.mockResolvedValueOnce(mockLoginResponse);

      const { login } = useAuthStore.getState();
      const result = await login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(true);
      expect(result.redirectTo).toBe('/painel'); // Dealership redirect

      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });

    it('should handle login failure', async () => {
      const errorResponse = {
        success: false,
        error: {
          error: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password',
          timestamp: Date.now(),
        },
      };

      mockAuthService.login.mockResolvedValueOnce(errorResponse);

      const { login } = useAuthStore.getState();
      const result = await login({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid email or password');

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBe('Invalid email or password');
    });

    it('should handle login exception', async () => {
      mockAuthService.login.mockRejectedValueOnce(new Error('Network error'));

      const { login } = useAuthStore.getState();
      const result = await login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');

      const state = useAuthStore.getState();
      expect(state.error).toBe('Network error');
    });

    it('should set loading state during login', async () => {
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve;
      });

      mockAuthService.login.mockReturnValueOnce(loginPromise);

      const { login } = useAuthStore.getState();
      const loginCall = login({
        email: '<EMAIL>',
        password: 'password123',
      });

      // Check loading state
      expect(useAuthStore.getState().isLoading).toBe(true);

      // Resolve the promise
      resolveLogin!(mockLoginResponse);
      await loginCall;

      // Check final state
      expect(useAuthStore.getState().isLoading).toBe(false);
    });
  });

  describe('Logout', () => {
    it('should logout successfully', async () => {
      // Set initial authenticated state
      useAuthStore.setState({
        user: mockUser,
        isAuthenticated: true,
      });

      mockAuthService.logout.mockResolvedValueOnce({ success: true });

      const { logout } = useAuthStore.getState();
      await logout();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
    });

    it('should clear state even if logout API fails', async () => {
      // Set initial authenticated state
      useAuthStore.setState({
        user: mockUser,
        isAuthenticated: true,
      });

      mockAuthService.logout.mockRejectedValueOnce(new Error('API error'));

      const { logout } = useAuthStore.getState();
      await logout();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });

  describe('Registration', () => {
    it('should register successfully', async () => {
      const registerResponse = {
        success: true,
        data: mockUser,
      };

      mockAuthService.register.mockResolvedValueOnce(registerResponse);

      const { register } = useAuthStore.getState();
      const result = await register({
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'New User',
        role: UserRole.DEALERSHIP,
      });

      expect(result.success).toBe(true);
      expect(useAuthStore.getState().error).toBeNull();
    });

    it('should handle registration failure', async () => {
      const errorResponse = {
        success: false,
        error: {
          error: 'EMAIL_EXISTS',
          message: 'Email already exists',
          timestamp: Date.now(),
        },
      };

      mockAuthService.register.mockResolvedValueOnce(errorResponse);

      const { register } = useAuthStore.getState();
      const result = await register({
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'New User',
        role: UserRole.DEALERSHIP,
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Email already exists');
      expect(useAuthStore.getState().error).toBe('Email already exists');
    });
  });

  describe('Refresh User', () => {
    it('should refresh user data when authenticated', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValueOnce({
        success: true,
        data: mockUser,
      });

      const { refreshUser } = useAuthStore.getState();
      await refreshUser();

      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should clear state when not authenticated', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);

      const { refreshUser } = useAuthStore.getState();
      await refreshUser();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });

    it('should handle refresh failure', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValueOnce({
        success: false,
        error: {
          error: 'TOKEN_EXPIRED',
          message: 'Token expired',
          timestamp: Date.now(),
        },
      });

      const { refreshUser } = useAuthStore.getState();
      await refreshUser();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Session expired');
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      useAuthStore.setState({
        user: mockUser,
        isAuthenticated: true,
      });
    });

    it('should get correct redirect path for roles', () => {
      const { getRedirectPath } = useAuthStore.getState();
      
      expect(getRedirectPath(UserRole.ADMIN)).toBe('/admin');
      expect(getRedirectPath(UserRole.DEALERSHIP)).toBe('/painel');
      expect(getRedirectPath(UserRole.CUSTOMER)).toBe('/');
    });

    it('should check if user has specific role', () => {
      const { hasRole } = useAuthStore.getState();
      
      expect(hasRole(UserRole.DEALERSHIP)).toBe(true);
      expect(hasRole(UserRole.ADMIN)).toBe(false);
      expect(hasRole(UserRole.CUSTOMER)).toBe(false);
    });

    it('should check role-specific methods', () => {
      const { isAdmin, isDealership, isCustomer } = useAuthStore.getState();
      
      expect(isAdmin()).toBe(false);
      expect(isDealership()).toBe(true);
      expect(isCustomer()).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should clear error', () => {
      useAuthStore.setState({ error: 'Some error' });

      const { clearError } = useAuthStore.getState();
      clearError();

      expect(useAuthStore.getState().error).toBeNull();
    });

    it('should set loading state', () => {
      const { setLoading } = useAuthStore.getState();
      
      setLoading(true);
      expect(useAuthStore.getState().isLoading).toBe(true);
      
      setLoading(false);
      expect(useAuthStore.getState().isLoading).toBe(false);
    });
  });
});