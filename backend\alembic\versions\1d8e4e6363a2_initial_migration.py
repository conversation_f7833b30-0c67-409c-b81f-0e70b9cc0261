"""Initial migration

Revision ID: 1d8e4e6363a2
Revises: 
Create Date: 2025-07-25 16:10:48.499730

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision: str = '1d8e4e6363a2'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dealerships',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('trading_name', sa.String(length=255), nullable=True),
    sa.Column('cnpj', sa.String(length=18), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('whatsapp', sa.String(length=20), nullable=True),
    sa.Column('address', sa.String(length=500), nullable=True),
    sa.Column('neighborhood', sa.String(length=100), nullable=True),
    sa.Column('city', sa.String(length=100), nullable=True),
    sa.Column('state', sa.String(length=2), nullable=True),
    sa.Column('zip_code', sa.String(length=10), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('responsible', sa.String(length=255), nullable=True),
    sa.Column('subscription_id', sa.String(length=100), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.String(36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_dealership_active', 'dealerships', ['is_active'], unique=False)
    op.create_index('idx_dealership_city_state', 'dealerships', ['city', 'state'], unique=False)
    op.create_index('idx_dealership_name', 'dealerships', ['name'], unique=False)
    op.create_index(op.f('ix_dealerships_cnpj'), 'dealerships', ['cnpj'], unique=True)
    op.create_index(op.f('ix_dealerships_email'), 'dealerships', ['email'], unique=True)
    op.create_index(op.f('ix_dealerships_id'), 'dealerships', ['id'], unique=False)
    op.create_index(op.f('ix_dealerships_is_active'), 'dealerships', ['is_active'], unique=False)
    op.create_index(op.f('ix_dealerships_subscription_id'), 'dealerships', ['subscription_id'], unique=False)
    op.create_table('parts',
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('subcategory', sa.String(length=100), nullable=True),
    sa.Column('brand', sa.String(length=50), nullable=True),
    sa.Column('model', sa.String(length=100), nullable=True),
    sa.Column('year', sa.Integer(), nullable=True),
    sa.Column('year_start', sa.Integer(), nullable=True),
    sa.Column('year_end', sa.Integer(), nullable=True),
    sa.Column('oem_code', sa.String(length=100), nullable=True),
    sa.Column('alternative_codes', sa.Text(), nullable=True),
    sa.Column('weight', sa.String(length=20), nullable=True),
    sa.Column('dimensions', sa.String(length=100), nullable=True),
    sa.Column('id', sa.String(36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_part_brand_model', 'parts', ['brand', 'model'], unique=False)
    op.create_index('idx_part_category', 'parts', ['category'], unique=False)
    op.create_index('idx_part_code', 'parts', ['code'], unique=False)
    op.create_index('idx_part_name', 'parts', ['name'], unique=False)
    op.create_index('idx_part_oem_code', 'parts', ['oem_code'], unique=False)
    op.create_index('idx_part_year', 'parts', ['year'], unique=False)
    op.create_index(op.f('ix_parts_brand'), 'parts', ['brand'], unique=False)
    op.create_index(op.f('ix_parts_category'), 'parts', ['category'], unique=False)
    op.create_index(op.f('ix_parts_code'), 'parts', ['code'], unique=False)
    op.create_index(op.f('ix_parts_id'), 'parts', ['id'], unique=False)
    op.create_index(op.f('ix_parts_model'), 'parts', ['model'], unique=False)
    op.create_index(op.f('ix_parts_oem_code'), 'parts', ['oem_code'], unique=False)
    op.create_index(op.f('ix_parts_year'), 'parts', ['year'], unique=False)
    op.create_table('inventories',
    sa.Column('dealership_id', sa.String(36), nullable=False),
    sa.Column('part_id', sa.String(36), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('minimum_stock', sa.Integer(), nullable=True),
    sa.Column('price', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('cost', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('location', sa.String(length=100), nullable=True),
    sa.Column('condition', sa.String(length=20), nullable=False),
    sa.Column('warranty_months', sa.Integer(), nullable=True),
    sa.Column('is_available', sa.Boolean(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.Column('notes', sa.String(length=500), nullable=True),
    sa.Column('supplier', sa.String(length=255), nullable=True),
    sa.Column('id', sa.String(36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['dealership_id'], ['dealerships.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['part_id'], ['parts.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_inventory_available', 'inventories', ['is_available'], unique=False)
    op.create_index('idx_inventory_dealership', 'inventories', ['dealership_id'], unique=False)
    op.create_index('idx_inventory_dealership_available', 'inventories', ['dealership_id', 'is_available'], unique=False)
    op.create_index('idx_inventory_last_updated', 'inventories', ['last_updated'], unique=False)
    op.create_index('idx_inventory_part', 'inventories', ['part_id'], unique=False)
    op.create_index('idx_inventory_part_available', 'inventories', ['part_id', 'is_available'], unique=False)
    op.create_index('idx_inventory_price', 'inventories', ['price'], unique=False)
    op.create_index('idx_inventory_quantity', 'inventories', ['quantity'], unique=False)
    op.create_index('idx_inventory_search', 'inventories', ['dealership_id', 'part_id'], unique=False)
    op.create_index(op.f('ix_inventories_dealership_id'), 'inventories', ['dealership_id'], unique=False)
    op.create_index(op.f('ix_inventories_id'), 'inventories', ['id'], unique=False)
    op.create_index(op.f('ix_inventories_is_available'), 'inventories', ['is_available'], unique=False)
    op.create_index(op.f('ix_inventories_part_id'), 'inventories', ['part_id'], unique=False)
    op.create_table('subscriptions',
    sa.Column('dealership_id', sa.String(36), nullable=False),
    sa.Column('asaas_subscription_id', sa.String(length=100), nullable=False),
    sa.Column('asaas_customer_id', sa.String(length=100), nullable=False),
    sa.Column('plan_type', sa.Enum('BASIC', 'PREMIUM', 'ENTERPRISE', 'TRIAL', name='plantype'), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'PENDING', 'CANCELLED', 'OVERDUE', 'SUSPENDED', name='subscriptionstatus'), nullable=False),
    sa.Column('value', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('next_due_date', sa.Date(), nullable=True),
    sa.Column('billing_type', sa.String(length=20), nullable=False),
    sa.Column('cycle', sa.String(length=20), nullable=False),
    sa.Column('description', sa.String(length=500), nullable=True),
    sa.Column('external_reference', sa.String(length=100), nullable=True),
    sa.Column('discount_value', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('interest_value', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('id', sa.String(36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['dealership_id'], ['dealerships.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_subscription_active', 'subscriptions', ['dealership_id', 'status'], unique=False)
    op.create_index('idx_subscription_asaas_id', 'subscriptions', ['asaas_subscription_id'], unique=False)
    op.create_index('idx_subscription_dealership', 'subscriptions', ['dealership_id'], unique=False)
    op.create_index('idx_subscription_due_date', 'subscriptions', ['next_due_date'], unique=False)
    op.create_index('idx_subscription_plan', 'subscriptions', ['plan_type'], unique=False)
    op.create_index('idx_subscription_status', 'subscriptions', ['status'], unique=False)
    op.create_index(op.f('ix_subscriptions_asaas_customer_id'), 'subscriptions', ['asaas_customer_id'], unique=False)
    op.create_index(op.f('ix_subscriptions_asaas_subscription_id'), 'subscriptions', ['asaas_subscription_id'], unique=True)
    op.create_index(op.f('ix_subscriptions_dealership_id'), 'subscriptions', ['dealership_id'], unique=False)
    op.create_index(op.f('ix_subscriptions_id'), 'subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_subscriptions_next_due_date'), 'subscriptions', ['next_due_date'], unique=False)
    op.create_index(op.f('ix_subscriptions_plan_type'), 'subscriptions', ['plan_type'], unique=False)
    op.create_index(op.f('ix_subscriptions_status'), 'subscriptions', ['status'], unique=False)
    op.create_table('users',
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=255), nullable=False),
    sa.Column('role', sa.Enum('ADMIN', 'DEALERSHIP', 'CUSTOMER', name='userrole'), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION', name='userstatus'), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('dealership_id', sa.String(36), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('login_attempts', sa.String(length=10), nullable=False),
    sa.Column('password_reset_token', sa.String(length=255), nullable=True),
    sa.Column('password_reset_expires', sa.DateTime(), nullable=True),
    sa.Column('email_verified', sa.Boolean(), nullable=False),
    sa.Column('email_verification_token', sa.String(length=255), nullable=True),
    sa.Column('api_key', sa.String(length=255), nullable=True),
    sa.Column('id', sa.String(36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['dealership_id'], ['dealerships.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('api_key')
    )
    op.create_index('idx_user_active', 'users', ['status', 'role'], unique=False)
    op.create_index('idx_user_api_key', 'users', ['api_key'], unique=False)
    op.create_index('idx_user_dealership', 'users', ['dealership_id'], unique=False)
    op.create_index('idx_user_email', 'users', ['email'], unique=False)
    op.create_index('idx_user_role', 'users', ['role'], unique=False)
    op.create_index('idx_user_status', 'users', ['status'], unique=False)
    op.create_index(op.f('ix_users_dealership_id'), 'users', ['dealership_id'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_role'), 'users', ['role'], unique=False)
    op.create_index(op.f('ix_users_status'), 'users', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_status'), table_name='users')
    op.drop_index(op.f('ix_users_role'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_dealership_id'), table_name='users')
    op.drop_index('idx_user_status', table_name='users')
    op.drop_index('idx_user_role', table_name='users')
    op.drop_index('idx_user_email', table_name='users')
    op.drop_index('idx_user_dealership', table_name='users')
    op.drop_index('idx_user_api_key', table_name='users')
    op.drop_index('idx_user_active', table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_subscriptions_status'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_plan_type'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_next_due_date'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_dealership_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_asaas_subscription_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_asaas_customer_id'), table_name='subscriptions')
    op.drop_index('idx_subscription_status', table_name='subscriptions')
    op.drop_index('idx_subscription_plan', table_name='subscriptions')
    op.drop_index('idx_subscription_due_date', table_name='subscriptions')
    op.drop_index('idx_subscription_dealership', table_name='subscriptions')
    op.drop_index('idx_subscription_asaas_id', table_name='subscriptions')
    op.drop_index('idx_subscription_active', table_name='subscriptions')
    op.drop_table('subscriptions')
    op.drop_index(op.f('ix_inventories_part_id'), table_name='inventories')
    op.drop_index(op.f('ix_inventories_is_available'), table_name='inventories')
    op.drop_index(op.f('ix_inventories_id'), table_name='inventories')
    op.drop_index(op.f('ix_inventories_dealership_id'), table_name='inventories')
    op.drop_index('idx_inventory_search', table_name='inventories')
    op.drop_index('idx_inventory_quantity', table_name='inventories')
    op.drop_index('idx_inventory_price', table_name='inventories')
    op.drop_index('idx_inventory_part_available', table_name='inventories')
    op.drop_index('idx_inventory_part', table_name='inventories')
    op.drop_index('idx_inventory_last_updated', table_name='inventories')
    op.drop_index('idx_inventory_dealership_available', table_name='inventories')
    op.drop_index('idx_inventory_dealership', table_name='inventories')
    op.drop_index('idx_inventory_available', table_name='inventories')
    op.drop_table('inventories')
    op.drop_index(op.f('ix_parts_year'), table_name='parts')
    op.drop_index(op.f('ix_parts_oem_code'), table_name='parts')
    op.drop_index(op.f('ix_parts_model'), table_name='parts')
    op.drop_index(op.f('ix_parts_id'), table_name='parts')
    op.drop_index(op.f('ix_parts_code'), table_name='parts')
    op.drop_index(op.f('ix_parts_category'), table_name='parts')
    op.drop_index(op.f('ix_parts_brand'), table_name='parts')
    op.drop_index('idx_part_year', table_name='parts')
    op.drop_index('idx_part_oem_code', table_name='parts')
    op.drop_index('idx_part_name', table_name='parts')
    op.drop_index('idx_part_code', table_name='parts')
    op.drop_index('idx_part_category', table_name='parts')
    op.drop_index('idx_part_brand_model', table_name='parts')
    op.drop_table('parts')
    op.drop_index(op.f('ix_dealerships_subscription_id'), table_name='dealerships')
    op.drop_index(op.f('ix_dealerships_is_active'), table_name='dealerships')
    op.drop_index(op.f('ix_dealerships_id'), table_name='dealerships')
    op.drop_index(op.f('ix_dealerships_email'), table_name='dealerships')
    op.drop_index(op.f('ix_dealerships_cnpj'), table_name='dealerships')
    op.drop_index('idx_dealership_name', table_name='dealerships')
    op.drop_index('idx_dealership_city_state', table_name='dealerships')
    op.drop_index('idx_dealership_active', table_name='dealerships')
    op.drop_table('dealerships')
    # ### end Alembic commands ###
