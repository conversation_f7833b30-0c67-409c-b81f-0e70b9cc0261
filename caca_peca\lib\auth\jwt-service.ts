/**
 * JWT Authentication Service
 * Enhanced JWT token management with automatic refresh and secure storage
 */

import Cookies from 'js-cookie';
import { jwtDecode } from 'jwt-decode';

// Token storage configuration
const TOKEN_CONFIG = {
  ACCESS_TOKEN_KEY: 'autoparts_access_token',
  REFRESH_TOKEN_KEY: 'autoparts_refresh_token',
  ACCESS_TOKEN_EXPIRY: 1/24, // 1 hour in days
  REFRESH_TOKEN_EXPIRY: 7, // 7 days
  REFRESH_THRESHOLD: 5 * 60 * 1000, // Refresh 5 minutes before expiry
};

interface JWTPayload {
  sub: string;
  email: string;
  role: string;
  exp: number;
  iat: number;
  jti?: string;
}

export class JWTService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private refreshPromise: Promise<string> | null = null;

  constructor() {
    this.loadTokensFromStorage();
  }

  /**
   * Load tokens from secure storage (cookies)
   */
  private loadTokensFromStorage(): void {
    if (typeof window !== 'undefined') {
      this.accessToken = Cookies.get(TOKEN_CONFIG.ACCESS_TOKEN_KEY) || null;
      this.refreshToken = Cookies.get(TOKEN_CONFIG.REFRESH_TOKEN_KEY) || null;
    }
  }

  /**
   * Save tokens to secure storage with appropriate security settings
   */
  setTokens(accessToken: string, refreshToken: string): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    
    if (typeof window !== 'undefined') {
      const isProduction = process.env.NODE_ENV === 'production';
      
      // Set access token with shorter expiry
      Cookies.set(TOKEN_CONFIG.ACCESS_TOKEN_KEY, accessToken, {
        expires: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY,
        secure: isProduction,
        sameSite: 'strict',
        httpOnly: false, // Need to access from JS for API calls
      });
      
      // Set refresh token with longer expiry
      Cookies.set(TOKEN_CONFIG.REFRESH_TOKEN_KEY, refreshToken, {
        expires: TOKEN_CONFIG.REFRESH_TOKEN_EXPIRY,
        secure: isProduction,
        sameSite: 'strict',
        httpOnly: false, // Need to access from JS for refresh calls
      });
    }
  }

  /**
   * Clear all tokens from storage
   */
  clearTokens(): void {
    this.accessToken = null;
    this.refreshToken = null;
    
    if (typeof window !== 'undefined') {
      Cookies.remove(TOKEN_CONFIG.ACCESS_TOKEN_KEY);
      Cookies.remove(TOKEN_CONFIG.REFRESH_TOKEN_KEY);
    }
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return this.refreshToken;
  }

  /**
   * Check if user is authenticated (has valid tokens)
   */
  isAuthenticated(): boolean {
    return !!this.accessToken && !!this.refreshToken && !this.isTokenExpired(this.accessToken);
  }

  /**
   * Check if a token is expired
   */
  private isTokenExpired(token: string): boolean {
    try {
      const decoded = jwtDecode<JWTPayload>(token);
      const currentTime = Date.now() / 1000;
      return decoded.exp < currentTime;
    } catch (error) {
      return true; // Consider invalid tokens as expired
    }
  }

  /**
   * Check if access token needs refresh (expires within threshold)
   */
  shouldRefreshToken(): boolean {
    if (!this.accessToken) return false;
    
    try {
      const decoded = jwtDecode<JWTPayload>(this.accessToken);
      const currentTime = Date.now() / 1000;
      const timeUntilExpiry = (decoded.exp - currentTime) * 1000;
      
      return timeUntilExpiry < TOKEN_CONFIG.REFRESH_THRESHOLD;
    } catch (error) {
      return true; // Refresh if token is invalid
    }
  }

  /**
   * Get user information from access token
   */
  getTokenPayload(): JWTPayload | null {
    if (!this.accessToken) return null;
    
    try {
      return jwtDecode<JWTPayload>(this.accessToken);
    } catch (error) {
      console.error('Failed to decode token:', error);
      return null;
    }
  }

  /**
   * Get user ID from token
   */
  getUserId(): string | null {
    const payload = this.getTokenPayload();
    return payload?.sub || null;
  }

  /**
   * Get user email from token
   */
  getUserEmail(): string | null {
    const payload = this.getTokenPayload();
    return payload?.email || null;
  }

  /**
   * Get user role from token
   */
  getUserRole(): string | null {
    const payload = this.getTokenPayload();
    return payload?.role || null;
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(): Date | null {
    const payload = this.getTokenPayload();
    if (!payload) return null;
    
    return new Date(payload.exp * 1000);
  }

  /**
   * Get time until token expires (in milliseconds)
   */
  getTimeUntilExpiry(): number | null {
    const expiration = this.getTokenExpiration();
    if (!expiration) return null;
    
    return expiration.getTime() - Date.now();
  }

  /**
   * Refresh access token using refresh token
   * Returns a promise that resolves to the new access token
   */
  async refreshAccessToken(): Promise<string> {
    // Prevent multiple simultaneous refresh requests
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performTokenRefresh();
    
    try {
      const newAccessToken = await this.refreshPromise;
      return newAccessToken;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh API call
   */
  private async performTokenRefresh(): Promise<string> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh_token: this.refreshToken,
      }),
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    
    // Update access token
    this.accessToken = data.access_token;
    
    if (typeof window !== 'undefined') {
      const isProduction = process.env.NODE_ENV === 'production';
      
      Cookies.set(TOKEN_CONFIG.ACCESS_TOKEN_KEY, this.accessToken, {
        expires: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY,
        secure: isProduction,
        sameSite: 'strict',
        httpOnly: false,
      });
    }

    return this.accessToken;
  }

  /**
   * Schedule automatic token refresh
   */
  scheduleTokenRefresh(): void {
    const timeUntilExpiry = this.getTimeUntilExpiry();
    
    if (!timeUntilExpiry) return;
    
    // Schedule refresh 5 minutes before expiry
    const refreshTime = Math.max(timeUntilExpiry - TOKEN_CONFIG.REFRESH_THRESHOLD, 0);
    
    setTimeout(async () => {
      try {
        await this.refreshAccessToken();
        // Schedule next refresh
        this.scheduleTokenRefresh();
      } catch (error) {
        console.error('Scheduled token refresh failed:', error);
        // Clear tokens if refresh fails
        this.clearTokens();
      }
    }, refreshTime);
  }

  /**
   * Validate token format and structure
   */
  validateToken(token: string): boolean {
    try {
      const decoded = jwtDecode<JWTPayload>(token);
      
      // Check required fields
      return !!(
        decoded.sub &&
        decoded.email &&
        decoded.role &&
        decoded.exp &&
        decoded.iat
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Get authorization header value
   */
  getAuthorizationHeader(): string | null {
    if (!this.accessToken) return null;
    return `Bearer ${this.accessToken}`;
  }

  /**
   * Check if current user has specific role
   */
  hasRole(role: string): boolean {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  /**
   * Check if current user is admin
   */
  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  /**
   * Check if current user is dealership
   */
  isDealership(): boolean {
    return this.hasRole('dealership');
  }

  /**
   * Check if current user is customer
   */
  isCustomer(): boolean {
    return this.hasRole('customer');
  }
}

// Export singleton instance
export const jwtService = new JWTService();
export default jwtService;