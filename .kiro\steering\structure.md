# Project Structure

## Root Directory Organization
```
caca_peca/
├── app/                    # Next.js App Router pages
├── components/             # React components
├── lib/                    # Utility functions
├── hooks/                  # Custom React hooks
├── public/                 # Static assets
└── node_modules/           # Dependencies
```

## App Router Structure (`/app`)
- **Route-based organization** following Next.js 13+ App Router conventions
- Each folder represents a route segment
- Special files: `layout.tsx`, `page.tsx`, `loading.tsx`

### Key Routes
- `/` - Homepage with search and dealership info
- `/admin/` - Admin panel
- `/auth/` - Authentication pages
- `/busca/` - Search results
- `/cadastro-concessionaria/` - Dealership registration
- `/concessionarias/` - Dealership listings
- `/contato/` - Contact page
- `/painel/` - User dashboard
- `/para-concessionarias/` - Dealership information

## Component Architecture (`/components`)

### Component Categories
- **UI Components** (`/components/ui/`) - shadcn/ui base components
- **Feature Components** - Business logic components (search-bar, product-card, etc.)
- **Layout Components** - header, footer, sidebar
- **Theme Components** - theme toggles and providers

### Naming Conventions
- Use kebab-case for component files (`product-card.tsx`)
- Use PascalCase for component names (`ProductCard`)
- Prefix UI components with descriptive names

## Utilities & Hooks

### `/lib` Directory
- `utils.ts` - Common utility functions (cn helper for className merging)
- `analytics.ts` - Analytics utilities

### `/hooks` Directory
- Custom React hooks with `use-` prefix
- `use-mobile.tsx` - Mobile detection
- `use-theme-effect.tsx` - Theme-related effects

## Styling Architecture

### Tailwind Configuration
- Custom color system with CSS variables
- Dark mode support with `class` strategy
- Extended theme with custom colors for branding
- Component-specific animations

### CSS Variables Pattern
- Use HSL color format: `hsl(var(--primary))`
- Semantic color naming (primary, secondary, muted, etc.)
- Application-specific colors (header-bg, footer-bg, etc.)

## File Naming Conventions
- **Components**: kebab-case (`.tsx` extension)
- **Pages**: kebab-case following route structure
- **Utilities**: kebab-case (`.ts` extension)
- **Hooks**: `use-` prefix with kebab-case

## Import Patterns
- Use path aliases (`@/components`, `@/lib`, `@/hooks`)
- Group imports: React/Next.js first, then third-party, then local
- Use named imports for better tree-shaking