/**
 * Authentication Utilities Unit Tests
 * Tests for authentication helper functions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { jwtService } from '../../lib/auth/jwt-service';
import { UserRole } from '../../lib/types/api';
import {
  isAuthenticated,
  getCurrentUserRole,
  getCurrentUserId,
  getCurrentUserEmail,
  hasRole,
  isAdmin,
  isDealership,
  isCustomer,
  getRedirectPath,
  getCurrentUserRedirectPath,
  canAccessRoute,
  formatUserDisplayName,
  getUserInitials,
  isTokenExpiringSoon,
  getTokenExpirationInfo,
  isValidEmail,
  validatePassword,
  generateSecurePassword,
  clearAuthData,
} from '../../lib/auth/utils';

// Mock JWT service
vi.mock('../../lib/auth/jwt-service');
const mockJwtService = vi.mocked(jwtService);

describe('Authentication Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Authentication Status', () => {
    it('should check if user is authenticated', () => {
      mockJwtService.isAuthenticated.mockReturnValue(true);
      expect(isAuthenticated()).toBe(true);

      mockJwtService.isAuthenticated.mockReturnValue(false);
      expect(isAuthenticated()).toBe(false);
    });
  });

  describe('User Information', () => {
    it('should get current user role', () => {
      mockJwtService.getUserRole.mockReturnValue('dealership');
      expect(getCurrentUserRole()).toBe('dealership');
    });

    it('should get current user ID', () => {
      mockJwtService.getUserId.mockReturnValue('user123');
      expect(getCurrentUserId()).toBe('user123');
    });

    it('should get current user email', () => {
      mockJwtService.getUserEmail.mockReturnValue('<EMAIL>');
      expect(getCurrentUserEmail()).toBe('<EMAIL>');
    });
  });

  describe('Role Checking', () => {
    beforeEach(() => {
      mockJwtService.getUserRole.mockReturnValue('dealership');
    });

    it('should check if user has specific role', () => {
      expect(hasRole(UserRole.DEALERSHIP)).toBe(true);
      expect(hasRole(UserRole.ADMIN)).toBe(false);
    });

    it('should check if user is admin', () => {
      mockJwtService.getUserRole.mockReturnValue('admin');
      expect(isAdmin()).toBe(true);

      mockJwtService.getUserRole.mockReturnValue('dealership');
      expect(isAdmin()).toBe(false);
    });

    it('should check if user is dealership', () => {
      expect(isDealership()).toBe(true);

      mockJwtService.getUserRole.mockReturnValue('admin');
      expect(isDealership()).toBe(false);
    });

    it('should check if user is customer', () => {
      expect(isCustomer()).toBe(false);

      mockJwtService.getUserRole.mockReturnValue('customer');
      expect(isCustomer()).toBe(true);
    });
  });

  describe('Redirect Paths', () => {
    it('should get correct redirect path for each role', () => {
      expect(getRedirectPath(UserRole.ADMIN)).toBe('/admin');
      expect(getRedirectPath(UserRole.DEALERSHIP)).toBe('/painel');
      expect(getRedirectPath(UserRole.CUSTOMER)).toBe('/');
    });

    it('should get current user redirect path', () => {
      mockJwtService.getUserRole.mockReturnValue('dealership');
      expect(getCurrentUserRedirectPath()).toBe('/painel');

      mockJwtService.getUserRole.mockReturnValue(null);
      expect(getCurrentUserRedirectPath()).toBe('/');
    });
  });

  describe('Route Access Control', () => {
    beforeEach(() => {
      mockJwtService.isAuthenticated.mockReturnValue(true);
    });

    it('should allow admin access to admin routes', () => {
      expect(canAccessRoute('/admin/dashboard', 'admin')).toBe(true);
      expect(canAccessRoute('/admin/users', 'admin')).toBe(true);
    });

    it('should deny non-admin access to admin routes', () => {
      expect(canAccessRoute('/admin/dashboard', 'dealership')).toBe(false);
      expect(canAccessRoute('/admin/users', 'customer')).toBe(false);
    });

    it('should allow dealership and admin access to dealership routes', () => {
      expect(canAccessRoute('/painel', 'dealership')).toBe(true);
      expect(canAccessRoute('/dashboard', 'dealership')).toBe(true);
      expect(canAccessRoute('/painel', 'admin')).toBe(true);
    });

    it('should allow all authenticated users access to public routes', () => {
      expect(canAccessRoute('/', 'customer')).toBe(true);
      expect(canAccessRoute('/busca', 'dealership')).toBe(true);
      expect(canAccessRoute('/contato', 'admin')).toBe(true);
    });

    it('should use current user role when no role provided', () => {
      mockJwtService.getUserRole.mockReturnValue('admin');
      expect(canAccessRoute('/admin/dashboard')).toBe(true);
    });

    it('should deny access when not authenticated', () => {
      expect(canAccessRoute('/admin/dashboard', null)).toBe(false);
    });
  });

  describe('User Display Utilities', () => {
    it('should format user display name with full name', () => {
      const user = {
        full_name: 'John Doe',
        email: '<EMAIL>',
      };

      expect(formatUserDisplayName(user)).toBe('John Doe');
    });

    it('should format user display name without full name', () => {
      const user = {
        email: '<EMAIL>',
      };

      expect(formatUserDisplayName(user)).toBe('john');
    });

    it('should get user initials from full name', () => {
      const user = {
        full_name: 'John Doe Smith',
        email: '<EMAIL>',
      };

      expect(getUserInitials(user)).toBe('JD');
    });

    it('should get user initials from email when no full name', () => {
      const user = {
        email: '<EMAIL>',
      };

      expect(getUserInitials(user)).toBe('J');
    });
  });

  describe('Token Expiration', () => {
    it('should check if token is expiring soon', () => {
      mockJwtService.getTimeUntilExpiry.mockReturnValue(3 * 60 * 1000); // 3 minutes
      expect(isTokenExpiringSoon(5)).toBe(true);

      mockJwtService.getTimeUntilExpiry.mockReturnValue(10 * 60 * 1000); // 10 minutes
      expect(isTokenExpiringSoon(5)).toBe(false);
    });

    it('should get token expiration info', () => {
      const mockExpiration = new Date('2024-01-01T12:00:00Z');
      const timeUntilExpiry = 30 * 60 * 1000; // 30 minutes

      mockJwtService.getTokenExpiration.mockReturnValue(mockExpiration);
      mockJwtService.getTimeUntilExpiry.mockReturnValue(timeUntilExpiry);

      const info = getTokenExpirationInfo();

      expect(info).toEqual({
        expiration: mockExpiration,
        timeUntilExpiry,
        minutes: 30,
        hours: 0,
        days: 0,
        isExpiringSoon: false,
        formattedTimeLeft: '30 minutos',
      });
    });

    it('should return null when no token expiration info', () => {
      mockJwtService.getTokenExpiration.mockReturnValue(null);
      mockJwtService.getTimeUntilExpiry.mockReturnValue(null);

      expect(getTokenExpirationInfo()).toBeNull();
    });
  });

  describe('Email Validation', () => {
    it('should validate correct email formats', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email formats', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('test.example.com')).toBe(false);
    });
  });

  describe('Password Validation', () => {
    it('should validate strong password', () => {
      const result = validatePassword('StrongPass123!');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.strength).toBe('strong');
    });

    it('should validate medium password', () => {
      const result = validatePassword('GoodPass123');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('A senha deve conter pelo menos um caractere especial');
      expect(result.strength).toBe('medium');
    });

    it('should validate weak password', () => {
      const result = validatePassword('weak');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('A senha deve ter pelo menos 8 caracteres');
      expect(result.errors).toContain('A senha deve conter pelo menos uma letra maiúscula');
      expect(result.errors).toContain('A senha deve conter pelo menos um número');
      expect(result.errors).toContain('A senha deve conter pelo menos um caractere especial');
      expect(result.strength).toBe('weak');
    });
  });

  describe('Password Generation', () => {
    it('should generate secure password with default length', () => {
      const password = generateSecurePassword();
      
      expect(password).toHaveLength(12);
      expect(validatePassword(password).isValid).toBe(true);
    });

    it('should generate secure password with custom length', () => {
      const password = generateSecurePassword(16);
      
      expect(password).toHaveLength(16);
      expect(validatePassword(password).isValid).toBe(true);
    });

    it('should generate different passwords each time', () => {
      const password1 = generateSecurePassword();
      const password2 = generateSecurePassword();
      
      expect(password1).not.toBe(password2);
    });
  });

  describe('Auth Data Cleanup', () => {
    it('should clear all auth data', () => {
      clearAuthData();

      expect(mockJwtService.clearTokens).toHaveBeenCalled();
      expect(window.localStorage.removeItem).toHaveBeenCalledWith('autoparts-auth-storage');
    });
  });
});