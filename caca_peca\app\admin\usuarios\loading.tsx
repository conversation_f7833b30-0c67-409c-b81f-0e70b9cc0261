import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function UsersLoading() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <Skeleton className="h-8 w-[250px]" />
          <Skeleton className="mt-2 h-4 w-[350px]" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-[120px]" />
          <Skeleton className="h-10 w-[150px]" />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-[100px]" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-[350px]" />
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <Skeleton className="h-10 flex-1" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-[180px]" />
              <Skeleton className="h-10 w-[180px]" />
            </div>
          </div>

          <div className="rounded-md border">
            <div className="p-4">
              <div className="flex items-center border-b pb-4">
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="ml-auto h-4 w-[150px]" />
                <Skeleton className="ml-4 h-4 w-[100px]" />
                <Skeleton className="ml-4 h-4 w-[100px]" />
                <Skeleton className="ml-4 h-4 w-[100px]" />
                <Skeleton className="ml-4 h-4 w-[50px]" />
              </div>
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center py-4">
                  <Skeleton className="h-4 w-[150px]" />
                  <Skeleton className="ml-auto h-4 w-[150px]" />
                  <Skeleton className="ml-4 h-4 w-[100px]" />
                  <Skeleton className="ml-4 h-4 w-[100px]" />
                  <Skeleton className="ml-4 h-4 w-[100px]" />
                  <Skeleton className="ml-4 h-4 w-[50px]" />
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-8 w-[300px]" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

