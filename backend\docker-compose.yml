version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: autoparts_postgres
    environment:
      POSTGRES_USER: autoparts
      POSTGRES_PASSWORD: autoparts123
      POSTGRES_DB: autoparts
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autoparts"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: autoparts_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  api:
    build: .
    container_name: autoparts_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************************/autoparts
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=True
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_data:
  redis_data:
