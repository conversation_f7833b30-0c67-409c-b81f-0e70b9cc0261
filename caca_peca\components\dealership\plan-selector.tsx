"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { 
  Check, 
  Star, 
  Zap, 
  Crown,
  Loader2,
  CreditCard,
  FileText,
  Smartphone
} from "lucide-react"
import { useSubscriptionPlans, useCreateSubscription } from "@/lib/hooks/useSubscription"
import { SubscriptionPlan, BillingType, BillingCycle } from "@/lib/types/api"
import { formatCurrency } from "@/lib/utils"

interface PlanSelectorProps {
  onPlanSelected?: (planType: SubscriptionPlan) => void
}

export function PlanSelector({ onPlanSelected }: PlanSelectorProps) {
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [billingCycle, setBillingCycle] = useState<BillingCycle>(BillingCycle.MONTHLY)
  const [billingType, setBillingType] = useState<BillingType>(BillingType.CREDIT_CARD)
  const [isYearly, setIsYearly] = useState(false)

  const { data: plansResponse, isLoading: plansLoading } = useSubscriptionPlans()
  const createSubscriptionMutation = useCreateSubscription()

  const plans = plansResponse?.data?.success ? plansResponse.data.data : []

  // Default plans if API doesn't return them
  const defaultPlans = [
    {
      type: SubscriptionPlan.BASIC,
      name: "Básico",
      description: "Ideal para pequenas concessionárias",
      features: [
        "Até 1.000 peças no catálogo",
        "Busca básica",
        "Suporte por email",
        "Dashboard básico",
        "1 usuário"
      ],
      monthly_price: 99.90,
      yearly_price: 999.00,
      popular: false,
      recommended: false
    },
    {
      type: SubscriptionPlan.PREMIUM,
      name: "Premium",
      description: "Para concessionárias em crescimento",
      features: [
        "Até 10.000 peças no catálogo",
        "Busca avançada com filtros",
        "Comparação de peças",
        "Verificador de compatibilidade",
        "Suporte prioritário",
        "Dashboard avançado",
        "Até 5 usuários",
        "API de integração",
        "Relatórios detalhados"
      ],
      monthly_price: 199.90,
      yearly_price: 1999.00,
      popular: true,
      recommended: true
    },
    {
      type: SubscriptionPlan.ENTERPRISE,
      name: "Enterprise",
      description: "Para grandes concessionárias",
      features: [
        "Peças ilimitadas no catálogo",
        "Todas as funcionalidades Premium",
        "Integração personalizada",
        "Suporte 24/7",
        "Gerente de conta dedicado",
        "Dashboard personalizado",
        "Usuários ilimitados",
        "White-label disponível",
        "SLA garantido",
        "Backup e recuperação"
      ],
      monthly_price: 499.90,
      yearly_price: 4999.00,
      popular: false,
      recommended: false
    }
  ]

  const displayPlans = plans.length > 0 ? plans : defaultPlans

  const handlePlanSelect = (planType: SubscriptionPlan) => {
    setSelectedPlan(planType)
    onPlanSelected?.(planType)
  }

  const handleSubscribe = async () => {
    if (!selectedPlan) return

    const selectedPlanData = displayPlans.find(p => p.type === selectedPlan)
    if (!selectedPlanData) return

    const price = isYearly ? selectedPlanData.yearly_price : selectedPlanData.monthly_price

    try {
      await createSubscriptionMutation.mutateAsync({
        plan_type: selectedPlan,
        billing_type: billingType,
        cycle: isYearly ? BillingCycle.YEARLY : BillingCycle.MONTHLY,
        value: price,
        description: `Assinatura ${selectedPlanData.name} - ${isYearly ? 'Anual' : 'Mensal'}`
      })
    } catch (error) {
      console.error('Error creating subscription:', error)
    }
  }

  const getPlanIcon = (planType: SubscriptionPlan) => {
    switch (planType) {
      case SubscriptionPlan.BASIC:
        return <FileText className="h-6 w-6" />
      case SubscriptionPlan.PREMIUM:
        return <Zap className="h-6 w-6" />
      case SubscriptionPlan.ENTERPRISE:
        return <Crown className="h-6 w-6" />
      default:
        return <FileText className="h-6 w-6" />
    }
  }

  const getYearlyDiscount = (monthlyPrice: number, yearlyPrice: number) => {
    const yearlyMonthly = yearlyPrice / 12
    const discount = ((monthlyPrice - yearlyMonthly) / monthlyPrice) * 100
    return Math.round(discount)
  }

  if (plansLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando planos...</span>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold">Escolha seu Plano</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Selecione o plano que melhor atende às necessidades da sua concessionária. 
          Você pode alterar ou cancelar a qualquer momento.
        </p>
      </div>

      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <Label htmlFor="billing-toggle" className={!isYearly ? "font-semibold" : ""}>
          Mensal
        </Label>
        <Switch
          id="billing-toggle"
          checked={isYearly}
          onCheckedChange={setIsYearly}
        />
        <Label htmlFor="billing-toggle" className={isYearly ? "font-semibold" : ""}>
          Anual
        </Label>
        {isYearly && (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            Economize até 17%
          </Badge>
        )}
      </div>

      {/* Plans Grid */}
      <div className="grid gap-6 md:grid-cols-3">
        {displayPlans.map((plan) => {
          const price = isYearly ? plan.yearly_price : plan.monthly_price
          const isSelected = selectedPlan === plan.type
          const yearlyDiscount = getYearlyDiscount(plan.monthly_price, plan.yearly_price)

          return (
            <Card 
              key={plan.type}
              className={`relative cursor-pointer transition-all hover:shadow-lg ${
                isSelected ? 'ring-2 ring-primary shadow-lg' : ''
              } ${plan.popular ? 'border-primary' : ''}`}
              onClick={() => handlePlanSelect(plan.type)}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground px-3 py-1">
                    <Star className="h-3 w-3 mr-1" />
                    Mais Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-2">
                  {getPlanIcon(plan.type)}
                </div>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <p className="text-sm text-muted-foreground">{plan.description}</p>
                
                <div className="space-y-2">
                  <div className="text-3xl font-bold">
                    {formatCurrency(price)}
                    <span className="text-sm font-normal text-muted-foreground">
                      /{isYearly ? 'ano' : 'mês'}
                    </span>
                  </div>
                  
                  {isYearly && (
                    <div className="text-sm text-muted-foreground">
                      <span className="line-through">
                        {formatCurrency(plan.monthly_price * 12)}/ano
                      </span>
                      <Badge variant="outline" className="ml-2 text-green-600">
                        -{yearlyDiscount}%
                      </Badge>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button 
                  className="w-full" 
                  variant={isSelected ? "default" : "outline"}
                  onClick={(e) => {
                    e.stopPropagation()
                    handlePlanSelect(plan.type)
                  }}
                >
                  {isSelected ? "Selecionado" : "Selecionar Plano"}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Payment Method Selection */}
      {selectedPlan && (
        <Card>
          <CardHeader>
            <CardTitle>Método de Pagamento</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  billingType === BillingType.CREDIT_CARD ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setBillingType(BillingType.CREDIT_CARD)}
              >
                <CardContent className="p-4 text-center">
                  <CreditCard className="h-8 w-8 mx-auto mb-2" />
                  <h4 className="font-semibold">Cartão de Crédito</h4>
                  <p className="text-sm text-muted-foreground">Cobrança automática</p>
                </CardContent>
              </Card>

              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  billingType === BillingType.BOLETO ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setBillingType(BillingType.BOLETO)}
              >
                <CardContent className="p-4 text-center">
                  <FileText className="h-8 w-8 mx-auto mb-2" />
                  <h4 className="font-semibold">Boleto</h4>
                  <p className="text-sm text-muted-foreground">Pagamento manual</p>
                </CardContent>
              </Card>

              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  billingType === BillingType.PIX ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setBillingType(BillingType.PIX)}
              >
                <CardContent className="p-4 text-center">
                  <Smartphone className="h-8 w-8 mx-auto mb-2" />
                  <h4 className="font-semibold">PIX</h4>
                  <p className="text-sm text-muted-foreground">Pagamento instantâneo</p>
                </CardContent>
              </Card>
            </div>

            <Separator />

            <div className="flex justify-center">
              <Button 
                size="lg" 
                onClick={handleSubscribe}
                disabled={!selectedPlan || createSubscriptionMutation.isPending}
                className="min-w-[200px]"
              >
                {createSubscriptionMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processando...
                  </>
                ) : (
                  'Assinar Agora'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
