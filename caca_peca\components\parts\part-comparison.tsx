"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  X, 
  Star, 
  Check, 
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  ShoppingCart,
  Heart,
  Share2
} from "lucide-react"
import { Part, PartComparison } from "@/lib/types/api"
import { useCompareParts } from "@/lib/hooks/useParts"
import { formatCurrency } from "@/lib/utils"

interface PartComparisonProps {
  partIds: string[]
  onRemovePart: (partId: string) => void
  onClose: () => void
}

export function PartComparisonComponent({ partIds, onRemovePart, onClose }: PartComparisonProps) {
  const { data: comparisonResponse, isLoading, error } = useCompareParts()
  const [selectedAttributes, setSelectedAttributes] = useState<string[]>([
    'price', 'brand', 'category', 'availability', 'warranty'
  ])

  // Extract comparison data
  const comparison = comparisonResponse?.data?.success ? comparisonResponse.data.data : null
  const parts = comparison?.parts || []
  const matrix = comparison?.comparison_matrix || {}
  const recommendations = comparison?.recommendations

  if (partIds.length === 0) {
    return (
      <Card className="w-full">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">Nenhuma peça selecionada</h3>
          <p className="text-muted-foreground">
            Adicione peças à comparação para ver as diferenças lado a lado
          </p>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="p-8 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p>Carregando comparação...</p>
        </CardContent>
      </Card>
    )
  }

  if (error || !comparison) {
    return (
      <Card className="w-full">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
          <h3 className="text-lg font-semibold mb-2">Erro ao carregar comparação</h3>
          <p className="text-muted-foreground">
            Não foi possível carregar os dados de comparação
          </p>
        </CardContent>
      </Card>
    )
  }

  const getComparisonIcon = (value1: any, value2: any, attribute: string) => {
    if (attribute === 'price') {
      const price1 = parseFloat(value1) || 0
      const price2 = parseFloat(value2) || 0
      if (price1 < price2) return <TrendingDown className="h-4 w-4 text-green-600" />
      if (price1 > price2) return <TrendingUp className="h-4 w-4 text-red-600" />
      return <Minus className="h-4 w-4 text-muted-foreground" />
    }
    
    if (value1 === value2) return <Minus className="h-4 w-4 text-muted-foreground" />
    return null
  }

  const getBestValueBadge = (partId: string) => {
    if (recommendations?.best_value === partId) {
      return <Badge variant="secondary" className="bg-green-100 text-green-800">Melhor Custo-Benefício</Badge>
    }
    if (recommendations?.highest_quality === partId) {
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Maior Qualidade</Badge>
    }
    if (recommendations?.most_compatible === partId) {
      return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Mais Compatível</Badge>
    }
    return null
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Comparação de Peças
            <Badge variant="outline">{parts.length} peças</Badge>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Parts Overview */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${parts.length}, 1fr)` }}>
          {parts.map((part) => (
            <Card key={part.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h4 className="font-semibold text-sm line-clamp-2">{part.name}</h4>
                    <p className="text-xs text-muted-foreground">{part.part_number}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemovePart(part.id)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                {getBestValueBadge(part.id)}
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <div className="text-lg font-bold text-primary">
                    {formatCurrency(part.price || 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {part.brand}
                  </div>
                  <div className="flex gap-1">
                    <Button size="sm" className="flex-1">
                      <ShoppingCart className="h-3 w-3 mr-1" />
                      Comprar
                    </Button>
                    <Button variant="outline" size="sm">
                      <Heart className="h-3 w-3" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Separator />

        {/* Detailed Comparison Table */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Comparação Detalhada</h3>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 font-medium">Atributo</th>
                  {parts.map((part) => (
                    <th key={part.id} className="text-left p-3 font-medium min-w-[200px]">
                      <div className="space-y-1">
                        <div className="font-semibold text-sm line-clamp-1">{part.name}</div>
                        <div className="text-xs text-muted-foreground">{part.part_number}</div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {/* Price Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Preço</td>
                  {parts.map((part, index) => (
                    <td key={part.id} className="p-3">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-primary">
                          {formatCurrency(part.price || 0)}
                        </span>
                        {index > 0 && getComparisonIcon(
                          part.price, 
                          parts[0].price, 
                          'price'
                        )}
                      </div>
                    </td>
                  ))}
                </tr>

                {/* Brand Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Marca</td>
                  {parts.map((part) => (
                    <td key={part.id} className="p-3">
                      <Badge variant="outline">{part.brand}</Badge>
                    </td>
                  ))}
                </tr>

                {/* Category Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Categoria</td>
                  {parts.map((part) => (
                    <td key={part.id} className="p-3">
                      {part.category}
                    </td>
                  ))}
                </tr>

                {/* Availability Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Disponibilidade</td>
                  {parts.map((part) => (
                    <td key={part.id} className="p-3">
                      <div className="flex items-center gap-2">
                        {part.is_available ? (
                          <>
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-green-600">Disponível</span>
                          </>
                        ) : (
                          <>
                            <X className="h-4 w-4 text-red-600" />
                            <span className="text-red-600">Indisponível</span>
                          </>
                        )}
                      </div>
                    </td>
                  ))}
                </tr>

                {/* Description Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Descrição</td>
                  {parts.map((part) => (
                    <td key={part.id} className="p-3">
                      <p className="text-sm line-clamp-3">{part.description}</p>
                    </td>
                  ))}
                </tr>

                {/* Warranty Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Garantia</td>
                  {parts.map((part) => (
                    <td key={part.id} className="p-3">
                      <span className="text-sm">
                        {matrix[part.id]?.warranty || 'Não informado'}
                      </span>
                    </td>
                  ))}
                </tr>

                {/* Rating Row */}
                <tr className="border-b hover:bg-muted/50">
                  <td className="p-3 font-medium">Avaliação</td>
                  {parts.map((part) => (
                    <td key={part.id} className="p-3">
                      <div className="flex items-center gap-1">
                        {Array.from({ length: 5 }, (_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < (matrix[part.id]?.rating || 0)
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="text-sm text-muted-foreground ml-1">
                          ({matrix[part.id]?.rating || 0}/5)
                        </span>
                      </div>
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Recommendations */}
        {recommendations && (
          <div className="space-y-4">
            <Separator />
            <h3 className="text-lg font-semibold">Recomendações</h3>
            <div className="grid gap-4 md:grid-cols-3">
              {recommendations.best_value && (
                <Card className="border-green-200 bg-green-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-800">Melhor Custo-Benefício</span>
                    </div>
                    <p className="text-sm text-green-700">
                      {parts.find(p => p.id === recommendations.best_value)?.name}
                    </p>
                  </CardContent>
                </Card>
              )}

              {recommendations.highest_quality && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Star className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-800">Maior Qualidade</span>
                    </div>
                    <p className="text-sm text-blue-700">
                      {parts.find(p => p.id === recommendations.highest_quality)?.name}
                    </p>
                  </CardContent>
                </Card>
              )}

              {recommendations.most_compatible && (
                <Card className="border-purple-200 bg-purple-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Check className="h-4 w-4 text-purple-600" />
                      <span className="font-medium text-purple-800">Mais Compatível</span>
                    </div>
                    <p className="text-sm text-purple-700">
                      {parts.find(p => p.id === recommendations.most_compatible)?.name}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
