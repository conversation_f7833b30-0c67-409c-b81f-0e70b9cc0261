"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MessageCircle, MapPin, Phone } from "lucide-react"
import { useState } from "react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface ProductCardProps {
  id: string
  name: string
  code: string
  price: number
  image: string
  brand: string
  isOriginal?: boolean
  inStock?: boolean
  state?: string
  dealership?: string
  city?: string
  lastUpdate?: string
}

export function ProductCard({
  id,
  name,
  code,
  price,
  image,
  brand,
  isOriginal = true,
  inStock = true,
  state = "",
  dealership = "",
  city = "",
  lastUpdate = "",
}: ProductCardProps) {
  const [phoneClicked, setPhoneClicked] = useState(false)
  const formattedPrice = new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(price)

  // Format date to Brazilian format
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    const date = new Date(dateString)
    return date.toLocaleDateString("pt-BR")
  }

  // Simulate a phone number
  const phoneNumber = "+55 11 99999-9999"

  // Track phone click
  const handlePhoneClick = () => {
    setPhoneClicked(true)
    // Here you would also log this interaction to your analytics or backend
    console.log(`Phone clicked for product ${id}`)
  }

  return (
    <Card className="product-card-hover overflow-hidden">
      <div className="relative aspect-square">
        <Image
          src={image || "/placeholder.svg"}
          alt={name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        {isOriginal && <Badge className="absolute right-2 top-2 bg-primary">Original</Badge>}
      </div>
      <CardContent className="p-4">
        <div className="mb-2 text-sm font-medium text-muted-foreground">
          {brand} • Código: {code}
        </div>
        <h3 className="line-clamp-2 text-base font-semibold">{name}</h3>
        <div className="mt-2 text-xl font-bold text-primary">{formattedPrice}</div>

        {/* Location and dealership info */}
        {(dealership || city || state) && (
          <div className="mt-2 flex items-start gap-1 text-xs text-muted-foreground">
            <MapPin className="mt-0.5 h-3 w-3 shrink-0" />
            <div>
              {dealership && <div className="font-medium">{dealership}</div>}
              {(city || state) && (
                <div>
                  {city}
                  {city && state && ", "}
                  {state}
                </div>
              )}
              {lastUpdate && <div>Atualizado em: {formatDate(lastUpdate)}</div>}
            </div>
          </div>
        )}

        {!inStock && (
          <Badge variant="outline" className="mt-2 text-yellow-600">
            Sob encomenda
          </Badge>
        )}
      </CardContent>
      <CardFooter className="flex gap-2 p-4 pt-0">
        <Button asChild className="flex-1">
          <Link href={`/produto/${id}`}>Ver detalhes</Link>
        </Button>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={handlePhoneClick}>
                <Phone className="h-5 w-5" />
                <span className="sr-only">Ver telefone</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>{phoneClicked ? phoneNumber : "Clique para ver o telefone"}</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon">
                <MessageCircle className="h-5 w-5" />
                <span className="sr-only">Contatar via WhatsApp</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Contatar via WhatsApp</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardFooter>
    </Card>
  )
}

