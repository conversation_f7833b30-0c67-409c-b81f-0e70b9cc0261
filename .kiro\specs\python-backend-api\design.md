# Design Document

## Overview

O backend Python será desenvolvido usando FastAPI como framework principal, fornecendo uma API RESTful robusta e performática para a aplicação AutoParts. O sistema será projetado para lidar com grandes volumes de dados de estoque de peças automotivas, integração com o gateway de pagamento Asaas, e fornecer endpoints otimizados para o frontend Next.js.

### Arquitetura Geral
- **Framework**: FastAPI com Pydantic para validação de dados
- **Banco de Dados**: PostgreSQL com SQLAlchemy ORM
- **Cache**: Redis para cache de consultas frequentes
- **Queue**: Celery com Redis para processamento assíncrono
- **Autenticação**: JWT com refresh tokens
- **Documentação**: Swagger/OpenAPI automática via FastAPI

## Architecture

### Arquitetura em Camadas

```
┌─────────────────────────────────────┐
│           Frontend (Next.js)        │
└─────────────────┬───────────────────┘
                  │ HTTP/REST
┌─────────────────▼───────────────────┐
│              API Layer              │
│            (FastAPI)                │
├─────────────────────────────────────┤
│           Business Logic            │
│            (Services)               │
├─────────────────────────────────────┤
│           Data Access               │
│         (Repositories)              │
├─────────────────────────────────────┤
│            Database                 │
│          (PostgreSQL)               │
└─────────────────────────────────────┘

External Services:
┌─────────────┐    ┌─────────────┐
│    Asaas    │    │    Redis    │
│   (Payments)│    │   (Cache)   │
└─────────────┘    └─────────────┘
```

### Microserviços Internos
- **API Service**: Endpoints REST principais
- **File Processing Service**: Processamento de Excel/CSV
- **Payment Service**: Integração com Asaas
- **Notification Service**: Emails e webhooks
- **Search Service**: Busca otimizada de peças

## Components and Interfaces

### 1. API Layer (FastAPI)

#### Estrutura de Routers
```python
/api/v1/
├── auth/          # Autenticação e autorização
├── dealerships/   # Gestão de concessionárias
├── parts/         # Catálogo de peças
├── inventory/     # Gestão de estoque
├── subscriptions/ # Assinaturas Asaas
├── uploads/       # Upload de arquivos
└── admin/         # Endpoints administrativos
```

#### Middleware Stack
- **CORS**: Configurado para frontend Next.js
- **Authentication**: JWT validation middleware
- **Rate Limiting**: Proteção contra abuse
- **Request Logging**: Logs estruturados
- **Error Handling**: Tratamento global de exceções

### 2. Business Logic Layer

#### Services
```python
class DealershipService:
    - create_dealership()
    - update_dealership()
    - get_dealership_by_id()
    - list_dealerships()

class InventoryService:
    - process_inventory_file()
    - update_part_stock()
    - search_parts()
    - get_part_availability()

class SubscriptionService:
    - create_subscription()
    - handle_payment_webhook()
    - check_subscription_status()
    - cancel_subscription()

class FileProcessingService:
    - validate_file_format()
    - process_excel_file()
    - process_csv_file()
    - handle_large_files()
```

### 3. Data Access Layer

#### Repository Pattern
```python
class BaseRepository:
    - create()
    - get_by_id()
    - update()
    - delete()
    - list_with_pagination()

class DealershipRepository(BaseRepository)
class PartRepository(BaseRepository)
class InventoryRepository(BaseRepository)
class SubscriptionRepository(BaseRepository)
```

### 4. External Integrations

#### Asaas Integration
```python
class AsaasClient:
    - create_customer()
    - create_subscription()
    - process_payment()
    - handle_webhook()
    - get_subscription_status()
```

## Data Models

### Core Entities

#### Dealership (Concessionária)
```python
class Dealership:
    id: UUID
    name: str
    cnpj: str
    email: str
    phone: str
    address: Address
    subscription_id: str  # Asaas subscription ID
    is_active: bool
    created_at: datetime
    updated_at: datetime
```

#### Part (Peça)
```python
class Part:
    id: UUID
    code: str  # Código original da peça
    name: str
    description: str
    category: str
    brand: str
    model: str
    year: int
    created_at: datetime
    updated_at: datetime
```

#### Inventory (Estoque)
```python
class Inventory:
    id: UUID
    dealership_id: UUID
    part_id: UUID
    quantity: int
    price: Decimal
    cost: Decimal
    location: str
    last_updated: datetime
```

#### Subscription (Assinatura)
```python
class Subscription:
    id: UUID
    dealership_id: UUID
    asaas_subscription_id: str
    plan_type: str
    status: SubscriptionStatus
    next_due_date: date
    created_at: datetime
    updated_at: datetime
```

### Database Schema Design

```sql
-- Indexes para performance
CREATE INDEX idx_parts_code ON parts(code);
CREATE INDEX idx_inventory_dealership ON inventory(dealership_id);
CREATE INDEX idx_inventory_part ON inventory(part_id);
CREATE INDEX idx_inventory_search ON inventory(dealership_id, part_id);

-- Full-text search para peças
CREATE INDEX idx_parts_search ON parts USING gin(to_tsvector('portuguese', name || ' ' || description));
```

## Error Handling

### Estrutura de Erros Padronizada
```python
class APIError:
    code: str
    message: str
    details: Optional[Dict]
    timestamp: datetime

class ErrorCodes:
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    SUBSCRIPTION_EXPIRED = "SUBSCRIPTION_EXPIRED"
    PART_NOT_FOUND = "PART_NOT_FOUND"
    UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS"
```

### Exception Handling Strategy
- **Validation Errors**: Retorno 422 com detalhes específicos
- **Authentication Errors**: Retorno 401 com mensagem em português
- **Authorization Errors**: Retorno 403 com contexto
- **Business Logic Errors**: Retorno 400 com código de erro específico
- **Internal Errors**: Retorno 500 com ID de rastreamento

## Testing Strategy

### Níveis de Teste

#### 1. Unit Tests
- **Services**: Lógica de negócio isolada
- **Repositories**: Operações de banco de dados
- **Utils**: Funções utilitárias
- **Coverage**: Mínimo 80%

#### 2. Integration Tests
- **API Endpoints**: Testes end-to-end
- **Database Operations**: Transações completas
- **External Services**: Mocks do Asaas
- **File Processing**: Upload e processamento

#### 3. Performance Tests
- **Load Testing**: Simulação de carga alta
- **File Processing**: Arquivos grandes (100k+ linhas)
- **Search Performance**: Consultas complexas
- **API Response Time**: < 500ms para buscas

### Test Data Strategy
- **Fixtures**: Dados de teste padronizados
- **Factories**: Geração dinâmica de dados
- **Database Isolation**: Transações rollback
- **Mock External APIs**: Asaas e outros serviços

### Continuous Integration
```yaml
# GitHub Actions Pipeline
- Lint (flake8, black, isort)
- Type Check (mypy)
- Unit Tests (pytest)
- Integration Tests
- Security Scan (bandit)
- Dependency Check
```

## Performance Considerations

### Database Optimization
- **Connection Pooling**: SQLAlchemy pool configurado
- **Query Optimization**: Eager loading para relacionamentos
- **Indexing Strategy**: Índices para consultas frequentes
- **Pagination**: Limit/offset para listas grandes

### Caching Strategy
- **Redis Cache**: Consultas de peças frequentes
- **TTL Configuration**: 15 minutos para dados de estoque
- **Cache Invalidation**: Atualização automática no upload
- **Memory Management**: Limpeza automática de cache

### File Processing Optimization
- **Chunked Processing**: Arquivos grandes em lotes
- **Background Jobs**: Celery para processamento assíncrono
- **Progress Tracking**: Status de processamento em tempo real
- **Memory Efficiency**: Streaming de arquivos grandes

### API Performance
- **Response Compression**: Gzip automático
- **Pagination**: Máximo 50 itens por página
- **Field Selection**: Permitir seleção de campos específicos
- **Rate Limiting**: 1000 requests/hora por usuário