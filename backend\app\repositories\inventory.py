"""
Inventory repository for data access operations.
"""
from typing import Optional, List, Tuple, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc, text
from uuid import UUID
from datetime import datetime, timedelta
from decimal import Decimal

from app.models.inventory import Inventory
from app.models.part import Part
from app.models.dealership import Dealership
from app.repositories.base import BaseRepository


class InventoryRepository(BaseRepository[Inventory]):
    """
    Repository for inventory-specific database operations.
    """

    def __init__(self, db: Session):
        super().__init__(Inventory, db)

    def get_by_dealership_and_part(
        self,
        dealership_id: UUID,
        part_id: UUID
    ) -> Optional[Inventory]:
        """
        Get inventory item by dealership and part.
        
        Args:
            dealership_id: Dealership UUID
            part_id: Part UUID
            
        Returns:
            Inventory instance or None if not found
        """
        return self.db.query(Inventory).filter(
            and_(
                Inventory.dealership_id == dealership_id,
                Inventory.part_id == part_id
            )
        ).first()

    def get_by_dealership(
        self,
        dealership_id: UUID,
        skip: int = 0,
        limit: int = 100,
        available_only: bool = True
    ) -> Tuple[List[Inventory], int]:
        """
        Get inventory items for a dealership.
        
        Args:
            dealership_id: Dealership UUID
            skip: Number of records to skip
            limit: Maximum number of records to return
            available_only: Only return available items
            
        Returns:
            Tuple of (inventory list, total count)
        """
        query = self.db.query(Inventory).filter(
            Inventory.dealership_id == dealership_id
        )
        
        if available_only:
            query = query.filter(
                and_(
                    Inventory.is_available == True,
                    Inventory.quantity > 0
                )
            )
        
        total = query.count()
        inventories = query.offset(skip).limit(limit).all()
        
        return inventories, total

    def get_by_part(
        self,
        part_id: UUID,
        skip: int = 0,
        limit: int = 100,
        available_only: bool = True
    ) -> Tuple[List[Inventory], int]:
        """
        Get inventory items for a specific part.
        
        Args:
            part_id: Part UUID
            skip: Number of records to skip
            limit: Maximum number of records to return
            available_only: Only return available items
            
        Returns:
            Tuple of (inventory list, total count)
        """
        query = self.db.query(Inventory).filter(Inventory.part_id == part_id)
        
        if available_only:
            query = query.filter(
                and_(
                    Inventory.is_available == True,
                    Inventory.quantity > 0
                )
            )
        
        total = query.count()
        inventories = query.offset(skip).limit(limit).all()
        
        return inventories, total

    def search_inventory(
        self,
        part_code: Optional[str] = None,
        part_name: Optional[str] = None,
        dealership_id: Optional[UUID] = None,
        brand: Optional[str] = None,
        model: Optional[str] = None,
        year: Optional[int] = None,
        category: Optional[str] = None,
        condition: Optional[str] = None,
        min_price: Optional[Decimal] = None,
        max_price: Optional[Decimal] = None,
        min_quantity: Optional[int] = None,
        available_only: bool = True,
        in_stock_only: bool = True,
        low_stock_only: bool = False,
        city: Optional[str] = None,
        state: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "last_updated",
        sort_order: str = "desc"
    ) -> Tuple[List[Inventory], int]:
        """
        Search inventory with advanced filtering.
        
        Args:
            part_code: Filter by part code
            part_name: Filter by part name
            dealership_id: Filter by dealership
            brand: Filter by vehicle brand
            model: Filter by vehicle model
            year: Filter by vehicle year
            category: Filter by part category
            condition: Filter by condition
            min_price: Minimum price filter
            max_price: Maximum price filter
            min_quantity: Minimum quantity filter
            available_only: Only available items
            in_stock_only: Only items in stock
            low_stock_only: Only low stock items
            city: Filter by dealership city
            state: Filter by dealership state
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            
        Returns:
            Tuple of (inventory list, total count)
        """
        query = self.db.query(Inventory).join(Part).join(Dealership)
        
        # Apply filters
        conditions = []
        
        # Part filters
        if part_code:
            conditions.append(Part.code.ilike(f"%{part_code}%"))
        
        if part_name:
            conditions.append(Part.name.ilike(f"%{part_name}%"))
        
        if brand:
            conditions.append(Part.brand.ilike(f"%{brand}%"))
        
        if model:
            conditions.append(Part.model.ilike(f"%{model}%"))
        
        if year:
            conditions.append(
                or_(
                    Part.year == year,
                    and_(
                        Part.year_start <= year,
                        Part.year_end >= year
                    )
                )
            )
        
        if category:
            conditions.append(Part.category.ilike(f"%{category}%"))
        
        # Inventory filters
        if dealership_id:
            conditions.append(Inventory.dealership_id == dealership_id)
        
        if condition:
            conditions.append(Inventory.condition == condition)
        
        if min_price:
            conditions.append(Inventory.price >= min_price)
        
        if max_price:
            conditions.append(Inventory.price <= max_price)
        
        if min_quantity:
            conditions.append(Inventory.quantity >= min_quantity)
        
        if available_only:
            conditions.append(Inventory.is_available == True)
        
        if in_stock_only:
            conditions.append(Inventory.quantity > 0)
        
        if low_stock_only:
            conditions.append(
                Inventory.quantity <= func.coalesce(Inventory.minimum_stock, 0)
            )
        
        # Dealership filters
        if city:
            conditions.append(Dealership.city.ilike(f"%{city}%"))
        
        if state:
            conditions.append(Dealership.state.ilike(f"%{state}%"))
        
        # Apply all conditions
        if conditions:
            query = query.filter(and_(*conditions))
        
        # Get total count before pagination
        total = query.count()
        
        # Apply sorting
        if sort_by == "part_code":
            sort_field = Part.code
        elif sort_by == "dealership_name":
            sort_field = Dealership.name
        else:
            sort_field = getattr(Inventory, sort_by, Inventory.last_updated)
        
        if sort_order == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(asc(sort_field))
        
        # Apply pagination
        inventories = query.offset(skip).limit(limit).all()
        
        return inventories, total

    def get_low_stock_items(
        self,
        dealership_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Inventory], int]:
        """
        Get items with low stock levels.
        
        Args:
            dealership_id: Optional dealership filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (inventory list, total count)
        """
        query = self.db.query(Inventory).filter(
            Inventory.quantity <= func.coalesce(Inventory.minimum_stock, 0)
        )
        
        if dealership_id:
            query = query.filter(Inventory.dealership_id == dealership_id)
        
        total = query.count()
        inventories = query.offset(skip).limit(limit).all()
        
        return inventories, total

    def get_out_of_stock_items(
        self,
        dealership_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Inventory], int]:
        """
        Get items that are out of stock.
        
        Args:
            dealership_id: Optional dealership filter
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (inventory list, total count)
        """
        query = self.db.query(Inventory).filter(Inventory.quantity == 0)
        
        if dealership_id:
            query = query.filter(Inventory.dealership_id == dealership_id)
        
        total = query.count()
        inventories = query.offset(skip).limit(limit).all()
        
        return inventories, total

    def get_inventory_stats(
        self,
        dealership_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """
        Get inventory statistics.
        
        Args:
            dealership_id: Optional dealership filter
            
        Returns:
            Dictionary with inventory statistics
        """
        query = self.db.query(Inventory)
        
        if dealership_id:
            query = query.filter(Inventory.dealership_id == dealership_id)
        
        # Total items and value
        total_items = query.count()
        total_value = query.with_entities(
            func.sum(Inventory.price * Inventory.quantity)
        ).scalar() or 0
        
        # Low stock and out of stock counts
        low_stock_count = query.filter(
            Inventory.quantity <= func.coalesce(Inventory.minimum_stock, 0)
        ).count()
        
        out_of_stock_count = query.filter(Inventory.quantity == 0).count()
        
        # Items by condition
        condition_stats = query.with_entities(
            Inventory.condition,
            func.count(Inventory.id)
        ).group_by(Inventory.condition).all()
        
        # Items by category (join with Part)
        category_stats = query.join(Part).with_entities(
            Part.category,
            func.count(Inventory.id)
        ).group_by(Part.category).all()
        
        # Recent updates (last 7 days)
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        recent_updates = query.filter(
            Inventory.last_updated >= seven_days_ago
        ).count()
        
        return {
            'total_items': total_items,
            'total_value': float(total_value),
            'low_stock_items': low_stock_count,
            'out_of_stock_items': out_of_stock_count,
            'items_by_condition': [
                {'condition': condition or 'Unknown', 'count': count}
                for condition, count in condition_stats
            ],
            'items_by_category': [
                {'category': category or 'Uncategorized', 'count': count}
                for category, count in category_stats[:10]  # Top 10 categories
            ],
            'recent_updates': recent_updates
        }

    def get_inventory_valuation(
        self,
        dealership_id: UUID
    ) -> Dict[str, Any]:
        """
        Get inventory valuation for a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            Dictionary with valuation data
        """
        query = self.db.query(Inventory).filter(
            Inventory.dealership_id == dealership_id
        )
        
        # Calculate totals
        totals = query.with_entities(
            func.sum(func.coalesce(Inventory.cost, 0) * Inventory.quantity).label('total_cost'),
            func.sum(Inventory.price * Inventory.quantity).label('total_selling_value'),
            func.count(Inventory.id).label('items_count')
        ).first()
        
        total_cost = float(totals.total_cost or 0)
        total_selling_value = float(totals.total_selling_value or 0)
        items_count = totals.items_count or 0
        
        potential_profit = total_selling_value - total_cost
        margin_percentage = (potential_profit / total_selling_value * 100) if total_selling_value > 0 else 0
        
        return {
            'total_cost': total_cost,
            'total_selling_value': total_selling_value,
            'potential_profit': potential_profit,
            'margin_percentage': round(margin_percentage, 2),
            'items_count': items_count,
            'valuation_date': datetime.utcnow()
        }

    def update_quantity(
        self,
        inventory_id: UUID,
        new_quantity: int,
        reason: Optional[str] = None
    ) -> Optional[Inventory]:
        """
        Update inventory quantity with tracking.
        
        Args:
            inventory_id: Inventory UUID
            new_quantity: New quantity value
            reason: Reason for the change
            
        Returns:
            Updated inventory instance or None if not found
        """
        inventory = self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        # Update quantity and last_updated
        inventory.quantity = new_quantity
        inventory.last_updated = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(inventory)
        
        return inventory

    def bulk_update_availability(
        self,
        inventory_ids: List[UUID],
        is_available: bool
    ) -> int:
        """
        Bulk update availability status.
        
        Args:
            inventory_ids: List of inventory UUIDs
            is_available: New availability status
            
        Returns:
            Number of updated records
        """
        updated = self.db.query(Inventory).filter(
            Inventory.id.in_(inventory_ids)
        ).update(
            {
                'is_available': is_available,
                'last_updated': datetime.utcnow()
            },
            synchronize_session=False
        )
        
        self.db.commit()
        return updated
