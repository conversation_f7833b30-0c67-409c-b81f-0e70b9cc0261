# Application Settings
APP_NAME=AutoParts API
APP_VERSION=1.0.0
DEBUG=False

# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=autoparts
POSTGRES_PASSWORD=your_password_here
POSTGRES_DB=autoparts
POSTGRES_PORT=5432
DATABASE_URL=postgresql://autoparts:your_password_here@localhost:5432/autoparts

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000","https://yourdomain.com"]

# Asaas Integration
ASAAS_API_URL=https://sandbox.asaas.com/api/v3
ASAAS_API_KEY=your_asaas_api_key_here
ASAAS_WEBHOOK_SECRET=your_asaas_webhook_secret_here

# File Upload Configuration
MAX_FILE_SIZE=52428800
UPLOAD_DIR=uploads
ALLOWED_FILE_EXTENSIONS=[".xlsx",".xls",".csv"]

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1
