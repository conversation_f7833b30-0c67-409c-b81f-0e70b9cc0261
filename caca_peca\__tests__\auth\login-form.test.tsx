/**
 * Login Form Component Tests
 * Tests for the enhanced login form component
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LoginForm } from '@/components/auth/login-form';
import { useAuth } from '@/lib/auth/context';

// Mock the auth context
vi.mock('@/lib/auth/context');
const mockUseAuth = vi.mocked(useAuth);

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

describe('LoginForm', () => {
  const mockLogin = vi.fn();
  const mockClearError = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      user: null,
      isAuthenticated: false,
      logout: vi.fn(),
      register: vi.fn(),
      refreshUser: vi.fn(),
      getRedirectPath: vi.fn(),
      hasRole: vi.fn(),
      isAdmin: vi.fn(),
      isDealership: vi.fn(),
      isCustomer: vi.fn(),
    });
  });

  it('should render login form with all fields', () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/e-mail/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/senha/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /entrar/i })).toBeInTheDocument();
    expect(screen.getByText(/esqueceu a senha/i)).toBeInTheDocument();
  });

  it('should show validation errors for empty fields', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const submitButton = screen.getByRole('button', { name: /entrar/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/e-mail é obrigatório/i)).toBeInTheDocument();
      expect(screen.getByText(/senha é obrigatória/i)).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('should show validation error for invalid email', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/e-mail/i);
    const submitButton = screen.getByRole('button', { name: /entrar/i });

    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/e-mail inválido/i)).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('should show validation error for short password', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/e-mail/i);
    const passwordInput = screen.getByLabelText(/senha/i);
    const submitButton = screen.getByRole('button', { name: /entrar/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, '123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/senha deve ter pelo menos 6 caracteres/i)).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    mockLogin.mockResolvedValueOnce({ success: true, redirectTo: '/dashboard' });

    const mockOnSuccess = vi.fn();
    render(<LoginForm onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByLabelText(/e-mail/i);
    const passwordInput = screen.getByLabelText(/senha/i);
    const submitButton = screen.getByRole('button', { name: /entrar/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    expect(mockOnSuccess).toHaveBeenCalledWith('/dashboard');
  });

  it('should handle login failure', async () => {
    const user = userEvent.setup();
    mockLogin.mockResolvedValueOnce({ success: false, error: 'Invalid credentials' });

    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/e-mail/i);
    const passwordInput = screen.getByLabelText(/senha/i);
    const submitButton = screen.getByRole('button', { name: /entrar/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled();
    });
  });

  it('should toggle password visibility', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const passwordInput = screen.getByLabelText(/senha/i);
    const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon button

    expect(passwordInput).toHaveAttribute('type', 'password');

    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');

    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('should show loading state during submission', async () => {
    const user = userEvent.setup();
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isLoading: true,
    });

    render(<LoginForm />);

    const submitButton = screen.getByRole('button', { name: /entrando/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/entrando/i)).toBeInTheDocument();
  });

  it('should display global error message', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      error: 'Network error',
    });

    render(<LoginForm />);

    expect(screen.getByText(/network error/i)).toBeInTheDocument();
  });

  it('should clear field errors when user starts typing', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/e-mail/i);
    const submitButton = screen.getByRole('button', { name: /entrar/i });

    // Trigger validation error
    await user.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText(/e-mail é obrigatório/i)).toBeInTheDocument();
    });

    // Start typing to clear error
    await user.type(emailInput, 'test');
    expect(screen.queryByText(/e-mail é obrigatório/i)).not.toBeInTheDocument();
  });

  it('should fill test credentials when buttons are clicked', async () => {
    const user = userEvent.setup();
    render(<LoginForm showTestCredentials={true} />);

    const emailInput = screen.getByLabelText(/e-mail/i) as HTMLInputElement;
    const passwordInput = screen.getByLabelText(/senha/i) as HTMLInputElement;

    // Click dealership test credentials
    const dealershipButton = screen.getByText(/concessionária/i);
    await user.click(dealershipButton);

    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('Teste@123');

    // Click admin test credentials
    const adminButton = screen.getByText(/administrador/i);
    await user.click(adminButton);

    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('Admin@123');
  });

  it('should not show test credentials when disabled', () => {
    render(<LoginForm showTestCredentials={false} />);

    expect(screen.queryByText(/credenciais de teste/i)).not.toBeInTheDocument();
  });
});