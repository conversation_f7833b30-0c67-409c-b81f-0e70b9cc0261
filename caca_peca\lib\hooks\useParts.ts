/**
 * Custom hooks for parts catalog functionality
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { partsService } from '../api/parts';
import {
  PartSearchRequest,
  PartSearchFilters,
  Part,
  PartCompatibilityCheck,
  PartComparison,
  PartSpecifications
} from '../types/api';

// Search parts with basic filters
export function usePartsSearch(searchParams: PartSearchRequest, enabled = true) {
  return useQuery({
    queryKey: ['parts', 'search', searchParams],
    queryFn: () => partsService.searchParts(searchParams),
    enabled: enabled && (!!searchParams.query || !!searchParams.part_number || !!searchParams.brand),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Advanced search with complex filters
export function useAdvancedPartsSearch(filters: PartSearchFilters, enabled = true) {
  return useQuery({
    queryKey: ['parts', 'advanced-search', filters],
    queryFn: () => partsService.advancedSearch(filters),
    enabled,
    staleTime: 5 * 60 * 1000,
  });
}

// Get part by ID
export function usePart(partId: string, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId],
    queryFn: () => partsService.getPartById(partId),
    enabled: enabled && !!partId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get part by part number
export function usePartByNumber(partNumber: string, enabled = true) {
  return useQuery({
    queryKey: ['parts', 'by-number', partNumber],
    queryFn: () => partsService.getPartByNumber(partNumber),
    enabled: enabled && !!partNumber,
    staleTime: 10 * 60 * 1000,
  });
}

// Get part specifications
export function usePartSpecifications(partId: string, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'specifications'],
    queryFn: () => partsService.getPartSpecifications(partId),
    enabled: enabled && !!partId,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Get related parts
export function useRelatedParts(partId: string, limit = 10, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'related', limit],
    queryFn: () => partsService.getRelatedParts(partId, limit),
    enabled: enabled && !!partId,
    staleTime: 15 * 60 * 1000,
  });
}

// Get alternative parts
export function useAlternativeParts(partId: string, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'alternatives'],
    queryFn: () => partsService.getAlternativeParts(partId),
    enabled: enabled && !!partId,
    staleTime: 15 * 60 * 1000,
  });
}

// Get similar parts
export function useSimilarParts(partId: string, limit = 10, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'similar', limit],
    queryFn: () => partsService.getSimilarParts(partId, limit),
    enabled: enabled && !!partId,
    staleTime: 15 * 60 * 1000,
  });
}

// Get part availability
export function usePartAvailability(
  partId: string, 
  location?: { state?: string; city?: string }, 
  enabled = true
) {
  return useQuery({
    queryKey: ['parts', partId, 'availability', location],
    queryFn: () => partsService.getPartAvailability(partId, location),
    enabled: enabled && !!partId,
    staleTime: 2 * 60 * 1000, // 2 minutes (more frequent updates for availability)
  });
}

// Get part price history
export function usePartPriceHistory(partId: string, days = 30, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'price-history', days],
    queryFn: () => partsService.getPartPriceHistory(partId, days),
    enabled: enabled && !!partId,
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

// Get popular parts
export function usePopularParts(category?: string, limit = 20) {
  return useQuery({
    queryKey: ['parts', 'popular', category, limit],
    queryFn: () => partsService.getPopularParts(category, limit),
    staleTime: 30 * 60 * 1000,
  });
}

// Get recently viewed parts
export function useRecentlyViewedParts(limit = 10) {
  return useQuery({
    queryKey: ['parts', 'recent', limit],
    queryFn: () => partsService.getRecentlyViewed(limit),
    staleTime: 5 * 60 * 1000,
  });
}

// Get part suggestions
export function usePartSuggestions(query: string, limit = 10, enabled = true) {
  return useQuery({
    queryKey: ['parts', 'suggestions', query, limit],
    queryFn: () => partsService.getPartSuggestions(query, limit),
    enabled: enabled && query.length >= 2,
    staleTime: 10 * 60 * 1000,
  });
}

// Get categories
export function usePartCategories() {
  return useQuery({
    queryKey: ['parts', 'categories'],
    queryFn: () => partsService.getCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

// Get brands
export function usePartBrands() {
  return useQuery({
    queryKey: ['parts', 'brands'],
    queryFn: () => partsService.getBrands(),
    staleTime: 60 * 60 * 1000,
  });
}

// Get vehicle makes
export function useVehicleMakes() {
  return useQuery({
    queryKey: ['vehicles', 'makes'],
    queryFn: () => partsService.getVehicleMakes(),
    staleTime: 60 * 60 * 1000,
  });
}

// Get vehicle models
export function useVehicleModels(make: string, enabled = true) {
  return useQuery({
    queryKey: ['vehicles', 'models', make],
    queryFn: () => partsService.getVehicleModels(make),
    enabled: enabled && !!make,
    staleTime: 60 * 60 * 1000,
  });
}

// Get vehicle years
export function useVehicleYears(make: string, model: string, enabled = true) {
  return useQuery({
    queryKey: ['vehicles', 'years', make, model],
    queryFn: () => partsService.getVehicleYears(make, model),
    enabled: enabled && !!make && !!model,
    staleTime: 60 * 60 * 1000,
  });
}

// Search by OEM number
export function useOEMSearch(oemNumber: string, enabled = true) {
  return useQuery({
    queryKey: ['parts', 'oem', oemNumber],
    queryFn: () => partsService.searchByOEM(oemNumber),
    enabled: enabled && !!oemNumber,
    staleTime: 10 * 60 * 1000,
  });
}

// Get installation guides
export function useInstallationGuides(partId: string, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'guides'],
    queryFn: () => partsService.getInstallationGuides(partId),
    enabled: enabled && !!partId,
    staleTime: 60 * 60 * 1000,
  });
}

// Get part reviews
export function usePartReviews(partId: string, page = 1, size = 10, enabled = true) {
  return useQuery({
    queryKey: ['parts', partId, 'reviews', page, size],
    queryFn: () => partsService.getPartReviews(partId, page, size),
    enabled: enabled && !!partId,
    staleTime: 5 * 60 * 1000,
  });
}

// Mutations
export function useCheckCompatibility() {
  return useMutation({
    mutationFn: ({ partId, vehicle }: { 
      partId: string; 
      vehicle: { make: string; model: string; year: number } 
    }) => partsService.checkCompatibility(partId, vehicle),
  });
}

export function useCompareParts() {
  return useMutation({
    mutationFn: (partIds: string[]) => partsService.compareParts(partIds),
  });
}

export function useTrackPartView() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (partId: string) => partsService.trackPartView(partId),
    onSuccess: () => {
      // Invalidate recently viewed parts
      queryClient.invalidateQueries({ queryKey: ['parts', 'recent'] });
    },
  });
}

export function useAddPartReview() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ partId, review }: { 
      partId: string; 
      review: { rating: number; comment: string } 
    }) => partsService.addPartReview(partId, review),
    onSuccess: (_, { partId }) => {
      // Invalidate part reviews
      queryClient.invalidateQueries({ queryKey: ['parts', partId, 'reviews'] });
    },
  });
}
