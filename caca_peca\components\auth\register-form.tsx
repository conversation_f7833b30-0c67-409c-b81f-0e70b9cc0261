/**
 * Registration Form Component
 * Enhanced registration form for new dealerships with validation
 */

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { PasswordStrength } from '@/components/password-strength';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/lib/types/api';
import { isValidEmail, validatePassword } from '@/lib/auth/utils';

interface RegisterFormProps {
  onSuccess?: () => void;
  defaultRole?: UserRole;
  showRoleSelection?: boolean;
}

export function RegisterForm({ 
  onSuccess, 
  defaultRole = UserRole.CUSTOMER,
  showRoleSelection = false 
}: RegisterFormProps) {
  const router = useRouter();
  const { register, isLoading, error, clearError } = useAuth();
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: defaultRole,
    dealershipName: '',
    cnpj: '',
    phone: '',
    acceptTerms: false,
    acceptMarketing: false,
  });
  
  const [formErrors, setFormErrors] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    dealershipName: '',
    cnpj: '',
    phone: '',
    acceptTerms: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  const isDealershipRegistration = formData.role === UserRole.DEALERSHIP;

  const validateForm = (): boolean => {
    const errors = {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      dealershipName: '',
      cnpj: '',
      phone: '',
      acceptTerms: '',
    };

    // Full name validation
    if (!formData.fullName.trim()) {
      errors.fullName = 'Nome completo é obrigatório';
    } else if (formData.fullName.trim().length < 2) {
      errors.fullName = 'Nome deve ter pelo menos 2 caracteres';
    }

    // Email validation
    if (!formData.email) {
      errors.email = 'E-mail é obrigatório';
    } else if (!isValidEmail(formData.email)) {
      errors.email = 'E-mail inválido';
    }

    // Password validation
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0] || 'Senha inválida';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Confirmação de senha é obrigatória';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Senhas não coincidem';
    }

    // Dealership-specific validations
    if (isDealershipRegistration) {
      if (!formData.dealershipName.trim()) {
        errors.dealershipName = 'Nome da concessionária é obrigatório';
      }

      if (!formData.cnpj.trim()) {
        errors.cnpj = 'CNPJ é obrigatório';
      } else if (!isValidCNPJ(formData.cnpj)) {
        errors.cnpj = 'CNPJ inválido';
      }

      if (!formData.phone.trim()) {
        errors.phone = 'Telefone é obrigatório';
      } else if (!isValidPhone(formData.phone)) {
        errors.phone = 'Telefone inválido';
      }
    }

    // Terms acceptance validation
    if (!formData.acceptTerms) {
      errors.acceptTerms = 'Você deve aceitar os termos de uso';
    }

    setFormErrors(errors);
    return Object.values(errors).every(error => !error);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await register({
        email: formData.email,
        password: formData.password,
        full_name: formData.fullName,
        role: formData.role,
        // Add dealership-specific data if needed
        ...(isDealershipRegistration && {
          dealership_name: formData.dealershipName,
          cnpj: formData.cnpj,
          phone: formData.phone,
        }),
      });

      if (result.success) {
        setRegistrationSuccess(true);
        
        if (onSuccess) {
          onSuccess();
        } else {
          // Redirect to login after successful registration
          setTimeout(() => {
            router.push('/auth?message=registration-success');
          }, 2000);
        }
      }
    } catch (err) {
      console.error('Registration error:', err);
    }
  };

  const handleInputChange = (field: keyof typeof formData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // Clear global error
    if (error) {
      clearError();
    }
  };

  const handleRoleChange = (role: UserRole) => {
    setFormData(prev => ({ ...prev, role }));
    // Clear dealership-specific errors when switching away from dealership
    if (role !== UserRole.DEALERSHIP) {
      setFormErrors(prev => ({
        ...prev,
        dealershipName: '',
        cnpj: '',
        phone: '',
      }));
    }
  };

  // Success state
  if (registrationSuccess) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
            <h3 className="text-lg font-semibold">Cadastro realizado com sucesso!</h3>
            <p className="text-sm text-muted-foreground">
              Sua conta foi criada. Você será redirecionado para a página de login.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Criar uma conta</CardTitle>
        <CardDescription>
          {isDealershipRegistration 
            ? 'Cadastre sua concessionária na plataforma'
            : 'Preencha os dados abaixo para criar sua conta'
          }
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* Global Error */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Role Selection */}
          {showRoleSelection && (
            <div className="space-y-2">
              <Label>Tipo de conta</Label>
              <Select 
                value={formData.role} 
                onValueChange={handleRoleChange}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de conta" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={UserRole.CUSTOMER}>Cliente</SelectItem>
                  <SelectItem value={UserRole.DEALERSHIP}>Concessionária</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Full Name */}
          <div className="space-y-2">
            <Label htmlFor="fullName">Nome Completo</Label>
            <Input
              id="fullName"
              placeholder="Seu nome completo"
              value={formData.fullName}
              onChange={handleInputChange('fullName')}
              className={formErrors.fullName ? 'border-red-500' : ''}
              disabled={isLoading}
              autoComplete="name"
            />
            {formErrors.fullName && (
              <p className="text-sm text-red-500">{formErrors.fullName}</p>
            )}
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email">E-mail</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleInputChange('email')}
              className={formErrors.email ? 'border-red-500' : ''}
              disabled={isLoading}
              autoComplete="email"
            />
            {formErrors.email && (
              <p className="text-sm text-red-500">{formErrors.email}</p>
            )}
          </div>

          {/* Dealership-specific fields */}
          {isDealershipRegistration && (
            <>
              <div className="space-y-2">
                <Label htmlFor="dealershipName">Nome da Concessionária</Label>
                <Input
                  id="dealershipName"
                  placeholder="Nome da sua concessionária"
                  value={formData.dealershipName}
                  onChange={handleInputChange('dealershipName')}
                  className={formErrors.dealershipName ? 'border-red-500' : ''}
                  disabled={isLoading}
                  autoComplete="organization"
                />
                {formErrors.dealershipName && (
                  <p className="text-sm text-red-500">{formErrors.dealershipName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="cnpj">CNPJ</Label>
                <Input
                  id="cnpj"
                  placeholder="00.000.000/0000-00"
                  value={formData.cnpj}
                  onChange={handleInputChange('cnpj')}
                  className={formErrors.cnpj ? 'border-red-500' : ''}
                  disabled={isLoading}
                />
                {formErrors.cnpj && (
                  <p className="text-sm text-red-500">{formErrors.cnpj}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  placeholder="(11) 99999-9999"
                  value={formData.phone}
                  onChange={handleInputChange('phone')}
                  className={formErrors.phone ? 'border-red-500' : ''}
                  disabled={isLoading}
                  autoComplete="tel"
                />
                {formErrors.phone && (
                  <p className="text-sm text-red-500">{formErrors.phone}</p>
                )}
              </div>
            </>
          )}

          {/* Password */}
          <div className="space-y-2">
            <Label htmlFor="password">Senha</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Digite sua senha"
                value={formData.password}
                onChange={handleInputChange('password')}
                className={formErrors.password ? 'border-red-500 pr-10' : 'pr-10'}
                disabled={isLoading}
                autoComplete="new-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {formErrors.password && (
              <p className="text-sm text-red-500">{formErrors.password}</p>
            )}
            <PasswordStrength password={formData.password} />
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirmar Senha</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirme sua senha"
                value={formData.confirmPassword}
                onChange={handleInputChange('confirmPassword')}
                className={formErrors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
                disabled={isLoading}
                autoComplete="new-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {formErrors.confirmPassword && (
              <p className="text-sm text-red-500">{formErrors.confirmPassword}</p>
            )}
          </div>

          {/* Terms and Conditions */}
          <div className="space-y-3">
            <div className="flex items-start space-x-2">
              <Checkbox
                id="acceptTerms"
                checked={formData.acceptTerms}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, acceptTerms: !!checked }))
                }
                disabled={isLoading}
                className={formErrors.acceptTerms ? 'border-red-500' : ''}
              />
              <Label htmlFor="acceptTerms" className="text-sm leading-5">
                Eu aceito os{' '}
                <a href="/termos" target="_blank" className="text-primary hover:underline">
                  termos de uso
                </a>{' '}
                e a{' '}
                <a href="/privacidade" target="_blank" className="text-primary hover:underline">
                  política de privacidade
                </a>
              </Label>
            </div>
            {formErrors.acceptTerms && (
              <p className="text-sm text-red-500">{formErrors.acceptTerms}</p>
            )}

            <div className="flex items-start space-x-2">
              <Checkbox
                id="acceptMarketing"
                checked={formData.acceptMarketing}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, acceptMarketing: !!checked }))
                }
                disabled={isLoading}
              />
              <Label htmlFor="acceptMarketing" className="text-sm leading-5">
                Desejo receber e-mails promocionais e novidades (opcional)
              </Label>
            </div>
          </div>
        </CardContent>

        <CardFooter>
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Cadastrando...
              </>
            ) : (
              'Criar Conta'
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}

// Utility functions for validation
function isValidCNPJ(cnpj: string): boolean {
  // Remove non-numeric characters
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  
  // Basic length check
  if (cleanCNPJ.length !== 14) return false;
  
  // Check for repeated digits
  if (/^(\d)\1+$/.test(cleanCNPJ)) return false;
  
  // CNPJ validation algorithm would go here
  // For now, just check length and format
  return true;
}

function isValidPhone(phone: string): boolean {
  // Remove non-numeric characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Brazilian phone number validation (10 or 11 digits)
  return cleanPhone.length >= 10 && cleanPhone.length <= 11;
}