/**
 * Authentication Store using Zustand
 * Manages authentication state and provides auth actions
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { authService } from '../api/auth';
import { User, LoginRequest, UserCreate, UserRole } from '../types/api';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<{ success: boolean; error?: string; redirectTo?: string }>;
  logout: () => Promise<void>;
  register: (userData: UserCreate) => Promise<{ success: boolean; error?: string }>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  
  // Utilities
  getRedirectPath: (role: UserRole) => string;
  hasRole: (role: UserRole) => boolean;
  isAdmin: () => boolean;
  isDealership: () => boolean;
  isCustomer: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authService.login(credentials);
          
          if (response.success && response.data) {
            const user = response.data.user;
            set({ 
              user, 
              isAuthenticated: true, 
              isLoading: false,
              error: null 
            });
            
            const redirectTo = get().getRedirectPath(user.role);
            return { success: true, redirectTo };
          } else {
            const errorMessage = response.error?.message || 'Login failed';
            set({ 
              isLoading: false, 
              error: errorMessage,
              isAuthenticated: false,
              user: null 
            });
            return { success: false, error: errorMessage };
          }
        } catch (error: any) {
          const errorMessage = error.message || 'An unexpected error occurred';
          set({ 
            isLoading: false, 
            error: errorMessage,
            isAuthenticated: false,
            user: null 
          });
          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        set({ isLoading: true });
        
        try {
          await authService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: null 
          });
        }
      },

      register: async (userData: UserCreate) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authService.register(userData);
          
          if (response.success) {
            set({ isLoading: false, error: null });
            return { success: true };
          } else {
            const errorMessage = response.error?.message || 'Registration failed';
            set({ isLoading: false, error: errorMessage });
            return { success: false, error: errorMessage };
          }
        } catch (error: any) {
          const errorMessage = error.message || 'An unexpected error occurred';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      refreshUser: async () => {
        if (!authService.isAuthenticated()) {
          set({ user: null, isAuthenticated: false });
          return;
        }

        try {
          const response = await authService.getCurrentUser();
          
          if (response.success && response.data) {
            set({ 
              user: response.data, 
              isAuthenticated: true,
              error: null 
            });
          } else {
            // Token might be invalid, clear auth state
            set({ 
              user: null, 
              isAuthenticated: false,
              error: 'Session expired' 
            });
          }
        } catch (error) {
          console.error('Failed to refresh user:', error);
          set({ 
            user: null, 
            isAuthenticated: false,
            error: 'Failed to refresh user session' 
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Utilities
      getRedirectPath: (role: UserRole): string => {
        switch (role) {
          case UserRole.ADMIN:
            return '/admin';
          case UserRole.DEALERSHIP:
            return '/painel';
          case UserRole.CUSTOMER:
            return '/';
          default:
            return '/';
        }
      },

      hasRole: (role: UserRole): boolean => {
        const { user } = get();
        return user?.role === role;
      },

      isAdmin: (): boolean => {
        return get().hasRole(UserRole.ADMIN);
      },

      isDealership: (): boolean => {
        return get().hasRole(UserRole.DEALERSHIP);
      },

      isCustomer: (): boolean => {
        return get().hasRole(UserRole.CUSTOMER);
      },
    }),
    {
      name: 'autoparts-auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Initialize auth state on app load
export const initializeAuth = async () => {
  const { refreshUser, setLoading } = useAuthStore.getState();
  
  setLoading(true);
  
  try {
    if (authService.isAuthenticated()) {
      await refreshUser();
    }
  } catch (error) {
    console.error('Failed to initialize auth:', error);
  } finally {
    setLoading(false);
  }
};

// Set up automatic token refresh
let refreshInterval: NodeJS.Timeout | null = null;

export const startTokenRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
  
  refreshInterval = setInterval(async () => {
    const { isAuthenticated, refreshUser } = useAuthStore.getState();
    
    if (isAuthenticated && authService.isAuthenticated()) {
      try {
        await refreshUser();
      } catch (error) {
        console.error('Token refresh failed:', error);
      }
    }
  }, 15 * 60 * 1000); // Refresh every 15 minutes
};

export const stopTokenRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
};