/**
 * Inventory API Service
 * Handles all inventory management related API calls
 */

import { apiClient } from './client';
import {
  ApiResponse,
  Inventory,
  InventoryCreate,
  InventoryUpdate,
  InventoryCondition,
  PaginatedResponse
} from '../types/api';

export interface InventorySearchParams {
  part_number?: string;
  brand?: string;
  category?: string;
  dealership_id?: string;
  condition?: InventoryCondition;
  min_price?: number;
  max_price?: number;
  available_only?: boolean;
  page?: number;
  size?: number;
}

export interface InventoryStats {
  total_items: number;
  total_value: number;
  low_stock_items: number;
  out_of_stock_items: number;
  by_condition: Record<InventoryCondition, number>;
  by_category: Record<string, number>;
}

export class InventoryService {
  /**
   * Search inventory with filters and pagination
   */
  async searchInventory(searchParams: InventorySearchParams): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    const params = new URLSearchParams();
    
    if (searchParams.part_number) params.append('part_number', searchParams.part_number);
    if (searchParams.brand) params.append('brand', searchParams.brand);
    if (searchParams.category) params.append('category', searchParams.category);
    if (searchParams.dealership_id) params.append('dealership_id', searchParams.dealership_id);
    if (searchParams.condition) params.append('condition', searchParams.condition);
    if (searchParams.min_price) params.append('min_price', searchParams.min_price.toString());
    if (searchParams.max_price) params.append('max_price', searchParams.max_price.toString());
    if (searchParams.available_only) params.append('available_only', searchParams.available_only.toString());
    if (searchParams.page) params.append('page', searchParams.page.toString());
    if (searchParams.size) params.append('size', searchParams.size.toString());

    return apiClient.request<PaginatedResponse<Inventory>>({
      method: 'GET',
      url: `/inventory/search?${params.toString()}`
    });
  }

  /**
   * Get inventory item by ID
   */
  async getInventoryById(inventoryId: string): Promise<ApiResponse<Inventory>> {
    return apiClient.request<Inventory>({
      method: 'GET',
      url: `/inventory/${inventoryId}`
    });
  }

  /**
   * Create new inventory item
   */
  async createInventory(inventoryData: InventoryCreate): Promise<ApiResponse<Inventory>> {
    return apiClient.request<Inventory>({
      method: 'POST',
      url: '/inventory',
      data: inventoryData
    });
  }

  /**
   * Update inventory item
   */
  async updateInventory(inventoryId: string, inventoryData: InventoryUpdate): Promise<ApiResponse<Inventory>> {
    return apiClient.request<Inventory>({
      method: 'PUT',
      url: `/inventory/${inventoryId}`,
      data: inventoryData
    });
  }

  /**
   * Delete inventory item
   */
  async deleteInventory(inventoryId: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'DELETE',
      url: `/inventory/${inventoryId}`
    });
  }

  /**
   * Get all inventory for current dealership
   */
  async getMyInventory(page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    return apiClient.request<PaginatedResponse<Inventory>>({
      method: 'GET',
      url: `/inventory/my?page=${page}&size=${size}`
    });
  }

  /**
   * Get inventory by dealership ID
   */
  async getInventoryByDealership(dealershipId: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    return apiClient.request<PaginatedResponse<Inventory>>({
      method: 'GET',
      url: `/inventory/dealership/${dealershipId}?page=${page}&size=${size}`
    });
  }

  /**
   * Get inventory for specific part
   */
  async getInventoryByPart(partId: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    return apiClient.request<PaginatedResponse<Inventory>>({
      method: 'GET',
      url: `/inventory/part/${partId}?page=${page}&size=${size}`
    });
  }

  /**
   * Get inventory statistics
   */
  async getInventoryStats(dealershipId?: string): Promise<ApiResponse<InventoryStats>> {
    const url = dealershipId 
      ? `/inventory/stats?dealership_id=${dealershipId}`
      : '/inventory/stats';
    
    return apiClient.request<InventoryStats>({
      method: 'GET',
      url
    });
  }

  /**
   * Get low stock items
   */
  async getLowStockItems(threshold = 5, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    return apiClient.request<PaginatedResponse<Inventory>>({
      method: 'GET',
      url: `/inventory/low-stock?threshold=${threshold}&page=${page}&size=${size}`
    });
  }

  /**
   * Get out of stock items
   */
  async getOutOfStockItems(page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    return apiClient.request<PaginatedResponse<Inventory>>({
      method: 'GET',
      url: `/inventory/out-of-stock?page=${page}&size=${size}`
    });
  }

  /**
   * Bulk update inventory
   */
  async bulkUpdateInventory(updates: Array<{ id: string; data: InventoryUpdate }>): Promise<ApiResponse<{ updated: number; errors: any[] }>> {
    return apiClient.request<{ updated: number; errors: any[] }>({
      method: 'PUT',
      url: '/inventory/bulk',
      data: { updates }
    });
  }

  /**
   * Bulk create inventory
   */
  async bulkCreateInventory(items: InventoryCreate[]): Promise<ApiResponse<{ created: number; errors: any[] }>> {
    return apiClient.request<{ created: number; errors: any[] }>({
      method: 'POST',
      url: '/inventory/bulk',
      data: { items }
    });
  }

  /**
   * Export inventory to CSV
   */
  async exportInventory(dealershipId?: string): Promise<ApiResponse<{ download_url: string }>> {
    const url = dealershipId 
      ? `/inventory/export?dealership_id=${dealershipId}`
      : '/inventory/export';
    
    return apiClient.request<{ download_url: string }>({
      method: 'GET',
      url
    });
  }

  /**
   * Update inventory availability
   */
  async updateAvailability(inventoryId: string, isAvailable: boolean): Promise<ApiResponse<Inventory>> {
    return apiClient.request<Inventory>({
      method: 'PATCH',
      url: `/inventory/${inventoryId}/availability`,
      data: { is_available: isAvailable }
    });
  }

  /**
   * Update inventory quantity
   */
  async updateQuantity(inventoryId: string, quantity: number): Promise<ApiResponse<Inventory>> {
    return apiClient.request<Inventory>({
      method: 'PATCH',
      url: `/inventory/${inventoryId}/quantity`,
      data: { quantity }
    });
  }

  /**
   * Update inventory price
   */
  async updatePrice(inventoryId: string, price: number): Promise<ApiResponse<Inventory>> {
    return apiClient.request<Inventory>({
      method: 'PATCH',
      url: `/inventory/${inventoryId}/price`,
      data: { price }
    });
  }

  /**
   * Bulk import inventory from file
   */
  async bulkImport(file: File): Promise<ApiResponse<{ job_id: string; message: string }>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('import_type', 'inventory');

    return apiClient.request<{ job_id: string; message: string }>({
      method: 'POST',
      url: '/imports/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * Get import job status
   */
  async getImportJobStatus(jobId: string): Promise<ApiResponse<any>> {
    return apiClient.request<any>({
      method: 'GET',
      url: `/imports/jobs/${jobId}`
    });
  }

  /**
   * Download import template
   */
  async downloadImportTemplate(): Promise<Blob> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/imports/template/inventory`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiClient.getAccessToken()}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to download template');
    }

    return response.blob();
  }
}

// Export singleton instance
export const inventoryService = new InventoryService();
export default inventoryService;
