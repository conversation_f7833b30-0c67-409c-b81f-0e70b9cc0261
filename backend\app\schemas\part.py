"""
Pydantic schemas for parts management.
"""
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
from uuid import UUID
import re


class PartBase(BaseModel):
    """Base part schema."""
    code: str = Field(..., min_length=1, max_length=100, description="Original part code/number")
    name: str = Field(..., min_length=2, max_length=255, description="Part name")
    description: Optional[str] = Field(None, description="Detailed part description")
    category: Optional[str] = Field(None, max_length=100, description="Part category")
    subcategory: Optional[str] = Field(None, max_length=100, description="Part subcategory")
    brand: Optional[str] = Field(None, max_length=50, description="Vehicle brand")
    model: Optional[str] = Field(None, max_length=100, description="Vehicle model")
    year: Optional[int] = Field(None, ge=1900, le=2030, description="Vehicle year")
    year_start: Optional[int] = Field(None, ge=1900, le=2030, description="Starting year for compatibility")
    year_end: Optional[int] = Field(None, ge=1900, le=2030, description="Ending year for compatibility")
    oem_code: Optional[str] = Field(None, max_length=100, description="OEM code")
    alternative_codes: Optional[str] = Field(None, description="Alternative part codes (comma-separated)")
    weight: Optional[str] = Field(None, max_length=20, description="Part weight")
    dimensions: Optional[str] = Field(None, max_length=100, description="Part dimensions")

    @validator('code')
    def validate_code(cls, v):
        """Validate part code format."""
        if not v or not v.strip():
            raise ValueError('Part code cannot be empty')
        # Remove extra spaces and normalize
        return v.strip().upper()

    @validator('year_start', 'year_end')
    def validate_years(cls, v):
        """Validate year range."""
        if v is not None and (v < 1900 or v > 2030):
            raise ValueError('Year must be between 1900 and 2030')
        return v

    @validator('year_end')
    def validate_year_range(cls, v, values):
        """Validate that year_end is not before year_start."""
        if v is not None and 'year_start' in values and values['year_start'] is not None:
            if v < values['year_start']:
                raise ValueError('End year cannot be before start year')
        return v

    @validator('alternative_codes')
    def validate_alternative_codes(cls, v):
        """Validate alternative codes format."""
        if v is None:
            return v
        # Clean up the codes - remove extra spaces, normalize
        codes = [code.strip().upper() for code in v.split(',') if code.strip()]
        return ','.join(codes) if codes else None

    @validator('brand', 'model')
    def normalize_text_fields(cls, v):
        """Normalize text fields."""
        if v is None:
            return v
        return v.strip().title()


class PartCreate(PartBase):
    """Schema for creating a new part."""
    pass


class PartUpdate(BaseModel):
    """Schema for updating part information."""
    name: Optional[str] = Field(None, min_length=2, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    brand: Optional[str] = Field(None, max_length=50)
    model: Optional[str] = Field(None, max_length=100)
    year: Optional[int] = Field(None, ge=1900, le=2030)
    year_start: Optional[int] = Field(None, ge=1900, le=2030)
    year_end: Optional[int] = Field(None, ge=1900, le=2030)
    oem_code: Optional[str] = Field(None, max_length=100)
    alternative_codes: Optional[str] = None
    weight: Optional[str] = Field(None, max_length=20)
    dimensions: Optional[str] = Field(None, max_length=100)

    @validator('alternative_codes')
    def validate_alternative_codes(cls, v):
        """Validate alternative codes format."""
        if v is None:
            return v
        codes = [code.strip().upper() for code in v.split(',') if code.strip()]
        return ','.join(codes) if codes else None


class PartInDB(PartBase):
    """Schema for part in database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Part(PartInDB):
    """Public part schema."""
    pass


class PartSummary(BaseModel):
    """Summary schema for part listings."""
    id: UUID
    code: str
    name: str
    brand: Optional[str] = None
    model: Optional[str] = None
    year: Optional[int] = None
    category: Optional[str] = None
    
    class Config:
        from_attributes = True


class PartWithInventory(Part):
    """Part schema with inventory information."""
    inventory_count: int = 0
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    total_quantity: int = 0
    dealership_count: int = 0


class PartListResponse(BaseModel):
    """Response schema for part listing."""
    parts: List[PartSummary]
    total: int
    page: int
    per_page: int
    pages: int


class PartSearchFilters(BaseModel):
    """Schema for part search filters."""
    search: Optional[str] = Field(None, description="Search term for code, name, or description")
    code: Optional[str] = Field(None, description="Exact part code")
    brand: Optional[str] = Field(None, description="Filter by brand")
    model: Optional[str] = Field(None, description="Filter by model")
    year: Optional[int] = Field(None, ge=1900, le=2030, description="Filter by year")
    year_start: Optional[int] = Field(None, ge=1900, le=2030, description="Filter by year range start")
    year_end: Optional[int] = Field(None, ge=1900, le=2030, description="Filter by year range end")
    category: Optional[str] = Field(None, description="Filter by category")
    subcategory: Optional[str] = Field(None, description="Filter by subcategory")
    has_inventory: bool = Field(False, description="Only show parts with available inventory")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: str = Field("name", description="Sort field")
    sort_order: str = Field("asc", description="Sort order (asc/desc)")

    @validator('sort_by')
    def validate_sort_by(cls, v):
        """Validate sort field."""
        valid_fields = ['name', 'code', 'brand', 'model', 'year', 'category', 'created_at']
        if v not in valid_fields:
            raise ValueError(f'Sort field must be one of: {", ".join(valid_fields)}')
        return v

    @validator('sort_order')
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v.lower() not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v.lower()


class PartSearchSuggestion(BaseModel):
    """Schema for search suggestions."""
    code: str
    name: str
    brand: Optional[str] = None
    model: Optional[str] = None
    similarity_score: float


class PartSearchResponse(BaseModel):
    """Response schema for part search."""
    parts: List[PartWithInventory]
    total: int
    page: int
    per_page: int
    pages: int
    suggestions: List[PartSearchSuggestion] = []
    search_time_ms: float


class PartStats(BaseModel):
    """Schema for part statistics."""
    total_parts: int
    parts_with_inventory: int
    parts_by_category: List[dict]
    parts_by_brand: List[dict]
    recent_additions: int


class PartCompatibility(BaseModel):
    """Schema for part compatibility information."""
    part_id: UUID
    compatible_vehicles: List[dict]
    alternative_parts: List[PartSummary]
    cross_references: List[str]


class BulkPartCreate(BaseModel):
    """Schema for bulk part creation."""
    parts: List[PartCreate]
    skip_duplicates: bool = Field(True, description="Skip parts with duplicate codes")
    update_existing: bool = Field(False, description="Update existing parts with same code")


class BulkPartResponse(BaseModel):
    """Response schema for bulk part operations."""
    created: int
    updated: int
    skipped: int
    errors: List[dict]
    total_processed: int
