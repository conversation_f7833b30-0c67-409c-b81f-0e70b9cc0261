"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
    to: new Date(),
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Relatórios</h1>
          <p className="text-muted-foreground">
            Visualize e analise dados importantes do seu negócio
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            Exportar PDF
          </Button>
          <Button>
            Atualizar Dados
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Relatórios em Desenvolvimento</CardTitle>
          <CardDescription>
            Esta página está sendo desenvolvida com integração à API real.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>Os relatórios serão implementados com dados reais da API.</p>
        </CardContent>
      </Card>
    </div>
  )
}
