"""
Part service for business logic operations.
"""
from typing import Op<PERSON>, List, Tuple, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import time
import logging

from app.models.part import Part
from app.repositories.part import PartRepository
from app.schemas.part import (
    PartCreate,
    PartUpdate,
    PartSearchFilters,
    PartSearchResponse,
    PartWithInventory,
    PartSearchSuggestion,
    PartStats,
    BulkPartCreate,
    BulkPartResponse
)
from app.core.cache_decorators import cached, cache_invalidate_pattern, CacheService
from app.core.cache import CacheTTL, CacheKeys

logger = logging.getLogger(__name__)


class PartService:
    """
    Service class for part management operations.
    """

    def __init__(self, db: Session):
        self.db = db
        self.part_repo = PartRepository(db)

    @cache_invalidate_pattern("parts:*")
    def create_part(self, part_data: PartCreate) -> Part:
        """
        Create a new part.
        
        Args:
            part_data: Part creation data
            
        Returns:
            Created part instance
            
        Raises:
            ValueError: If part code already exists
        """
        # Check if part code already exists
        existing_part = self.part_repo.get_by_code(part_data.code)
        if existing_part:
            raise ValueError(f"Part with code '{part_data.code}' already exists")
        
        # Create part
        part_dict = part_data.dict()
        part = self.part_repo.create(part_dict)
        
        logger.info(f"Created part {part.id} with code {part.code}")
        return part

    @cache_invalidate_pattern("parts:*")
    def update_part(self, part_id: UUID, update_data: PartUpdate) -> Optional[Part]:
        """
        Update part information.
        
        Args:
            part_id: Part UUID
            update_data: Updated part data
            
        Returns:
            Updated part instance or None if not found
        """
        update_dict = update_data.dict(exclude_unset=True)
        part = self.part_repo.update(part_id, update_dict)
        
        if part:
            logger.info(f"Updated part {part_id}")
        
        return part

    @cached(
        key_prefix=CacheKeys.PART_DETAILS,
        ttl=CacheTTL.PART_DETAILS,
        key_params=['part_id']
    )
    def get_part_by_id(self, part_id: UUID) -> Optional[Part]:
        """
        Get part by ID.
        
        Args:
            part_id: Part UUID
            
        Returns:
            Part instance or None if not found
        """
        return self.part_repo.get_by_id(part_id)

    def get_part_by_code(self, code: str) -> Optional[Part]:
        """
        Get part by code.
        
        Args:
            code: Part code
            
        Returns:
            Part instance or None if not found
        """
        return self.part_repo.get_by_code(code)

    @cached(
        key_prefix=CacheKeys.PARTS_SEARCH,
        ttl=CacheTTL.PARTS_SEARCH,
        key_params=['search', 'code', 'brand', 'model', 'year', 'category', 'page', 'per_page']
    )
    def search_parts(self, filters: PartSearchFilters) -> PartSearchResponse:
        """
        Search parts with advanced filtering and suggestions.
        
        Args:
            filters: Search filters
            
        Returns:
            Search response with parts and suggestions
        """
        start_time = time.time()
        
        skip = (filters.page - 1) * filters.per_page
        
        # Perform search
        if filters.search and len(filters.search) > 2:
            # Use full-text search for better results
            parts, total = self.part_repo.full_text_search(
                search_term=filters.search,
                skip=skip,
                limit=filters.per_page
            )
        else:
            # Use regular search with filters
            parts, total = self.part_repo.search_parts(
                search_term=filters.search,
                code=filters.code,
                brand=filters.brand,
                model=filters.model,
                year=filters.year,
                year_start=filters.year_start,
                year_end=filters.year_end,
                category=filters.category,
                subcategory=filters.subcategory,
                has_inventory=filters.has_inventory,
                skip=skip,
                limit=filters.per_page,
                sort_by=filters.sort_by,
                sort_order=filters.sort_order
            )
        
        # Get parts with inventory information
        parts_with_inventory = []
        for part in parts:
            inventory_stats = self.part_repo.get_parts_with_inventory_stats(
                skip=0, limit=1
            )
            
            # Find stats for this part
            part_stats = next(
                (stats for stats in inventory_stats if stats['part'].id == part.id),
                {
                    'inventory_count': 0,
                    'min_price': None,
                    'max_price': None,
                    'total_quantity': 0,
                    'dealership_count': 0
                }
            )
            
            part_with_inventory = PartWithInventory(
                **part.__dict__,
                inventory_count=part_stats['inventory_count'],
                min_price=part_stats['min_price'],
                max_price=part_stats['max_price'],
                total_quantity=part_stats['total_quantity'],
                dealership_count=part_stats['dealership_count']
            )
            parts_with_inventory.append(part_with_inventory)
        
        # Generate suggestions if no results or few results
        suggestions = []
        if total < 5 and filters.search:
            suggestions = self._generate_search_suggestions(filters.search)
        
        search_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        pages = (total + filters.per_page - 1) // filters.per_page
        
        return PartSearchResponse(
            parts=parts_with_inventory,
            total=total,
            page=filters.page,
            per_page=filters.per_page,
            pages=pages,
            suggestions=suggestions,
            search_time_ms=round(search_time, 2)
        )

    def get_similar_parts(self, part_id: UUID, limit: int = 10) -> List[Part]:
        """
        Get similar parts.
        
        Args:
            part_id: Reference part ID
            limit: Maximum number of similar parts
            
        Returns:
            List of similar parts
        """
        return self.part_repo.get_similar_parts(part_id, limit)

    def get_parts_by_alternative_code(self, alternative_code: str) -> List[Part]:
        """
        Get parts by alternative code.
        
        Args:
            alternative_code: Alternative part code
            
        Returns:
            List of parts with matching alternative code
        """
        return self.part_repo.get_parts_by_alternative_code(alternative_code)

    def get_part_stats(self) -> PartStats:
        """
        Get part statistics.
        
        Returns:
            Part statistics
        """
        # Total parts
        total = self.part_repo.count()
        
        # Parts with inventory
        parts_with_inventory = self.part_repo.count({'has_inventory': True})
        
        # Parts by category
        category_counts = self.part_repo.count_by_category()
        parts_by_category = [
            {'category': category or 'Uncategorized', 'count': count}
            for category, count in category_counts[:10]  # Top 10 categories
        ]
        
        # Parts by brand
        brand_counts = self.part_repo.count_by_brand()
        parts_by_brand = [
            {'brand': brand or 'Unknown', 'count': count}
            for brand, count in brand_counts[:10]  # Top 10 brands
        ]
        
        # Recent additions (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent = self.db.query(Part).filter(
            Part.created_at >= thirty_days_ago
        ).count()
        
        return PartStats(
            total_parts=total,
            parts_with_inventory=parts_with_inventory,
            parts_by_category=parts_by_category,
            parts_by_brand=parts_by_brand,
            recent_additions=recent
        )

    def bulk_create_parts(self, bulk_data: BulkPartCreate) -> BulkPartResponse:
        """
        Create multiple parts in bulk.
        
        Args:
            bulk_data: Bulk creation data
            
        Returns:
            Bulk operation response
        """
        created = 0
        updated = 0
        skipped = 0
        errors = []
        
        for i, part_data in enumerate(bulk_data.parts):
            try:
                existing_part = self.part_repo.get_by_code(part_data.code)
                
                if existing_part:
                    if bulk_data.update_existing:
                        # Update existing part
                        update_data = PartUpdate(**part_data.dict(exclude={'code'}))
                        self.update_part(existing_part.id, update_data)
                        updated += 1
                    elif bulk_data.skip_duplicates:
                        # Skip duplicate
                        skipped += 1
                    else:
                        # Report error
                        errors.append({
                            'index': i,
                            'code': part_data.code,
                            'error': f'Part with code {part_data.code} already exists'
                        })
                else:
                    # Create new part
                    self.create_part(part_data)
                    created += 1
                    
            except Exception as e:
                errors.append({
                    'index': i,
                    'code': part_data.code if hasattr(part_data, 'code') else 'Unknown',
                    'error': str(e)
                })
        
        logger.info(f"Bulk operation completed: {created} created, {updated} updated, {skipped} skipped, {len(errors)} errors")
        
        return BulkPartResponse(
            created=created,
            updated=updated,
            skipped=skipped,
            errors=errors,
            total_processed=len(bulk_data.parts)
        )

    def delete_part(self, part_id: UUID) -> bool:
        """
        Delete a part.
        
        Args:
            part_id: Part UUID
            
        Returns:
            True if deleted successfully, False if not found
        """
        success = self.part_repo.delete(part_id)
        
        if success:
            logger.info(f"Deleted part {part_id}")
        
        return success

    def _generate_search_suggestions(self, search_term: str) -> List[PartSearchSuggestion]:
        """
        Generate search suggestions based on search term.
        
        Args:
            search_term: Original search term
            
        Returns:
            List of search suggestions
        """
        suggestions = []
        
        # Try to find similar codes
        similar_parts, _ = self.part_repo.search_parts(
            search_term=search_term,
            limit=5
        )
        
        for part in similar_parts:
            # Calculate simple similarity score based on string matching
            similarity = self._calculate_similarity(search_term, part.code)
            if similarity > 0.3:  # Minimum similarity threshold
                suggestions.append(PartSearchSuggestion(
                    code=part.code,
                    name=part.name,
                    brand=part.brand,
                    model=part.model,
                    similarity_score=similarity
                ))
        
        # Sort by similarity score
        suggestions.sort(key=lambda x: x.similarity_score, reverse=True)
        
        return suggestions[:5]  # Return top 5 suggestions

    def _calculate_similarity(self, term1: str, term2: str) -> float:
        """
        Calculate simple similarity score between two strings.
        
        Args:
            term1: First string
            term2: Second string
            
        Returns:
            Similarity score between 0 and 1
        """
        if not term1 or not term2:
            return 0.0
        
        term1 = term1.lower()
        term2 = term2.lower()
        
        # Simple character-based similarity
        if term1 == term2:
            return 1.0
        
        # Check if one contains the other
        if term1 in term2 or term2 in term1:
            return 0.8
        
        # Calculate character overlap
        set1 = set(term1)
        set2 = set(term2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
