"use client"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Clock, Download, Edit, FileText, Mail, MapPin, Phone, RefreshCw, User } from "lucide-react"
import Link from "next/link"

// Dados simulados de concessionária
const dealershipData = {
  id: "1",
  name: "AutoPeças Premium",
  tradingName: "AutoPeças Premium LTDA",
  cnpj: "12.345.678/0001-90",
  ie: "123456789",
  responsible: "<PERSON>",
  email: "<EMAIL>",
  phone: "(11) 3333-4444",
  whatsapp: "+5511999999999",
  address: "Av. Paul<PERSON>, 1000",
  neighborhood: "Bela Vista",
  city: "São Paulo",
  state: "SP",
  zipCode: "01310-100",
  status: "active",
  stockItems: 1248,
  lastStockUpdate: "2023-10-15T14:30:00",
  lastLogin: "2023-10-18T09:15:00",
  plan: "premium",
  planStartDate: "2023-01-01",
  planEndDate: "2023-12-31",
  createdAt: "2022-05-10T10:00:00",
  description: "Concessionária especializada em peças originais Toyota, Honda e Nissan.",
  openingHours: "Segunda a Sexta: 8h às 18h | Sábado: 8h às 13h",
  logoUrl: "/placeholder.svg?height=100&width=100",
  users: [
    {
      id: "1",
      name: "João Silva",
      email: "<EMAIL>",
      role: "admin",
      lastLogin: "2023-10-18T09:15:00",
    },
    {
      id: "2",
      name: "Maria Souza",
      email: "<EMAIL>",
      role: "manager",
      lastLogin: "2023-10-17T14:30:00",
    },
    {
      id: "3",
      name: "Pedro Santos",
      email: "<EMAIL>",
      role: "staff",
      lastLogin: "2023-10-16T11:45:00",
    },
  ],
  invoices: [
    {
      id: "INV-001",
      date: "2023-10-01",
      dueDate: "2023-10-15",
      amount: 1250.9,
      status: "paid",
      paidDate: "2023-10-10",
    },
    {
      id: "INV-002",
      date: "2023-09-01",
      dueDate: "2023-09-15",
      amount: 1250.9,
      status: "paid",
      paidDate: "2023-09-12",
    },
    {
      id: "INV-003",
      date: "2023-08-01",
      dueDate: "2023-08-15",
      amount: 1250.9,
      status: "paid",
      paidDate: "2023-08-10",
    },
  ],
  stockUpdates: [
    {
      id: "1",
      date: "2023-10-15T14:30:00",
      itemsCount: 1248,
      user: "João Silva",
    },
    {
      id: "2",
      date: "2023-10-01T10:15:00",
      itemsCount: 1200,
      user: "Maria Souza",
    },
    {
      id: "3",
      date: "2023-09-15T09:45:00",
      itemsCount: 1150,
      user: "Pedro Santos",
    },
  ],
  loginHistory: [
    {
      id: "1",
      date: "2023-10-18T09:15:00",
      user: "João Silva",
      ip: "***********",
    },
    {
      id: "2",
      date: "2023-10-17T14:30:00",
      user: "Maria Souza",
      ip: "***********",
    },
    {
      id: "3",
      date: "2023-10-16T11:45:00",
      user: "Pedro Santos",
      ip: "***********",
    },
  ],
}

// Formatar status da concessionária
const formatStatus = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-green-500">Ativa</Badge>
    case "inactive":
      return <Badge variant="outline">Inativa</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Formatar status da fatura
const formatInvoiceStatus = (status: string) => {
  switch (status) {
    case "paid":
      return <Badge className="bg-green-500">Paga</Badge>
    case "pending":
      return <Badge className="bg-yellow-500">Pendente</Badge>
    case "overdue":
      return <Badge className="bg-red-500">Vencida</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Formatar plano
const formatPlan = (plan: string) => {
  switch (plan) {
    case "basic":
      return <Badge variant="outline">Básico</Badge>
    case "standard":
      return <Badge className="bg-blue-500">Padrão</Badge>
    case "premium":
      return <Badge className="bg-purple-500">Premium</Badge>
    default:
      return <Badge variant="outline">{plan}</Badge>
  }
}

// Formatar data relativa
const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return "agora mesmo"
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `há ${minutes} ${minutes === 1 ? "minuto" : "minutos"}`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `há ${hours} ${hours === 1 ? "hora" : "horas"}`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `há ${days} ${days === 1 ? "dia" : "dias"}`
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000)
    return `há ${months} ${months === 1 ? "mês" : "meses"}`
  } else {
    const years = Math.floor(diffInSeconds / 31536000)
    return `há ${years} ${years === 1 ? "ano" : "anos"}`
  }
}

export default function DealershipDetailsPage() {
  const params = useParams()
  const dealershipId = params.id as string

  // Em uma aplicação real, você buscaria os dados da concessionária com base no ID
  const dealership = dealershipData

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/concessionarias">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{dealership.name}</h1>
            <p className="text-muted-foreground">
              {dealership.city}/{dealership.state} • {formatStatus(dealership.status)}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Editar
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Informações Gerais</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="h-16 w-16 overflow-hidden rounded-lg border">
                <img src={dealership.logoUrl || "/placeholder.svg"} alt="Logo" className="h-full w-full object-cover" />
              </div>
              <div>
                <div className="font-medium">{dealership.tradingName}</div>
                <div className="text-sm text-muted-foreground">CNPJ: {dealership.cnpj}</div>
                <div className="text-sm text-muted-foreground">IE: {dealership.ie}</div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <User className="mt-0.5 h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Responsável</div>
                  <div className="text-sm text-muted-foreground">{dealership.responsible}</div>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Mail className="mt-0.5 h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Email</div>
                  <div className="text-sm text-muted-foreground">{dealership.email}</div>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Phone className="mt-0.5 h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Telefone</div>
                  <div className="text-sm text-muted-foreground">{dealership.phone}</div>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <MapPin className="mt-0.5 h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Endereço</div>
                  <div className="text-sm text-muted-foreground">
                    {dealership.address}, {dealership.neighborhood}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {dealership.city}/{dealership.state} - CEP: {dealership.zipCode}
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Clock className="mt-0.5 h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Horário de Funcionamento</div>
                  <div className="text-sm text-muted-foreground">{dealership.openingHours}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Plano e Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Plano Atual</div>
              <div>{formatPlan(dealership.plan)}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Status</div>
              <div>{formatStatus(dealership.status)}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Início do Plano</div>
              <div className="text-sm">{new Date(dealership.planStartDate).toLocaleDateString()}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Término do Plano</div>
              <div className="text-sm">{new Date(dealership.planEndDate).toLocaleDateString()}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Data de Cadastro</div>
              <div className="text-sm">{new Date(dealership.createdAt).toLocaleDateString()}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Último Login</div>
              <div className="text-sm">{formatRelativeTime(dealership.lastLogin)}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Última Atualização de Estoque</div>
              <div className="text-sm">{formatRelativeTime(dealership.lastStockUpdate)}</div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">Total de Itens em Estoque</div>
              <div className="text-sm">{dealership.stockItems} itens</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Últimas Faturas</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {dealership.invoices.map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between rounded-lg border p-3">
                <div>
                  <div className="font-medium">{invoice.id}</div>
                  <div className="text-sm text-muted-foreground">
                    Vencimento: {new Date(invoice.dueDate).toLocaleDateString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">
                    {new Intl.NumberFormat("pt-BR", {
                      style: "currency",
                      currency: "BRL",
                    }).format(invoice.amount)}
                  </div>
                  <div className="flex items-center justify-end gap-1">
                    {formatInvoiceStatus(invoice.status)}
                    {invoice.status === "paid" && (
                      <span className="text-xs text-muted-foreground">
                        em {new Date(invoice.paidDate!).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <Button variant="outline" className="w-full">
              <FileText className="mr-2 h-4 w-4" />
              Ver Todas as Faturas
            </Button>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="users">
        <TabsList>
          <TabsTrigger value="users">Usuários</TabsTrigger>
          <TabsTrigger value="stock">Histórico de Estoque</TabsTrigger>
          <TabsTrigger value="login">Histórico de Login</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Usuários da Concessionária</CardTitle>
              <CardDescription>Lista de usuários com acesso ao painel da concessionária</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Função</TableHead>
                    <TableHead>Último Login</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dealership.users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {user.role === "admin" && <Badge className="bg-purple-500">Administrador</Badge>}
                        {user.role === "manager" && <Badge className="bg-blue-500">Gerente</Badge>}
                        {user.role === "staff" && <Badge variant="outline">Funcionário</Badge>}
                      </TableCell>
                      <TableCell>{formatRelativeTime(user.lastLogin)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stock" className="mt-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Histórico de Atualizações de Estoque</CardTitle>
              <CardDescription>Registro de atualizações de estoque da concessionária</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Usuário</TableHead>
                    <TableHead>Quantidade de Itens</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dealership.stockUpdates.map((update) => (
                    <TableRow key={update.id}>
                      <TableCell>
                        <div>{new Date(update.date).toLocaleDateString()}</div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(update.date).toLocaleTimeString()}
                        </div>
                      </TableCell>
                      <TableCell>{update.user}</TableCell>
                      <TableCell>{update.itemsCount} itens</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Exportar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="login" className="mt-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Histórico de Login</CardTitle>
              <CardDescription>Registro de acessos à plataforma</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Usuário</TableHead>
                    <TableHead>IP</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dealership.loginHistory.map((login) => (
                    <TableRow key={login.id}>
                      <TableCell>
                        <div>{new Date(login.date).toLocaleDateString()}</div>
                        <div className="text-xs text-muted-foreground">{new Date(login.date).toLocaleTimeString()}</div>
                      </TableCell>
                      <TableCell>{login.user}</TableCell>
                      <TableCell>{login.ip}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

