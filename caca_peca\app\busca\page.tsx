"use client"

import { useState, useEffect, useMemo } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { SearchBar } from "@/components/search-bar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  ChevronDown,
  ChevronUp,
  Filter,
  Phone,
  X,
  Clock,
  ArrowDown,
  ArrowUp,
  CheckCircle,
  MessageCircle,
  Loader2,
  AlertCircle,
  Eye,
  Plus,
  Check,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { trackEvent } from "@/lib/analytics"
import { useMobile } from "@/hooks/use-mobile"
import { useInventorySearch, usePartBrands, useDealershipStates } from "@/lib/hooks/useApi"
import { usePartsSearch, usePartCategories } from "@/lib/hooks/useParts"
import { InventoryCondition, PartSearchRequest, PartSearchFilters } from "@/lib/types/api"
import { AdvancedFilters } from "@/components/parts/advanced-filters"
import { PartComparisonComponent } from "@/components/parts/part-comparison"

// Sample data
const CITIES = ["São Paulo", "Campinas", "Rio de Janeiro", "Belo Horizonte", "Curitiba", "Porto Alegre"]
const STATES = ["SP", "RJ", "MG", "PR", "RS", "SC"]
const STORES = ["AutoPeças Premium", "Center Parts", "Freios & Cia", "Amortex", "Sul Peças", "Paraná Autopeças"]

// Function to calculate days since last update
const getDaysSinceUpdate = (lastUpdateDate: string): number => {
  const lastUpdate = new Date(lastUpdateDate)
  const today = new Date()
  const diffTime = Math.abs(today.getTime() - lastUpdate.getTime())
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// Function to format the last update message
const formatLastUpdate = (lastUpdateDate: string): string => {
  const days = getDaysSinceUpdate(lastUpdateDate)

  if (days === 0) {
    return "hoje"
  } else if (days === 1) {
    return "há 1 dia"
  } else {
    return `há ${days} dias`
  }
}

// Function to get status color based on days since update
const getUpdateStatusColor = (days: number): string => {
  if (days <= 7) {
    return "text-green-600" // Recent update (within a week)
  } else if (days <= 30) {
    return "text-amber-600" // Moderate update (within a month)
  } else {
    return "text-red-600" // Old update (more than a month)
  }
}

// Sort options
type SortOption =
  | "relevance"
  | "price-asc"
  | "price-desc"
  | "name-asc"
  | "update-desc"
  | "stock-asc"
  | "stock-desc"
  | "store-asc"
  | "store-desc"
  | "brand-asc"
  | "brand-desc"

export default function SearchPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const query = searchParams.get("q") || ""
  const state = searchParams.get("state") || ""
  const brand = searchParams.get("brand") || ""
  const page = parseInt(searchParams.get("page") || "1")
  const size = parseInt(searchParams.get("size") || "20")

  // State for filters
  const [showPrices, setShowPrices] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [selectedCity, setSelectedCity] = useState("all")
  const [selectedState, setSelectedState] = useState(state)
  const [selectedStore, setSelectedStore] = useState("all")
  const [selectedCondition, setSelectedCondition] = useState<InventoryCondition | "all">("all")
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 2000])
  const [availableOnly, setAvailableOnly] = useState(true)

  // Advanced search state
  const [advancedFilters, setAdvancedFilters] = useState<PartSearchFilters>({
    page: 1,
    size: 20
  })

  // Comparison state
  const [comparisonParts, setComparisonParts] = useState<string[]>([])
  const [showComparison, setShowComparison] = useState(false)

  // Estado para rastrear quais números de telefone foram revelados
  const [revealedPhones, setRevealedPhones] = useState<Record<string, boolean>>({})

  // Sorting state
  const [sortOption, setSortOption] = useState<SortOption>("relevance")

  // Mobile hook
  const isMobile = useMobile()

  // API hooks
  const searchParams_api = useMemo(() => ({
    part_number: query,
    brand: brand || undefined,
    min_price: priceRange[0],
    max_price: priceRange[1],
    condition: selectedCondition !== "all" ? selectedCondition : undefined,
    available_only: availableOnly,
    page,
    size
  }), [query, brand, priceRange, selectedCondition, availableOnly, page, size])

  // Parts search for enhanced functionality
  const partsSearchParams = useMemo(() => ({
    query: query,
    part_number: query,
    brand: brand || undefined,
    page,
    size,
    sort_by: sortOption === 'price-asc' || sortOption === 'price-desc' ? 'price' as const :
             sortOption === 'name-asc' ? 'name' as const :
             sortOption === 'brand-asc' || sortOption === 'brand-desc' ? 'brand' as const : undefined,
    sort_order: sortOption.includes('-desc') ? 'desc' as const : 'asc' as const
  }), [query, brand, page, size, sortOption])

  const { data: searchResults, isLoading, error } = useInventorySearch(searchParams_api)
  const { data: partsSearchResults, isLoading: partsLoading } = usePartsSearch(partsSearchParams, !!query)
  const { data: brandsResponse } = usePartBrands()
  const { data: categoriesResponse } = usePartCategories()
  const { data: statesResponse } = useDealershipStates()

  // Extract data from API responses
  const results = useMemo(() => {
    if (searchResults?.data?.success && searchResults.data.data) {
      return searchResults.data.data
    }
    return { items: [], total: 0, page: 1, size: 20, pages: 0 }
  }, [searchResults])

  // Current date for demo purposes
  const currentDate = new Date()

  // Função para rastrear cliques no telefone
  const handlePhoneClick = (product: any) => {
    // Rastrear o evento
    trackEvent("phone_click", {
      productReference: product.reference,
      store: product.store,
      phone: product.phone,
    })

    // Marcar o telefone como revelado
    setRevealedPhones((prev) => ({
      ...prev,
      [product.reference]: true,
    }))

    // Abrir o telefone para ligação
    window.open(`tel:${product.phone.replace(/[^0-9]/g, "")}`, "_blank")
  }

  // Função para rastrear cliques no WhatsApp
  const handleWhatsAppClick = (product: any) => {
    trackEvent("whatsapp_click", {
      productReference: product.reference,
      store: product.store,
      whatsapp: product.whatsapp,
    })

    window.open(`https://wa.me/${product.whatsapp}`, "_blank")
  }

  // Dados simulados de produtos
  const products = [
    {
      reference: "7086701",
      description: "Kit de Embreagem Original",
      brand: "Fiat",
      price: 1250.9,
      stock: 3,
      store: "AutoPeças Premium",
      city: "São Paulo",
      state: "SP",
      whatsapp: "+5511999999999",
      phone: "(11) 3333-4444",
      lastUpdate: new Date(currentDate.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 2 days ago
    },
    {
      reference: "52089544",
      description: "Filtro de Óleo Motor 1.0",
      brand: "Volkswagen",
      price: 89.9,
      stock: 12,
      store: "Center Parts",
      city: "Campinas",
      state: "SP",
      whatsapp: "+5519999999999",
      phone: "(19) 2222-3333",
      lastUpdate: new Date(currentDate.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 5 days ago
    },
    {
      reference: "AX889922",
      description: "Pastilha de Freio Dianteira",
      brand: "Frasle",
      price: 129.9,
      stock: 8,
      store: "Freios & Cia",
      city: "Rio de Janeiro",
      state: "RJ",
      whatsapp: "+5521999999999",
      phone: "(21) 4444-5555",
      lastUpdate: new Date(currentDate.getTime() - 12 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 12 days ago
    },
    {
      reference: "BB445566",
      description: "Amortecedor Traseiro (Par)",
      brand: "Monroe",
      price: 459.9,
      stock: 4,
      store: "Amortex",
      city: "Belo Horizonte",
      state: "MG",
      whatsapp: "+5531999999999",
      phone: "(31) 5555-6666",
      lastUpdate: new Date(currentDate.getTime() - 20 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 20 days ago
    },
    {
      reference: "CC778899",
      description: "Vela de Ignição",
      brand: "NGK",
      price: 29.9,
      stock: 20,
      store: "Sul Peças",
      city: "Porto Alegre",
      state: "RS",
      whatsapp: "+5551999999999",
      phone: "(51) 7777-8888",
      lastUpdate: new Date(currentDate.getTime() - 0 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // Today
    },
    {
      reference: "DD112233",
      description: "Correia Dentada",
      brand: "Gates",
      price: 89.9,
      stock: 6,
      store: "Paraná Autopeças",
      city: "Curitiba",
      state: "PR",
      whatsapp: "+5541999999999",
      phone: "(41) 8888-9999",
      lastUpdate: new Date(currentDate.getTime() - 45 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 45 days ago
    },
    {
      reference: "EE445566",
      description: "Bomba d'água",
      brand: "Urba",
      price: 189.9,
      stock: 3,
      store: "AutoPeças Premium",
      city: "São Paulo",
      state: "SP",
      whatsapp: "+5511999999999",
      phone: "(11) 9999-0000",
      lastUpdate: new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 1 day ago
    },
    {
      reference: "FF778899",
      description: "Filtro de Ar",
      brand: "Fram",
      price: 49.9,
      stock: 15,
      store: "Center Parts",
      city: "Campinas",
      state: "SP",
      whatsapp: "+5519999999999",
      phone: "(19) 0000-1111",
      lastUpdate: new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 7 days ago
    },
    {
      reference: "GG112233",
      description: "Junta do Cabeçote",
      brand: "Sabó",
      price: 159.9,
      stock: 2,
      store: "Freios & Cia",
      city: "Rio de Janeiro",
      state: "RJ",
      whatsapp: "+5521999999999",
      phone: "(21) 1111-2222",
      lastUpdate: new Date(currentDate.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 60 days ago
    },
    {
      reference: "HH445566",
      description: "Radiador",
      brand: "Visconde",
      price: 359.9,
      stock: 4,
      store: "Amortex",
      city: "Belo Horizonte",
      state: "MG",
      whatsapp: "+5531999999999",
      phone: "(31) 2222-3333",
      lastUpdate: new Date(currentDate.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 3 days ago
    },
    {
      reference: "II778899",
      description: "Sensor de Oxigênio",
      brand: "Bosch",
      price: 219.9,
      stock: 7,
      store: "Sul Peças",
      city: "Porto Alegre",
      state: "RS",
      whatsapp: "+5551999999999",
      phone: "(51) 3333-4444",
      lastUpdate: new Date(currentDate.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 15 days ago
    },
    {
      reference: "JJ112233",
      description: "Bateria 60Ah",
      brand: "Moura",
      price: 459.9,
      stock: 5,
      store: "Paraná Autopeças",
      city: "Curitiba",
      state: "PR",
      whatsapp: "+5541999999999",
      phone: "(41) 4444-5555",
      lastUpdate: new Date(currentDate.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 30 days ago
    },
  ]

  // Filter products based on all filters
  const filteredProducts = products.filter((product) => {
    // Filter by query (reference or description)
    const matchesQuery =
      !query ||
      product.reference.toLowerCase().includes(query.toLowerCase()) ||
      product.description.toLowerCase().includes(query.toLowerCase())

    // Filter by state
    const matchesState = selectedState === "all" || product.state === selectedState

    // Filter by brand
    const matchesBrand = brand === "all" || product.brand.toLowerCase() === brand.toLowerCase()

    // Filter by city
    const matchesCity = selectedCity === "all" || product.city === selectedCity

    // Filter by store
    const matchesStore = selectedStore === "all" || product.store === selectedStore

    // Filter by price range
    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1]

    return matchesQuery && matchesState && matchesBrand && matchesCity && matchesStore && matchesPrice
  })

  // Sort products based on selected sort option
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortOption) {
      case "price-asc":
        return a.price - b.price
      case "price-desc":
        return b.price - a.price
      case "name-asc":
        return a.description.localeCompare(b.description)
      case "update-desc":
        return getDaysSinceUpdate(a.lastUpdate) - getDaysSinceUpdate(b.lastUpdate)
      case "stock-asc":
        return a.stock - b.stock
      case "stock-desc":
        return b.stock - a.stock
      case "store-asc":
        return a.store.localeCompare(b.store)
      case "store-desc":
        return b.store.localeCompare(a.store)
      case "brand-asc":
        return a.brand.localeCompare(b.brand)
      case "brand-desc":
        return b.brand.localeCompare(a.brand)
      case "relevance":
      default:
        // For relevance, prioritize exact matches in reference, then description
        const aMatchesRef = a.reference.toLowerCase().includes(query.toLowerCase())
        const bMatchesRef = b.reference.toLowerCase().includes(query.toLowerCase())

        if (aMatchesRef && !bMatchesRef) return -1
        if (!aMatchesRef && bMatchesRef) return 1

        // If both match or don't match reference, check description
        return a.description.toLowerCase().localeCompare(b.description.toLowerCase())
    }
  })

  // Pagination logic
  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage)
  const paginatedProducts = sortedProducts.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // Reset to page 1 when filters or sort option change
  useEffect(() => {
    setCurrentPage(1)
  }, [selectedState, selectedCity, selectedStore, priceRange, brand, query, sortOption])

  // Apply filters function
  const applyFilters = () => {
    const params = new URLSearchParams(searchParams.toString())

    if (selectedState !== "all") {
      params.set("uf", selectedState)
    } else {
      params.delete("uf")
    }

    // Update URL with new filters
    router.push(`/busca?${params.toString()}`)
  }

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedCity("all")
    setSelectedState("all")
    setSelectedStore("all")
    setPriceRange([0, 2000])

    // Update URL
    const params = new URLSearchParams(searchParams.toString())
    params.delete("uf")
    params.delete("brand")
    router.push(`/busca?${params.toString()}`)
  }

  // Count active filters
  const activeFiltersCount = [
    selectedState !== "all",
    selectedCity !== "all",
    selectedStore !== "all",
    priceRange[0] > 0 || priceRange[1] < 2000,
    brand !== "all",
  ].filter(Boolean).length

  // Comparison functions
  const addToComparison = (partId: string) => {
    if (comparisonParts.length < 4 && !comparisonParts.includes(partId)) {
      setComparisonParts(prev => [...prev, partId])
    }
  }

  const removeFromComparison = (partId: string) => {
    setComparisonParts(prev => prev.filter(id => id !== partId))
  }

  const clearComparison = () => {
    setComparisonParts([])
    setShowComparison(false)
  }

  // Advanced filters functions
  const handleAdvancedFiltersApply = () => {
    // Apply advanced filters logic here
    console.log('Applying advanced filters:', advancedFilters)
  }

  const handleAdvancedFiltersClear = () => {
    setAdvancedFilters({ page: 1, size: 20 })
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1">
        <div className="container py-8">
          <div className="mb-8">
            <div className="mb-4">
              <h1 className="text-2xl font-bold md:text-3xl">Buscar peças</h1>
              <p className="text-sm text-muted-foreground">
                Digite a referência da peça (separe múltiplas por vírgula)
              </p>
            </div>

            <div className="space-y-4">
              <SearchBar />
              <div className="flex flex-wrap items-center justify-between gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show-prices"
                    checked={showPrices}
                    onCheckedChange={(checked) => setShowPrices(checked as boolean)}
                  />
                  <Label htmlFor="show-prices">Exibir preços</Label>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center gap-1"
                  >
                    <Filter className="h-4 w-4" />
                    Filtros
                    {activeFiltersCount > 0 && (
                      <Badge className="ml-1 h-5 w-5 rounded-full p-0 text-[10px]">{activeFiltersCount}</Badge>
                    )}
                    {showFilters ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
                  </Button>

                  <AdvancedFilters
                    filters={advancedFilters}
                    onFiltersChange={setAdvancedFilters}
                    onApply={handleAdvancedFiltersApply}
                    onClear={handleAdvancedFiltersClear}
                    isOpen={showAdvancedFilters}
                    onToggle={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  />

                  {comparisonParts.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowComparison(!showComparison)}
                      className="flex items-center gap-1"
                    >
                      Comparar
                      <Badge className="ml-1">{comparisonParts.length}</Badge>
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Comparison Component */}
            {showComparison && comparisonParts.length > 0 && (
              <div className="mt-4">
                <PartComparisonComponent
                  partIds={comparisonParts}
                  onRemovePart={removeFromComparison}
                  onClose={() => setShowComparison(false)}
                />
              </div>
            )}

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <div className="mt-4">
                <AdvancedFilters
                  filters={advancedFilters}
                  onFiltersChange={setAdvancedFilters}
                  onApply={handleAdvancedFiltersApply}
                  onClear={handleAdvancedFiltersClear}
                  isOpen={true}
                  onToggle={() => setShowAdvancedFilters(false)}
                />
              </div>
            )}

            {/* Top-aligned filters */}
            {showFilters && (
              <div className="mt-4 rounded-lg border bg-card p-4 shadow-sm">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="font-medium">Filtrar resultados</h3>
                  <Button variant="ghost" size="sm" onClick={clearAllFilters} disabled={activeFiltersCount === 0}>
                    <X className="mr-1 h-4 w-4" />
                    Limpar filtros
                  </Button>
                </div>

                <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                  {/* State filter */}
                  <div className="space-y-2">
                    <Label htmlFor="filter-state">Estado</Label>
                    <Select value={selectedState} onValueChange={setSelectedState}>
                      <SelectTrigger id="filter-state">
                        <SelectValue placeholder="Selecione um estado" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos os estados</SelectItem>
                        {STATES.map((state) => (
                          <SelectItem key={state} value={state}>
                            {state}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* City filter */}
                  <div className="space-y-2">
                    <Label htmlFor="filter-city">Cidade</Label>
                    <Select value={selectedCity} onValueChange={setSelectedCity}>
                      <SelectTrigger id="filter-city">
                        <SelectValue placeholder="Selecione uma cidade" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as cidades</SelectItem>
                        {CITIES.map((city) => (
                          <SelectItem key={city} value={city}>
                            {city}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Store filter */}
                  <div className="space-y-2">
                    <Label htmlFor="filter-store">Loja</Label>
                    <Select value={selectedStore} onValueChange={setSelectedStore}>
                      <SelectTrigger id="filter-store">
                        <SelectValue placeholder="Selecione uma loja" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as lojas</SelectItem>
                        {STORES.map((store) => (
                          <SelectItem key={store} value={store}>
                            {store}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Price range filter */}
                  <div className="space-y-2">
                    <Label>Faixa de preço</Label>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        min={0}
                        max={2000}
                        step={10}
                        onValueChange={(value) => setPriceRange(value as [number, number])}
                        className="py-4"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Input
                        type="number"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
                        className="h-8 w-20"
                      />
                      <span className="text-sm text-muted-foreground">até</span>
                      <Input
                        type="number"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value)])}
                        className="h-8 w-20"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-4 flex justify-end">
                  <Button onClick={applyFilters}>Aplicar filtros</Button>
                </div>
              </div>
            )}

            {/* Active filters display */}
            {activeFiltersCount > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                {selectedState !== "all" && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Estado: {selectedState}
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={() => setSelectedState("all")}>
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remover filtro de estado</span>
                    </Button>
                  </Badge>
                )}

                {selectedCity !== "all" && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Cidade: {selectedCity}
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={() => setSelectedCity("all")}>
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remover filtro de cidade</span>
                    </Button>
                  </Badge>
                )}

                {selectedStore !== "all" && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Loja: {selectedStore}
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={() => setSelectedStore("all")}>
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remover filtro de loja</span>
                    </Button>
                  </Badge>
                )}

                {(priceRange[0] > 0 || priceRange[1] < 2000) && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Preço: R$ {priceRange[0]} - R$ {priceRange[1]}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0"
                      onClick={() => setPriceRange([0, 2000])}
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remover filtro de preço</span>
                    </Button>
                  </Badge>
                )}

                {brand !== "all" && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Marca: {brand}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0"
                      onClick={() => {
                        const params = new URLSearchParams(searchParams.toString())
                        params.delete("brand")
                        router.push(`/busca?${params.toString()}`)
                      }}
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remover filtro de marca</span>
                    </Button>
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Search results */}
          <div className="space-y-4">
            {/* Active filters summary */}
            {activeFiltersCount > 0 && (
              <div className="rounded-md bg-muted/50 p-3">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm font-medium">Filtros ativos:</span>
                  {selectedState !== "all" && (
                    <Badge variant="outline" className="flex items-center gap-1 bg-primary/10">
                      Estado: {selectedState}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0 ml-1"
                        onClick={() => setSelectedState("all")}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remover filtro de estado</span>
                      </Button>
                    </Badge>
                  )}
                  {selectedCity !== "all" && (
                    <Badge variant="outline" className="flex items-center gap-1 bg-primary/10">
                      Cidade: {selectedCity}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0 ml-1"
                        onClick={() => setSelectedCity("all")}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remover filtro de cidade</span>
                      </Button>
                    </Badge>
                  )}
                  {selectedStore !== "all" && (
                    <Badge variant="outline" className="flex items-center gap-1 bg-primary/10">
                      Loja: {selectedStore}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0 ml-1"
                        onClick={() => setSelectedStore("all")}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remover filtro de loja</span>
                      </Button>
                    </Badge>
                  )}
                  {(priceRange[0] > 0 || priceRange[1] < 2000) && (
                    <Badge variant="outline" className="flex items-center gap-1 bg-primary/10">
                      Preço: R$ {priceRange[0]} - R$ {priceRange[1]}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0 ml-1"
                        onClick={() => setPriceRange([0, 2000])}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remover filtro de preço</span>
                      </Button>
                    </Badge>
                  )}
                  {brand !== "all" && (
                    <Badge variant="outline" className="flex items-center gap-1 bg-primary/10">
                      Marca: {brand}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0 ml-1"
                        onClick={() => {
                          const params = new URLSearchParams(searchParams.toString())
                          params.delete("brand")
                          router.push(`/busca?${params.toString()}`)
                        }}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remover filtro de marca</span>
                      </Button>
                    </Badge>
                  )}
                  <Button variant="outline" size="sm" className="ml-auto" onClick={clearAllFilters}>
                    <X className="mr-1 h-4 w-4" />
                    Limpar todos os filtros
                  </Button>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">{sortedProducts.length} resultados encontrados</p>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Ordenar por:</span>
                <Select value={sortOption} onValueChange={(value) => setSortOption(value as SortOption)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">
                      <div className="flex items-center">
                        <span>Relevância</span>
                        {sortOption === "relevance" && <CheckCircle className="ml-2 h-4 w-4" />}
                      </div>
                    </SelectItem>
                    <SelectItem value="price-asc">
                      <div className="flex items-center">
                        <span>Menor preço</span>
                        <ArrowDown className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="price-desc">
                      <div className="flex items-center">
                        <span>Maior preço</span>
                        <ArrowUp className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="stock-asc">
                      <div className="flex items-center">
                        <span>Menor estoque</span>
                        <ArrowDown className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="stock-desc">
                      <div className="flex items-center">
                        <span>Maior estoque</span>
                        <ArrowUp className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="brand-asc">
                      <div className="flex items-center">
                        <span>Marca (A-Z)</span>
                        <ArrowDown className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="store-asc">
                      <div className="flex items-center">
                        <span>Loja (A-Z)</span>
                        <ArrowDown className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="name-asc">
                      <div className="flex items-center">
                        <span>Nome (A-Z)</span>
                        <ArrowDown className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                    <SelectItem value="update-desc">
                      <div className="flex items-center">
                        <span>Atualização recente</span>
                        <Clock className="ml-2 h-4 w-4" />
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {sortedProducts.length > 0 ? (
              <div className="space-y-4">
                {/* Table-like header (visible only on md and up) */}
                <div className="hidden md:grid md:grid-cols-12 md:gap-4 p-2 font-medium text-sm border-b mb-2">
                  <div className="md:col-span-3">REFERÊNCIA / DESCRIÇÃO</div>
                  <div className="md:col-span-2">MARCA / ESTOQUE</div>
                  <div className="md:col-span-2">LOJA / CIDADE</div>
                  {showPrices && <div className="md:col-span-1 text-center">PREÇO</div>}
                  <div className="md:col-span-2">ATUALIZAÇÃO</div>
                  <div className="md:col-span-2">CONTATO</div>
                </div>
                {/* Responsive Card Grid Layout */}
                <div className="space-y-2">
                  {paginatedProducts.map((product) => {
                    const daysSinceUpdate = getDaysSinceUpdate(product.lastUpdate)
                    const updateStatusColor = getUpdateStatusColor(daysSinceUpdate)

                    return (
                      <div
                        key={product.reference}
                        className="rounded-lg border bg-card shadow-sm transition-all hover:shadow-md"
                      >
                        <div className="grid grid-cols-1 md:grid-cols-12 md:items-center p-3 md:p-4 gap-2 md:gap-4">
                          {/* Reference & Description - 3 cols */}
                          <div className="md:col-span-3 space-y-1">
                            <span className="inline-block rounded bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                              {product.reference}
                            </span>
                            <h3 className="font-semibold line-clamp-2">{product.description}</h3>
                          </div>

                          {/* Brand & Stock - 2 cols */}
                          <div className="md:col-span-2 text-sm grid grid-cols-2 md:grid-cols-1 gap-2">
                            <div>
                              <span className="text-muted-foreground md:hidden">Marca: </span>
                              <span className="font-medium">{product.brand}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground md:hidden">Estoque: </span>
                              <span className="font-medium">{product.stock} un</span>
                            </div>
                          </div>

                          {/* Store & Location - 2 cols */}
                          <div className="md:col-span-2 text-sm grid grid-cols-2 md:grid-cols-1 gap-2">
                            <div>
                              <span className="text-muted-foreground md:hidden">Loja: </span>
                              <span className="font-medium">{product.store}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground md:hidden">Local: </span>
                              <span className="font-medium">
                                {product.city}/{product.state}
                              </span>
                            </div>
                          </div>

                          {/* Price - 1 col */}
                          {showPrices && (
                            <div className="md:col-span-1 text-right md:text-center">
                              <span className="font-medium text-green-600">
                                {new Intl.NumberFormat("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                }).format(product.price)}
                              </span>
                            </div>
                          )}

                          {/* Last Update - 2 cols */}
                          <div className={`md:col-span-2 flex items-center ${updateStatusColor}`}>
                            <Clock className="mr-1.5 h-3.5 w-3.5" />
                            <span className="text-sm">{formatLastUpdate(product.lastUpdate)}</span>
                          </div>

                          {/* Action Buttons - 2 cols */}
                          <div className="md:col-span-2 flex flex-col gap-2">
                            <div className="flex gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1"
                                onClick={() => window.open(`/peca/${product.reference}`, '_blank')}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                Ver
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => addToComparison(product.reference)}
                                disabled={comparisonParts.includes(product.reference) || comparisonParts.length >= 4}
                                className={comparisonParts.includes(product.reference) ? "bg-primary/10" : ""}
                              >
                                {comparisonParts.includes(product.reference) ? (
                                  <Check className="h-3 w-3" />
                                ) : (
                                  <Plus className="h-3 w-3" />
                                )}
                              </Button>
                            </div>
                            <Button
                              variant="outline"
                              className="w-full"
                              size="sm"
                              icon={<Phone className="h-4 w-4" />}
                              onClick={(e) => {
                                e.preventDefault()
                                const button = e.currentTarget
                                const currentText = button.textContent?.trim()
                                const phone = product.phone

                                // Toggle between showing "Ver telefone" and the actual phone number
                                if (currentText === "Ver telefone") {
                                  button.innerHTML = `<svg class="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>${phone}`
                                } else {
                                  button.innerHTML = `<svg class="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>Ver telefone`
                                }
                              }}
                            >
                              Ver telefone
                            </Button>
                            <Button
                              className="w-full bg-green-500 hover:bg-green-600"
                              size="sm"
                              icon={<MessageCircle className="h-4 w-4" />}
                              onClick={() => handleWhatsAppClick(product)}
                            >
                              WhatsApp
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Pagination - keep the same */}
                {totalPages > 1 && (
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault()
                            if (currentPage > 1) setCurrentPage(currentPage - 1)
                            return false
                          }}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                        >
                          Anterior
                        </PaginationPrevious>
                      </PaginationItem>

                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        // Show first page, last page, current page, and pages around current
                        let pageToShow
                        if (totalPages <= 5) {
                          pageToShow = i + 1
                        } else if (currentPage <= 3) {
                          pageToShow = i + 1
                        } else if (currentPage >= totalPages - 2) {
                          pageToShow = totalPages - 4 + i
                        } else {
                          pageToShow = currentPage - 2 + i
                        }

                        if (pageToShow <= totalPages) {
                          return (
                            <PaginationItem key={pageToShow}>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  setCurrentPage(pageToShow)
                                  return false
                                }}
                                isActive={currentPage === pageToShow}
                              >
                                {pageToShow}
                              </PaginationLink>
                            </PaginationItem>
                          )
                        }
                        return null
                      })}

                      {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault()
                              setCurrentPage(totalPages)
                              return false
                            }}
                          >
                            {totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault()
                            if (currentPage < totalPages) setCurrentPage(currentPage + 1)
                            return false
                          }}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                        >
                          Próximo
                        </PaginationNext>
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </div>
            ) : (
              <div className="rounded-lg border border-dashed p-8 text-center">
                <h3 className="mb-2 text-lg font-semibold">Nenhum resultado encontrado</h3>
                <p className="text-muted-foreground">Tente ajustar seus filtros ou buscar por outro termo</p>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

