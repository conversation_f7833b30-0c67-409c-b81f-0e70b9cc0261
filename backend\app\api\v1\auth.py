"""
Authentication endpoints for the AutoParts API.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.auth import AuthService
from app.api.deps import get_current_active_user, get_auth_service
from app.schemas.auth import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    UserCreate,
    User,
    PasswordResetRequest,
    PasswordResetConfirm,
    PasswordChangeRequest,
    APIKeyResponse,
    CurrentUser
)
from app.models.user import User as UserModel

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Login endpoint for user authentication.
    
    Args:
        login_data: Login credentials
        auth_service: Authentication service
        
    Returns:
        Login response with access and refresh tokens
        
    Raises:
        HTTPException: If authentication fails
    """
    login_response = auth_service.login(login_data.email, login_data.password)
    
    if not login_response:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return login_response


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Refresh access token using refresh token.
    
    Args:
        refresh_data: Refresh token request
        auth_service: Authentication service
        
    Returns:
        New access token
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    token_response = auth_service.refresh_access_token(refresh_data.refresh_token)
    
    if not token_response:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return token_response


@router.post("/register", response_model=User)
async def register(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user.
    
    Args:
        user_data: User registration data
        auth_service: Authentication service
        
    Returns:
        Created user information
        
    Raises:
        HTTPException: If registration fails
    """
    try:
        user = auth_service.register_user(user_data)
        return User.from_orm(user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/me", response_model=CurrentUser)
async def get_current_user_info(
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user information
    """
    return CurrentUser.from_orm(current_user)


@router.post("/logout")
async def logout():
    """
    Logout endpoint.
    
    Note: Since we're using stateless JWT tokens, logout is handled
    client-side by removing the tokens. This endpoint exists for
    API consistency and future token blacklisting implementation.
    
    Returns:
        Success message
    """
    return {"message": "Successfully logged out"}


@router.post("/password-reset")
async def request_password_reset(
    reset_data: PasswordResetRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Request password reset.
    
    Args:
        reset_data: Password reset request
        auth_service: Authentication service
        
    Returns:
        Success message
        
    Note: Always returns success for security reasons,
    even if email doesn't exist.
    """
    auth_service.initiate_password_reset(reset_data.email)
    
    return {
        "message": "If the email exists, a password reset link has been sent"
    }


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Confirm password reset with token.
    
    Args:
        reset_data: Password reset confirmation
        auth_service: Authentication service
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If reset token is invalid or expired
    """
    success = auth_service.reset_password(reset_data.token, reset_data.new_password)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    return {"message": "Password reset successfully"}


@router.post("/password-change")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: UserModel = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Change user password.
    
    Args:
        password_data: Password change request
        current_user: Current authenticated user
        auth_service: Authentication service
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If current password is incorrect
    """
    success = auth_service.change_password(
        current_user.id,
        password_data.current_password,
        password_data.new_password
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    return {"message": "Password changed successfully"}


@router.post("/api-key", response_model=APIKeyResponse)
async def generate_api_key(
    current_user: UserModel = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Generate API key for current user.
    
    Args:
        current_user: Current authenticated user
        auth_service: Authentication service
        
    Returns:
        Generated API key
        
    Raises:
        HTTPException: If API key generation fails
    """
    api_key = auth_service.generate_api_key(current_user.id)
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate API key"
        )
    
    return APIKeyResponse(
        api_key=api_key,
        created_at=current_user.updated_at
    )
