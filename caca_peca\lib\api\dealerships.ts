/**
 * Dealerships API Service
 * Handles all dealership management related API calls
 */

import { apiClient } from './client';
import {
  ApiResponse,
  Dealership,
  DealershipCreate,
  DealershipUpdate,
  PaginatedResponse
} from '../types/api';

export interface DealershipSearchParams {
  name?: string;
  city?: string;
  state?: string;
  active_only?: boolean;
  page?: number;
  size?: number;
}

export class DealershipsService {
  /**
   * Search dealerships with filters and pagination
   */
  async searchDealerships(searchParams: DealershipSearchParams): Promise<ApiResponse<PaginatedResponse<Dealership>>> {
    const params = new URLSearchParams();
    
    if (searchParams.name) params.append('name', searchParams.name);
    if (searchParams.city) params.append('city', searchParams.city);
    if (searchParams.state) params.append('state', searchParams.state);
    if (searchParams.active_only) params.append('active_only', searchParams.active_only.toString());
    if (searchParams.page) params.append('page', searchParams.page.toString());
    if (searchParams.size) params.append('size', searchParams.size.toString());

    return apiClient.request<PaginatedResponse<Dealership>>({
      method: 'GET',
      url: `/dealerships/search?${params.toString()}`
    });
  }

  /**
   * Get dealership by ID
   */
  async getDealershipById(dealershipId: string): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'GET',
      url: `/dealerships/${dealershipId}`
    });
  }

  /**
   * Get current dealership profile
   */
  async getMyProfile(): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'GET',
      url: '/dealerships/me'
    });
  }

  /**
   * Create new dealership
   */
  async createDealership(dealershipData: DealershipCreate): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'POST',
      url: '/dealerships',
      data: dealershipData
    });
  }

  /**
   * Update dealership
   */
  async updateDealership(dealershipId: string, dealershipData: DealershipUpdate): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'PUT',
      url: `/dealerships/${dealershipId}`,
      data: dealershipData
    });
  }

  /**
   * Update current dealership profile
   */
  async updateMyProfile(dealershipData: DealershipUpdate): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'PUT',
      url: '/dealerships/me',
      data: dealershipData
    });
  }

  /**
   * Delete dealership (admin only)
   */
  async deleteDealership(dealershipId: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'DELETE',
      url: `/dealerships/${dealershipId}`
    });
  }

  /**
   * Get all dealerships with pagination
   */
  async getAllDealerships(page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Dealership>>> {
    return apiClient.request<PaginatedResponse<Dealership>>({
      method: 'GET',
      url: `/dealerships?page=${page}&size=${size}`
    });
  }

  /**
   * Get dealerships by state
   */
  async getDealershipsByState(state: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Dealership>>> {
    return apiClient.request<PaginatedResponse<Dealership>>({
      method: 'GET',
      url: `/dealerships/state/${encodeURIComponent(state)}?page=${page}&size=${size}`
    });
  }

  /**
   * Get dealerships by city
   */
  async getDealershipsByCity(city: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Dealership>>> {
    return apiClient.request<PaginatedResponse<Dealership>>({
      method: 'GET',
      url: `/dealerships/city/${encodeURIComponent(city)}?page=${page}&size=${size}`
    });
  }

  /**
   * Activate dealership (admin only)
   */
  async activateDealership(dealershipId: string): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'POST',
      url: `/dealerships/${dealershipId}/activate`
    });
  }

  /**
   * Deactivate dealership (admin only)
   */
  async deactivateDealership(dealershipId: string): Promise<ApiResponse<Dealership>> {
    return apiClient.request<Dealership>({
      method: 'POST',
      url: `/dealerships/${dealershipId}/deactivate`
    });
  }

  /**
   * Get dealership statistics
   */
  async getDealershipStats(dealershipId?: string): Promise<ApiResponse<any>> {
    const url = dealershipId 
      ? `/dealerships/${dealershipId}/stats`
      : '/dealerships/me/stats';
    
    return apiClient.request<any>({
      method: 'GET',
      url
    });
  }

  /**
   * Get all states with dealerships
   */
  async getStates(): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: '/dealerships/states'
    });
  }

  /**
   * Get all cities with dealerships in a state
   */
  async getCitiesByState(state: string): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: `/dealerships/cities?state=${encodeURIComponent(state)}`
    });
  }

  /**
   * Check if CNPJ is available
   */
  async checkCnpjAvailability(cnpj: string): Promise<ApiResponse<{ available: boolean }>> {
    return apiClient.request<{ available: boolean }>({
      method: 'GET',
      url: `/dealerships/check-cnpj?cnpj=${encodeURIComponent(cnpj)}`
    });
  }

  /**
   * Check if email is available
   */
  async checkEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    return apiClient.request<{ available: boolean }>({
      method: 'GET',
      url: `/dealerships/check-email?email=${encodeURIComponent(email)}`
    });
  }

  /**
   * Get dealerships with inventory for specific part
   */
  async getDealershipsWithPart(partId: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Dealership>>> {
    return apiClient.request<PaginatedResponse<Dealership>>({
      method: 'GET',
      url: `/dealerships/with-part/${partId}?page=${page}&size=${size}`
    });
  }

  /**
   * Get nearby dealerships by coordinates
   */
  async getNearbyDealerships(
    latitude: number, 
    longitude: number, 
    radius = 50, 
    page = 1, 
    size = 20
  ): Promise<ApiResponse<PaginatedResponse<Dealership>>> {
    return apiClient.request<PaginatedResponse<Dealership>>({
      method: 'GET',
      url: `/dealerships/nearby?lat=${latitude}&lng=${longitude}&radius=${radius}&page=${page}&size=${size}`
    });
  }
}

// Export singleton instance
export const dealershipsService = new DealershipsService();
export default dealershipsService;
