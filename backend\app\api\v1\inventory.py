"""
Inventory management endpoints for the AutoParts API.
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.seo_cache import seo_cache
from app.services.inventory import InventoryService
from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
    get_current_dealership_user,
    optional_authentication
)
from app.schemas.inventory import (
    InventoryCreate,
    InventoryUpdate,
    Inventory,
    InventoryWithDetails,
    InventoryListResponse,
    InventorySearchFilters,
    InventoryStats,
    DealershipInventoryStats,
    InventoryValuation,
    BulkInventoryUpdate,
    BulkInventoryResponse
)
from app.models.user import User

router = APIRouter()


def get_inventory_service(db: Session = Depends(get_db)) -> InventoryService:
    """Get inventory service instance."""
    return InventoryService(db)


@router.post("/", response_model=Inventory)
async def create_inventory_item(
    inventory_data: InventoryCreate,
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Create a new inventory item.
    
    Args:
        inventory_data: Inventory creation data
        current_user: Current authenticated user
        inventory_service: Inventory service instance
        
    Returns:
        Created inventory item
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id
        
        inventory = inventory_service.create_inventory_item(
            inventory_data, user_dealership_id
        )

        # Invalidate SEO cache for the part
        if inventory.part and inventory.part.part_number:
            seo_cache.invalidate_part_cache(inventory.part.part_number)

        return Inventory.from_orm(inventory)
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/search", response_model=InventoryListResponse)
async def search_inventory(
    part_code: Optional[str] = Query(None, description="Filter by part code"),
    part_name: Optional[str] = Query(None, description="Filter by part name"),
    dealership_id: Optional[UUID] = Query(None, description="Filter by dealership"),
    brand: Optional[str] = Query(None, description="Filter by vehicle brand"),
    model: Optional[str] = Query(None, description="Filter by vehicle model"),
    year: Optional[int] = Query(None, ge=1900, le=2030, description="Filter by vehicle year"),
    category: Optional[str] = Query(None, description="Filter by part category"),
    condition: Optional[str] = Query(None, description="Filter by condition"),
    min_price: Optional[float] = Query(None, gt=0, description="Minimum price"),
    max_price: Optional[float] = Query(None, gt=0, description="Maximum price"),
    min_quantity: Optional[int] = Query(None, ge=0, description="Minimum quantity"),
    available_only: bool = Query(True, description="Only show available items"),
    in_stock_only: bool = Query(True, description="Only show items in stock"),
    low_stock_only: bool = Query(False, description="Only show low stock items"),
    city: Optional[str] = Query(None, description="Filter by dealership city"),
    state: Optional[str] = Query(None, description="Filter by dealership state"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("last_updated", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    current_user: Optional[User] = Depends(optional_authentication),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Search inventory with advanced filtering.
    
    This endpoint supports both authenticated and anonymous access.
    Authenticated dealership users can only see their own inventory.
    
    Args:
        part_code: Filter by part code
        part_name: Filter by part name
        dealership_id: Filter by dealership
        brand: Vehicle brand filter
        model: Vehicle model filter
        year: Vehicle year filter
        category: Part category filter
        condition: Condition filter
        min_price: Minimum price
        max_price: Maximum price
        min_quantity: Minimum quantity
        available_only: Only available items
        in_stock_only: Only items in stock
        low_stock_only: Only low stock items
        city: Dealership city filter
        state: Dealership state filter
        page: Page number
        per_page: Items per page
        sort_by: Sort field
        sort_order: Sort order
        current_user: Optional authenticated user
        inventory_service: Inventory service instance
        
    Returns:
        Search results with inventory items
    """
    filters = InventorySearchFilters(
        part_code=part_code,
        part_name=part_name,
        dealership_id=dealership_id,
        brand=brand,
        model=model,
        year=year,
        category=category,
        condition=condition,
        min_price=min_price,
        max_price=max_price,
        min_quantity=min_quantity,
        available_only=available_only,
        in_stock_only=in_stock_only,
        low_stock_only=low_stock_only,
        city=city,
        state=state,
        page=page,
        per_page=per_page,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    user_dealership_id = None
    if current_user and not current_user.is_admin:
        user_dealership_id = current_user.dealership_id
    
    inventories, total = inventory_service.search_inventory(filters, user_dealership_id)
    
    pages = (total + per_page - 1) // per_page
    
    return InventoryListResponse(
        inventories=inventories,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/stats", response_model=InventoryStats)
async def get_inventory_stats(
    dealership_id: Optional[UUID] = Query(None, description="Filter by dealership"),
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Get inventory statistics.
    
    Args:
        dealership_id: Optional dealership filter
        current_user: Current authenticated user
        inventory_service: Inventory service instance
        
    Returns:
        Inventory statistics
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id
        
        return inventory_service.get_inventory_stats(dealership_id, user_dealership_id)
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get("/low-stock", response_model=InventoryListResponse)
async def get_low_stock_items(
    dealership_id: Optional[UUID] = Query(None, description="Filter by dealership"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Get items with low stock levels.
    
    Args:
        dealership_id: Optional dealership filter
        page: Page number
        per_page: Items per page
        current_user: Current authenticated user
        inventory_service: Inventory service instance
        
    Returns:
        Low stock inventory items
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id
        skip = (page - 1) * per_page
        
        inventories, total = inventory_service.get_low_stock_items(
            dealership_id, skip, per_page, user_dealership_id
        )
        
        pages = (total + per_page - 1) // per_page
        
        return InventoryListResponse(
            inventories=inventories,
            total=total,
            page=page,
            per_page=per_page,
            pages=pages
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get("/out-of-stock", response_model=InventoryListResponse)
async def get_out_of_stock_items(
    dealership_id: Optional[UUID] = Query(None, description="Filter by dealership"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Get items that are out of stock.
    
    Args:
        dealership_id: Optional dealership filter
        page: Page number
        per_page: Items per page
        current_user: Current authenticated user
        inventory_service: Inventory service instance
        
    Returns:
        Out of stock inventory items
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id
        skip = (page - 1) * per_page
        
        inventories, total = inventory_service.get_out_of_stock_items(
            dealership_id, skip, per_page, user_dealership_id
        )
        
        pages = (total + per_page - 1) // per_page
        
        return InventoryListResponse(
            inventories=inventories,
            total=total,
            page=page,
            per_page=per_page,
            pages=pages
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get("/{inventory_id}", response_model=InventoryWithDetails)
async def get_inventory_item(
    inventory_id: UUID,
    current_user: Optional[User] = Depends(optional_authentication),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Get inventory item by ID.

    Args:
        inventory_id: Inventory UUID
        current_user: Optional authenticated user
        inventory_service: Inventory service instance

    Returns:
        Inventory item with details

    Raises:
        HTTPException: If inventory not found or access denied
    """
    try:
        user_dealership_id = None
        if current_user and not current_user.is_admin:
            user_dealership_id = current_user.dealership_id

        inventory = inventory_service.get_inventory_by_id(inventory_id, user_dealership_id)

        if not inventory:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inventory item not found"
            )

        return inventory
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put("/{inventory_id}", response_model=Inventory)
async def update_inventory_item(
    inventory_id: UUID,
    update_data: InventoryUpdate,
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Update inventory item.

    Args:
        inventory_id: Inventory UUID
        update_data: Updated inventory data
        current_user: Current authenticated user
        inventory_service: Inventory service instance

    Returns:
        Updated inventory item

    Raises:
        HTTPException: If inventory not found or access denied
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id

        inventory = inventory_service.update_inventory_item(
            inventory_id, update_data, user_dealership_id
        )

        if not inventory:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inventory item not found"
            )

        # Invalidate SEO cache for the part
        if inventory.part and inventory.part.part_number:
            seo_cache.invalidate_part_cache(inventory.part.part_number)

        return Inventory.from_orm(inventory)
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.delete("/{inventory_id}")
async def delete_inventory_item(
    inventory_id: UUID,
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Delete an inventory item.

    Args:
        inventory_id: Inventory UUID
        current_user: Current authenticated user
        inventory_service: Inventory service instance

    Returns:
        Success message

    Raises:
        HTTPException: If inventory not found or access denied
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id

        success = inventory_service.delete_inventory_item(inventory_id, user_dealership_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inventory item not found"
            )

        return {"message": "Inventory item deleted successfully"}
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post("/bulk-update", response_model=BulkInventoryResponse)
async def bulk_update_inventory(
    bulk_data: BulkInventoryUpdate,
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Perform bulk inventory updates.

    Args:
        bulk_data: Bulk update data
        current_user: Current authenticated user
        inventory_service: Inventory service instance

    Returns:
        Bulk operation response
    """
    user_dealership_id = None if current_user.is_admin else current_user.dealership_id

    return inventory_service.bulk_update_inventory(bulk_data, user_dealership_id)


@router.get("/dealership/{dealership_id}", response_model=InventoryListResponse)
async def get_dealership_inventory(
    dealership_id: UUID,
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    available_only: bool = Query(True, description="Only show available items"),
    current_user: User = Depends(get_current_active_user),
    inventory_service: InventoryService = Depends(get_inventory_service)
):
    """
    Get inventory for a specific dealership.

    Args:
        dealership_id: Dealership UUID
        page: Page number
        per_page: Items per page
        available_only: Only available items
        current_user: Current authenticated user
        inventory_service: Inventory service instance

    Returns:
        Dealership inventory items

    Raises:
        HTTPException: If access denied
    """
    try:
        user_dealership_id = None if current_user.is_admin else current_user.dealership_id
        skip = (page - 1) * per_page

        inventories, total = inventory_service.get_dealership_inventory(
            dealership_id, skip, per_page, available_only, user_dealership_id
        )

        pages = (total + per_page - 1) // per_page

        return InventoryListResponse(
            inventories=inventories,
            total=total,
            page=page,
            per_page=per_page,
            pages=pages
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
