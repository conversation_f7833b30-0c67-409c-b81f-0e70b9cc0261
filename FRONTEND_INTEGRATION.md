# AutoParts Frontend Integration Guide

## Overview

This document describes the integration between the AutoParts Next.js frontend and FastAPI backend. The integration provides a complete B2B marketplace for automotive parts with real-time inventory management.

## Architecture

```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   Next.js       │◄──────────────────►│   FastAPI       │
│   Frontend      │                     │   Backend       │
│   (Port 3000)   │                     │   (Port 8000)   │
└─────────────────┘                     └─────────────────┘
│                                       │
├── React Components                    ├── API Endpoints
├── TypeScript API Client               ├── Pydantic Models
├── React Query (TanStack)              ├── SQLAlchemy ORM
├── Zustand State Management            ├── PostgreSQL Database
├── JWT Authentication                  ├── Redis Caching
└── Tailwind CSS                        └── Celery Background Tasks
```

## API Client Implementation

### TypeScript API Client

The frontend uses a comprehensive TypeScript API client located in `caca_peca/lib/api/`:

- **`client.ts`**: Main HTTP client with axios, JWT handling, and error management
- **`auth.ts`**: Authentication service (login, logout, register, token refresh)
- **`parts.ts`**: Parts catalog service (search, categories, brands, compatibility)
- **`inventory.ts`**: Inventory management service (CRUD, search, statistics)
- **`dealerships.ts`**: Dealership management service (profiles, registration)

### Key Features

1. **Automatic Token Management**: JWT tokens are automatically included in requests
2. **Token Refresh**: Expired tokens are automatically refreshed
3. **Error Handling**: Standardized error responses with proper typing
4. **Request Interceptors**: Automatic retry logic and request/response logging
5. **Type Safety**: Full TypeScript support with generated types from backend schemas

## Authentication Flow

```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend
    participant DB as Database

    F->>B: POST /api/v1/auth/login
    B->>DB: Validate credentials
    DB-->>B: User data
    B-->>F: JWT tokens (access + refresh)
    
    Note over F: Store tokens in secure cookies
    
    F->>B: API request with Bearer token
    B->>B: Validate JWT
    B-->>F: API response
    
    Note over F: Token expires
    
    F->>B: POST /api/v1/auth/refresh
    B-->>F: New access token
    F->>B: Retry original request
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Token refresh
- `GET /api/v1/auth/me` - Get current user

### Parts Catalog
- `GET /api/v1/parts/search` - Search parts with filters
- `GET /api/v1/parts/{id}` - Get part by ID
- `GET /api/v1/parts/categories` - Get all categories
- `GET /api/v1/parts/brands` - Get all brands
- `GET /api/v1/parts/suggestions` - Get search suggestions

### Inventory Management
- `GET /api/v1/inventory/search` - Search inventory
- `POST /api/v1/inventory` - Create inventory item
- `PUT /api/v1/inventory/{id}` - Update inventory item
- `DELETE /api/v1/inventory/{id}` - Delete inventory item
- `GET /api/v1/inventory/stats` - Get inventory statistics

### Dealerships
- `GET /api/v1/dealerships/search` - Search dealerships
- `POST /api/v1/dealerships` - Create dealership
- `GET /api/v1/dealerships/me` - Get current dealership profile
- `PUT /api/v1/dealerships/me` - Update dealership profile

## React Query Integration

The frontend uses TanStack Query (React Query) for:

- **Caching**: Automatic caching of API responses
- **Background Updates**: Automatic refetching of stale data
- **Optimistic Updates**: Immediate UI updates with rollback on error
- **Loading States**: Built-in loading and error states
- **Pagination**: Infinite queries for large datasets

### Example Usage

```typescript
import { usePartsSearch } from '@/lib/hooks/useApi';

function PartsSearchComponent() {
  const { data, isLoading, error } = usePartsSearch({
    query: 'brake pad',
    brand: 'toyota',
    page: 1,
    size: 20
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {data?.data?.items.map(part => (
        <div key={part.id}>{part.name}</div>
      ))}
    </div>
  );
}
```

## State Management

### Authentication Context

```typescript
import { useAuth } from '@/lib/auth/context';

function LoginComponent() {
  const { login, user, isAuthenticated } = useAuth();

  const handleLogin = async (credentials) => {
    const result = await login(credentials);
    if (result.success) {
      // Redirect to dashboard
    }
  };
}
```

### Role-Based Access Control

```typescript
import { useRole } from '@/lib/auth/context';

function AdminPanel() {
  const { isAdmin, isDealership } = useRole();

  if (!isAdmin) {
    return <div>Access denied</div>;
  }

  return <div>Admin content</div>;
}
```

## Error Handling

### Standardized Error Format

All API errors follow this format:

```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Validation error",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": 1642678800
}
```

### Frontend Error Handling

```typescript
const { data, error } = usePartsSearch(params);

if (error) {
  const errorMessage = error.response?.data?.error?.message || 'An error occurred';
  // Display error to user
}
```

## CORS Configuration

The backend is configured to accept requests from:
- `http://localhost:3000` (development)
- `https://autoparts.com` (production)
- `https://www.autoparts.com` (production)

## Environment Variables

### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1
NEXT_PUBLIC_APP_NAME=AutoParts
```

### Backend (.env)
```bash
BACKEND_CORS_ORIGINS=["http://localhost:3000","https://autoparts.com"]
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://user:pass@localhost/autoparts
```

## Testing Integration

Run the integration test suite:

```bash
# Make sure both services are running
cd backend && python -m uvicorn app.main:app --reload
cd caca_peca && npm run dev

# Run integration tests
python test_integration.py
```

## Deployment

### Development
```bash
# Backend
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Frontend
cd caca_peca
npm run dev
```

### Production
```bash
# Backend
cd backend
docker-compose up -d

# Frontend
cd caca_peca
npm run build
npm start
```

## Security Considerations

1. **JWT Tokens**: Stored in secure HTTP-only cookies
2. **CORS**: Properly configured for production domains
3. **Input Validation**: All inputs validated on both frontend and backend
4. **Rate Limiting**: API endpoints are rate-limited
5. **HTTPS**: All production traffic uses HTTPS

## Performance Optimizations

1. **Caching**: React Query caches API responses
2. **Code Splitting**: Next.js automatic code splitting
3. **Image Optimization**: Next.js Image component
4. **Database Indexing**: Optimized database queries
5. **Redis Caching**: Backend response caching

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check BACKEND_CORS_ORIGINS configuration
2. **Authentication Failures**: Verify JWT secret keys match
3. **API Connection**: Ensure backend is running on correct port
4. **Database Errors**: Check PostgreSQL connection and migrations

### Debug Mode

Enable debug logging:

```bash
# Backend
DEBUG=True

# Frontend
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
```
