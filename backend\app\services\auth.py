"""
Authentication service for user management and JWT operations.
"""
from typing import Optional, Dict, Any
from datetime import datetime, timed<PERSON>ta
from uuid import UUID
from sqlalchemy.orm import Session

from app.models.user import User, UserRole, UserStatus
from app.repositories.user import UserRepository
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    generate_password_reset_token,
    create_api_key,
    get_password_hash,
    verify_password
)
from app.core.config import settings
import os
from app.schemas.auth import (
    UserCreate,
    LoginResponse,
    RefreshTokenResponse,
    CurrentUser
)


class AuthService:
    """
    Service class for authentication operations.
    """

    def __init__(self, db: Session):
        self.db = db
        self.user_repo = UserRepository(db)

    def register_user(self, user_data: UserCreate) -> User:
        """
        Register a new user.

        Args:
            user_data: User registration data

        Returns:
            Created user instance

        Raises:
            ValueError: If email already exists
        """
        # Check if email already exists
        existing_user = self.user_repo.get_by_email(user_data.email)
        if existing_user:
            raise ValueError("Email already registered")

        # For development, auto-activate users to skip email verification
        is_development = os.getenv("ENVIRONMENT", "development").lower() == "development"

        # Create user
        user = self.user_repo.create_user(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            role=user_data.role,
            dealership_id=user_data.dealership_id,
            auto_activate=is_development
        )

        return user

    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password.

        Args:
            email: User email
            password: User password

        Returns:
            User instance if authentication successful, None otherwise
        """
        user = self.user_repo.authenticate(email, password)

        if not user:
            return None

        # Check if user is active
        if user.status != UserStatus.ACTIVE:
            return None

        # Check if account is locked
        if user.is_locked():
            return None

        # For development, allow login without email verification
        is_development = os.getenv("ENVIRONMENT", "development").lower() == "development"
        if not is_development and not user.email_verified:
            return None

        return user

    def login(self, email: str, password: str) -> Optional[LoginResponse]:
        """
        Login user and generate tokens.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            Login response with tokens or None if authentication failed
        """
        user = self.authenticate_user(email, password)
        if not user:
            return None
        
        # Create token payload
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "role": user.role.value,
            "dealership_id": str(user.dealership_id) if user.dealership_id else None
        }
        
        # Generate tokens
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token({"sub": str(user.id)})
        
        # Convert user to schema
        user_schema = CurrentUser.from_orm(user)
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_schema
        )

    def refresh_access_token(self, refresh_token: str) -> Optional[RefreshTokenResponse]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            New access token or None if refresh token is invalid
        """
        # Verify refresh token
        payload = verify_token(refresh_token, token_type="refresh")
        if not payload:
            return None
        
        # Get user
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        user = self.user_repo.get_by_id(UUID(user_id))
        if not user or user.status != UserStatus.ACTIVE:
            return None
        
        # Create new access token
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "role": user.role.value,
            "dealership_id": str(user.dealership_id) if user.dealership_id else None
        }
        
        access_token = create_access_token(token_data)
        
        return RefreshTokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

    def get_current_user(self, token: str) -> Optional[User]:
        """
        Get current user from access token.
        
        Args:
            token: JWT access token
            
        Returns:
            User instance or None if token is invalid
        """
        payload = verify_token(token, token_type="access")
        if not payload:
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        user = self.user_repo.get_by_id(UUID(user_id))
        if not user or user.status != UserStatus.ACTIVE:
            return None
        
        return user

    def get_user_by_api_key(self, api_key: str) -> Optional[User]:
        """
        Get user by API key.
        
        Args:
            api_key: API key
            
        Returns:
            User instance or None if API key is invalid
        """
        user = self.user_repo.get_by_api_key(api_key)
        if not user or user.status != UserStatus.ACTIVE:
            return None
        
        return user

    def generate_api_key(self, user_id: UUID) -> Optional[str]:
        """
        Generate API key for user.
        
        Args:
            user_id: User UUID
            
        Returns:
            Generated API key or None if user not found
        """
        api_key = create_api_key()
        user = self.user_repo.set_api_key(user_id, api_key)
        
        if user:
            return api_key
        return None

    def initiate_password_reset(self, email: str) -> Optional[str]:
        """
        Initiate password reset process.
        
        Args:
            email: User email
            
        Returns:
            Reset token or None if user not found
        """
        user = self.user_repo.get_by_email(email)
        if not user:
            return None
        
        # Generate reset token
        reset_token = generate_password_reset_token()
        expires_at = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
        
        self.user_repo.set_password_reset_token(
            user.id,
            reset_token,
            expires_at.isoformat()
        )
        
        return reset_token

    def reset_password(self, token: str, new_password: str) -> bool:
        """
        Reset user password using reset token.
        
        Args:
            token: Password reset token
            new_password: New password
            
        Returns:
            True if password was reset successfully, False otherwise
        """
        # Find user with this reset token
        user = self.db.query(User).filter(
            User.password_reset_token == token
        ).first()
        
        if not user:
            return False
        
        # Check if token is expired
        if user.password_reset_expires:
            expires_at = datetime.fromisoformat(user.password_reset_expires)
            if datetime.utcnow() > expires_at:
                return False
        
        # Update password and clear reset token
        password_hash = get_password_hash(new_password)
        self.user_repo.update(user.id, {
            "password_hash": password_hash,
            "password_reset_token": None,
            "password_reset_expires": None
        })
        
        return True

    def change_password(
        self,
        user_id: UUID,
        current_password: str,
        new_password: str
    ) -> bool:
        """
        Change user password.
        
        Args:
            user_id: User UUID
            current_password: Current password
            new_password: New password
            
        Returns:
            True if password was changed successfully, False otherwise
        """
        user = self.user_repo.get_by_id(user_id)
        if not user:
            return False
        
        # Verify current password
        if not verify_password(current_password, user.password_hash):
            return False
        
        # Update password
        self.user_repo.update_password(user_id, new_password)
        return True
