import type React from "react"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex h-screen">
      <div className="w-64 bg-gray-100 dark:bg-gray-900 border-r">
        <div className="p-4">
          <h2 className="text-lg font-semibold">Admin Panel</h2>
        </div>
        <nav className="p-4">
          <ul className="space-y-2">
            <li><a href="/admin" className="block p-2 hover:bg-gray-200 dark:hover:bg-gray-800 rounded">Dashboard</a></li>
            <li><a href="/admin/relatorios" className="block p-2 hover:bg-gray-200 dark:hover:bg-gray-800 rounded">Relatórios</a></li>
          </ul>
        </nav>
      </div>
      <div className="flex-1 overflow-auto">
        <main className="h-full p-6">{children}</main>
      </div>
    </div>
  )
}

