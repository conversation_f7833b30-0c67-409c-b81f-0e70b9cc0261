"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { PasswordStrength } from "@/components/password-strength"
import { Separator } from "@/components/ui/separator"
import {
  AlertCircle,
  CheckCircle,
  Key,
  Settings,
  CreditCard,
  Shield,
  BarChart,
  Search,
  Facebook,
  Globe,
  AlertTriangle,
  Save,
  Loader2,
  QrCode,
  Eye,
  EyeOff,
  Upload,
  ImageIcon,
} from "lucide-react"

export default function AdminSettingsPage() {
  // Estados para os diferentes formulários
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const [maintenanceMode, setMaintenanceMode] = useState(false)
  const [maintenanceMessage, setMaintenanceMessage] = useState(
    "O site está temporariamente em manutenção. Por favor, volte mais tarde.",
  )

  const [asaasApiKey, setAsaasApiKey] = useState("")
  const [asaasEnvironment, setAsaasEnvironment] = useState("sandbox")

  const [pixKey, setPixKey] = useState("")
  const [pixKeyType, setPixKeyType] = useState("cnpj")
  const [pixReceiverName, setPixReceiverName] = useState("")
  const [pixReceiverDocument, setPixReceiverDocument] = useState("")

  const [recaptchaSiteKey, setRecaptchaSiteKey] = useState("")
  const [recaptchaSecretKey, setRecaptchaSecretKey] = useState("")
  const [recaptchaEnabled, setRecaptchaEnabled] = useState(false)

  const [facebookPixelId, setFacebookPixelId] = useState("")
  const [googleAnalyticsId, setGoogleAnalyticsId] = useState("")
  const [googleTagManagerId, setGoogleTagManagerId] = useState("")

  const [metaTitle, setMetaTitle] = useState("AutoParts - Encontre peças para seu veículo")
  const [metaDescription, setMetaDescription] = useState("Plataforma de busca de peças automotivas")
  const [metaKeywords, setMetaKeywords] = useState("peças, automotivas, carros, veículos, autopeças")
  const [robotsTxt, setRobotsTxt] = useState("User-agent: *\nAllow: /")

  // Estados para feedback de salvamento
  const [saveStatus, setSaveStatus] = useState<"idle" | "saving" | "success" | "error">("idle")
  const [activeTab, setActiveTab] = useState("security")

  const [logo, setLogo] = useState<File | null>(null)
  const [favicon, setFavicon] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>("/placeholder.svg?height=60&width=180")
  const [faviconPreview, setFaviconPreview] = useState<string | null>("/placeholder.svg?height=32&width=32")

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setLogo(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.result) {
          setLogoPreview(e.target.result as string)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const handleFaviconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setFavicon(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.result) {
          setFaviconPreview(e.target.result as string)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const removeLogo = () => {
    setLogo(null)
    setLogoPreview("/placeholder.svg?height=60&width=180")
  }

  const removeFavicon = () => {
    setFavicon(null)
    setFaviconPreview("/placeholder.svg?height=32&width=32")
  }

  // Função para salvar configurações
  const handleSave = (section: string) => {
    setSaveStatus("saving")

    // Simulação de salvamento
    setTimeout(() => {
      setSaveStatus("success")

      // Resetar status após 3 segundos
      setTimeout(() => {
        setSaveStatus("idle")
      }, 3000)
    }, 1500)

    console.log(`Salvando configurações da seção: ${section}`)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
        <p className="text-muted-foreground">Gerencie as configurações do sistema</p>
      </div>

      {saveStatus === "success" && (
        <Alert className="bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Configurações salvas com sucesso!</AlertTitle>
          <AlertDescription>As alterações foram aplicadas ao sistema.</AlertDescription>
        </Alert>
      )}

      {saveStatus === "error" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro ao salvar configurações</AlertTitle>
          <AlertDescription>Ocorreu um erro ao salvar as configurações. Por favor, tente novamente.</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 md:grid-cols-6">
          <TabsTrigger value="security">Segurança</TabsTrigger>
          <TabsTrigger value="system">Sistema</TabsTrigger>
          <TabsTrigger value="payment">Pagamento</TabsTrigger>
          <TabsTrigger value="verification">Verificação</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        {/* Segurança */}
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Alterar Senha do Administrador
              </CardTitle>
              <CardDescription>
                Altere a senha da sua conta de administrador. Recomendamos usar uma senha forte e única.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="current-password">Senha Atual</Label>
                <div className="relative">
                  <Input
                    id="current-password"
                    type={showCurrentPassword ? "text" : "password"}
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-password">Nova Senha</Label>
                <div className="relative">
                  <Input
                    id="new-password"
                    type={showNewPassword ? "text" : "password"}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                <PasswordStrength password={newPassword} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirmar Nova Senha</Label>
                <div className="relative">
                  <Input
                    id="confirm-password"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {confirmPassword && newPassword !== confirmPassword && (
                  <p className="text-sm text-red-500">As senhas não coincidem</p>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => handleSave("security")}
                disabled={
                  !currentPassword || !newPassword || newPassword !== confirmPassword || saveStatus === "saving"
                }
              >
                {saveStatus === "saving" && activeTab === "security" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Sistema */}
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configurações do Sistema
              </CardTitle>
              <CardDescription>Configure as opções gerais do sistema, incluindo modo de manutenção.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="maintenance-mode">Modo de Manutenção</Label>
                    <p className="text-sm text-muted-foreground">
                      Ative para exibir uma página de manutenção para todos os visitantes
                    </p>
                  </div>
                  <Switch id="maintenance-mode" checked={maintenanceMode} onCheckedChange={setMaintenanceMode} />
                </div>

                {maintenanceMode && (
                  <div className="space-y-2">
                    <Label htmlFor="maintenance-message">Mensagem de Manutenção</Label>
                    <Textarea
                      id="maintenance-message"
                      placeholder="Mensagem a ser exibida durante a manutenção"
                      value={maintenanceMessage}
                      onChange={(e) => setMaintenanceMessage(e.target.value)}
                      rows={3}
                    />
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="flex items-center gap-2 text-lg font-medium">
                  <ImageIcon className="h-5 w-5" />
                  Identidade Visual
                </h3>

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="logo-upload">Logo do Site</Label>
                      <div className="flex flex-col gap-4">
                        <div className="flex h-[100px] w-full items-center justify-center rounded-md border border-dashed p-4">
                          {logoPreview && (
                            <div className="relative h-full">
                              <img
                                src={logoPreview || "/placeholder.svg"}
                                alt="Logo preview"
                                className="h-full max-h-[80px] w-auto object-contain"
                              />
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <div className="relative flex-1">
                            <Input
                              id="logo-upload"
                              type="file"
                              accept="image/png, image/jpeg, image/svg+xml"
                              className="absolute inset-0 cursor-pointer opacity-0"
                              onChange={handleLogoChange}
                            />
                            <Button variant="outline" className="w-full">
                              <Upload className="mr-2 h-4 w-4" />
                              Selecionar Logo
                            </Button>
                          </div>
                          <Button variant="outline" onClick={removeLogo} disabled={!logo}>
                            Remover
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Formatos aceitos: PNG, JPG, SVG. Tamanho máximo: 2MB. Dimensão recomendada: 180x60px.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="favicon-upload">Favicon</Label>
                      <div className="flex flex-col gap-4">
                        <div className="flex h-[100px] w-full items-center justify-center rounded-md border border-dashed p-4">
                          {faviconPreview && (
                            <div className="relative h-full">
                              <img
                                src={faviconPreview || "/placeholder.svg"}
                                alt="Favicon preview"
                                className="h-full max-h-[32px] w-auto object-contain"
                              />
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <div className="relative flex-1">
                            <Input
                              id="favicon-upload"
                              type="file"
                              accept="image/png, image/x-icon, image/svg+xml"
                              className="absolute inset-0 cursor-pointer opacity-0"
                              onChange={handleFaviconChange}
                            />
                            <Button variant="outline" className="w-full">
                              <Upload className="mr-2 h-4 w-4" />
                              Selecionar Favicon
                            </Button>
                          </div>
                          <Button variant="outline" onClick={removeFavicon} disabled={!favicon}>
                            Remover
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Formatos aceitos: ICO, PNG, SVG. Tamanho máximo: 1MB. Dimensão recomendada: 32x32px ou
                          16x16px.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Configurações de Email</h3>

                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="email-from">Email de Envio</Label>
                    <Input id="email-from" placeholder="<EMAIL>" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email-name">Nome de Exibição</Label>
                    <Input id="email-name" placeholder="AutoParts" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtp-host">Servidor SMTP</Label>
                  <Input id="smtp-host" placeholder="smtp.example.com" />
                </div>

                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="smtp-user">Usuário SMTP</Label>
                    <Input id="smtp-user" placeholder="<EMAIL>" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="smtp-password">Senha SMTP</Label>
                    <Input id="smtp-password" type="password" placeholder="••••••••" />
                  </div>
                </div>

                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="smtp-port">Porta SMTP</Label>
                    <Input id="smtp-port" placeholder="587" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="smtp-security">Segurança</Label>
                    <select
                      id="smtp-security"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="tls">TLS</option>
                      <option value="ssl">SSL</option>
                      <option value="none">Nenhuma</option>
                    </select>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSave("system")} disabled={saveStatus === "saving"}>
                {saveStatus === "saving" && activeTab === "system" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Pagamento */}
        <TabsContent value="payment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Configurações de Pagamento
              </CardTitle>
              <CardDescription>Configure os gateways de pagamento e métodos de recebimento.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">ASAAS</h3>

                <div className="space-y-2">
                  <Label htmlFor="asaas-api-key">Chave de API</Label>
                  <Input
                    id="asaas-api-key"
                    placeholder="$aas_..."
                    value={asaasApiKey}
                    onChange={(e) => setAsaasApiKey(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="asaas-environment">Ambiente</Label>
                  <select
                    id="asaas-environment"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={asaasEnvironment}
                    onChange={(e) => setAsaasEnvironment(e.target.value)}
                  >
                    <option value="sandbox">Sandbox (Testes)</option>
                    <option value="production">Produção</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="asaas-enabled" />
                  <Label htmlFor="asaas-enabled">Habilitar ASAAS</Label>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="flex items-center gap-2 text-lg font-medium">
                  <QrCode className="h-5 w-5" />
                  Configurações de PIX
                </h3>

                <div className="space-y-2">
                  <Label htmlFor="pix-key">Chave PIX</Label>
                  <Input
                    id="pix-key"
                    placeholder="Sua chave PIX"
                    value={pixKey}
                    onChange={(e) => setPixKey(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pix-key-type">Tipo de Chave</Label>
                  <select
                    id="pix-key-type"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={pixKeyType}
                    onChange={(e) => setPixKeyType(e.target.value)}
                  >
                    <option value="cpf">CPF</option>
                    <option value="cnpj">CNPJ</option>
                    <option value="email">Email</option>
                    <option value="phone">Telefone</option>
                    <option value="random">Chave Aleatória</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pix-receiver-name">Nome do Recebedor</Label>
                  <Input
                    id="pix-receiver-name"
                    placeholder="Nome completo ou razão social"
                    value={pixReceiverName}
                    onChange={(e) => setPixReceiverName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pix-receiver-document">Documento do Recebedor</Label>
                  <Input
                    id="pix-receiver-document"
                    placeholder="CPF ou CNPJ"
                    value={pixReceiverDocument}
                    onChange={(e) => setPixReceiverDocument(e.target.value)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="pix-enabled" />
                  <Label htmlFor="pix-enabled">Habilitar PIX</Label>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSave("payment")} disabled={saveStatus === "saving"}>
                {saveStatus === "saving" && activeTab === "payment" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Verificação */}
        <TabsContent value="verification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Configurações de Verificação
              </CardTitle>
              <CardDescription>Configure os sistemas de verificação e segurança.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Google reCAPTCHA</h3>

                <Alert className="bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Informação</AlertTitle>
                  <AlertDescription>
                    Obtenha suas chaves do reCAPTCHA no{" "}
                    <a
                      href="https://www.google.com/recaptcha/admin"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium underline"
                    >
                      Console do Google reCAPTCHA
                    </a>
                    .
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="recaptcha-site-key">Chave do Site</Label>
                  <Input
                    id="recaptcha-site-key"
                    placeholder="6LdXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                    value={recaptchaSiteKey}
                    onChange={(e) => setRecaptchaSiteKey(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recaptcha-secret-key">Chave Secreta</Label>
                  <Input
                    id="recaptcha-secret-key"
                    placeholder="6LdXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                    value={recaptchaSecretKey}
                    onChange={(e) => setRecaptchaSecretKey(e.target.value)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="recaptcha-enabled" checked={recaptchaEnabled} onCheckedChange={setRecaptchaEnabled} />
                  <Label htmlFor="recaptcha-enabled">Habilitar reCAPTCHA</Label>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Segurança Adicional</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="two-factor">Autenticação de Dois Fatores</Label>
                    <p className="text-sm text-muted-foreground">
                      Exigir autenticação de dois fatores para contas de administrador
                    </p>
                  </div>
                  <Switch id="two-factor" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="login-attempts">Limite de Tentativas de Login</Label>
                    <p className="text-sm text-muted-foreground">
                      Bloquear conta após múltiplas tentativas de login malsucedidas
                    </p>
                  </div>
                  <Switch id="login-attempts" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-attempts">Número Máximo de Tentativas</Label>
                  <Input id="max-attempts" type="number" min="1" max="10" defaultValue="5" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSave("verification")} disabled={saveStatus === "saving"}>
                {saveStatus === "saving" && activeTab === "verification" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Analytics */}
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="h-5 w-5" />
                Configurações de Analytics
              </CardTitle>
              <CardDescription>Configure as ferramentas de rastreamento e análise.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="flex items-center gap-2 text-lg font-medium">
                  <Facebook className="h-5 w-5 text-blue-600" />
                  Facebook Pixel
                </h3>

                <div className="space-y-2">
                  <Label htmlFor="facebook-pixel-id">ID do Pixel</Label>
                  <Input
                    id="facebook-pixel-id"
                    placeholder="123456789012345"
                    value={facebookPixelId}
                    onChange={(e) => setFacebookPixelId(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">Exemplo: 123456789012345</p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="facebook-pixel-enabled" />
                  <Label htmlFor="facebook-pixel-enabled">Habilitar Facebook Pixel</Label>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Google Analytics</h3>

                <div className="space-y-2">
                  <Label htmlFor="google-analytics-id">ID de Medição</Label>
                  <Input
                    id="google-analytics-id"
                    placeholder="G-XXXXXXXXXX ou UA-XXXXXXXX-X"
                    value={googleAnalyticsId}
                    onChange={(e) => setGoogleAnalyticsId(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Exemplo: G-XXXXXXXXXX (GA4) ou UA-XXXXXXXX-X (Universal Analytics)
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="google-analytics-enabled" />
                  <Label htmlFor="google-analytics-enabled">Habilitar Google Analytics</Label>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Google Tag Manager</h3>

                <div className="space-y-2">
                  <Label htmlFor="google-tag-manager-id">ID do Contêiner</Label>
                  <Input
                    id="google-tag-manager-id"
                    placeholder="GTM-XXXXXXX"
                    value={googleTagManagerId}
                    onChange={(e) => setGoogleTagManagerId(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">Exemplo: GTM-XXXXXXX</p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="google-tag-manager-enabled" />
                  <Label htmlFor="google-tag-manager-enabled">Habilitar Google Tag Manager</Label>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSave("analytics")} disabled={saveStatus === "saving"}>
                {saveStatus === "saving" && activeTab === "analytics" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* SEO */}
        <TabsContent value="seo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Configurações de SEO
              </CardTitle>
              <CardDescription>
                Configure as meta tags e outras configurações de otimização para motores de busca.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Meta Tags</h3>

                <div className="space-y-2">
                  <Label htmlFor="meta-title">Título do Site</Label>
                  <Input
                    id="meta-title"
                    placeholder="AutoParts - Encontre peças para seu veículo"
                    value={metaTitle}
                    onChange={(e) => setMetaTitle(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">Recomendado: até 60 caracteres</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="meta-description">Descrição</Label>
                  <Textarea
                    id="meta-description"
                    placeholder="Plataforma de busca de peças automotivas"
                    value={metaDescription}
                    onChange={(e) => setMetaDescription(e.target.value)}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">Recomendado: até 160 caracteres</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="meta-keywords">Palavras-chave</Label>
                  <Input
                    id="meta-keywords"
                    placeholder="peças, automotivas, carros, veículos, autopeças"
                    value={metaKeywords}
                    onChange={(e) => setMetaKeywords(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">Separe as palavras-chave por vírgulas</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="flex items-center gap-2 text-lg font-medium">
                  <Globe className="h-5 w-5" />
                  Configurações Avançadas
                </h3>

                <div className="space-y-2">
                  <Label htmlFor="robots-txt">Arquivo robots.txt</Label>
                  <Textarea
                    id="robots-txt"
                    className="font-mono text-sm"
                    value={robotsTxt}
                    onChange={(e) => setRobotsTxt(e.target.value)}
                    rows={6}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="sitemap-enabled" defaultChecked />
                  <Label htmlFor="sitemap-enabled">Gerar sitemap.xml automaticamente</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="canonical-urls" defaultChecked />
                  <Label htmlFor="canonical-urls">Usar URLs canônicas</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="structured-data" defaultChecked />
                  <Label htmlFor="structured-data">Incluir dados estruturados (Schema.org)</Label>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="flex items-center gap-2 text-lg font-medium">
                  <AlertTriangle className="h-5 w-5" />
                  Verificação de Propriedade
                </h3>

                <div className="space-y-2">
                  <Label htmlFor="google-verification">Google Search Console</Label>
                  <Input
                    id="google-verification"
                    placeholder="google-site-verification=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bing-verification">Bing Webmaster Tools</Label>
                  <Input id="bing-verification" placeholder="msvalidate.01=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="facebook-domain-verification">Facebook Domain Verification</Label>
                  <Input
                    id="facebook-domain-verification"
                    placeholder="facebook-domain-verification=xxxxxxxxxxxxxxxxxxxxxxxxxx"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSave("seo")} disabled={saveStatus === "saving"}>
                {saveStatus === "saving" && activeTab === "seo" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

