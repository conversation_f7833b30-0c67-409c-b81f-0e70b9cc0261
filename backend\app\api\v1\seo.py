"""
SEO-optimized endpoints for search engines and social media.
Provides structured data, meta tags, and SEO-friendly URLs.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi.responses import HTMLResponse, PlainTextResponse
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import json

from app.core.database import get_db
from app.core.logging import get_logger
from app.core.seo_cache import seo_cache
from app.models.part import Part
from app.models.dealership import Dealership
from app.models.inventory import Inventory
# from app.schemas.part import PartWithInventory

logger = get_logger("app.api.seo")

router = APIRouter()


class SEOMetaTags:
    """Generate SEO meta tags for different content types."""
    
    @staticmethod
    def generate_part_meta(part: Part, inventory_count: int = 0) -> Dict[str, str]:
        """Generate meta tags for a specific part."""
        title = f"{part.name} - {part.brand} | Peça Original {part.part_number}"
        description = f"Encontre {part.name} da {part.brand} (código {part.part_number}). {inventory_count} ofertas disponíveis. Peças originais com garantia."
        
        return {
            "title": title,
            "description": description,
            "keywords": f"{part.name}, {part.brand}, {part.part_number}, peças automotivas, peças originais",
            "og:title": title,
            "og:description": description,
            "og:type": "product",
            "og:url": f"/parts/{part.part_number}",
            "twitter:card": "summary_large_image",
            "twitter:title": title,
            "twitter:description": description,
        }
    
    @staticmethod
    def generate_search_meta(query: str, results_count: int) -> Dict[str, str]:
        """Generate meta tags for search results."""
        title = f"Busca por '{query}' - {results_count} resultados encontrados"
        description = f"Encontre peças automotivas para '{query}'. {results_count} opções disponíveis de concessionárias verificadas."
        
        return {
            "title": title,
            "description": description,
            "keywords": f"{query}, peças automotivas, busca peças, {query} original",
            "og:title": title,
            "og:description": description,
            "og:type": "website",
            "twitter:card": "summary",
            "twitter:title": title,
            "twitter:description": description,
        }


class StructuredData:
    """Generate JSON-LD structured data for SEO."""
    
    @staticmethod
    def generate_part_structured_data(part: Part, inventory_items: List[Inventory]) -> Dict[str, Any]:
        """Generate structured data for a part with inventory."""
        offers = []
        for item in inventory_items:
            offers.append({
                "@type": "Offer",
                "price": str(item.price),
                "priceCurrency": "BRL",
                "availability": "https://schema.org/InStock" if item.is_available else "https://schema.org/OutOfStock",
                "condition": f"https://schema.org/{item.condition.value.title()}Condition",
                "seller": {
                    "@type": "Organization",
                    "name": item.dealership.name if item.dealership else "Concessionária"
                }
            })
        
        return {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": part.name,
            "description": part.description or f"Peça automotiva {part.name} da marca {part.brand}",
            "brand": {
                "@type": "Brand",
                "name": part.brand
            },
            "mpn": part.part_number,  # Manufacturer Part Number
            "category": part.category,
            "offers": offers,
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.5",
                "reviewCount": "10"
            } if offers else None
        }
    
    @staticmethod
    def generate_dealership_structured_data(dealership: Dealership) -> Dict[str, Any]:
        """Generate structured data for a dealership."""
        return {
            "@context": "https://schema.org/",
            "@type": "AutoDealer",
            "name": dealership.name,
            "address": {
                "@type": "PostalAddress",
                "streetAddress": dealership.address,
                "addressLocality": dealership.city,
                "addressRegion": dealership.state,
                "postalCode": dealership.zip_code,
                "addressCountry": "BR"
            },
            "telephone": dealership.phone,
            "email": dealership.email,
            "url": f"/dealerships/{dealership.id}"
        }


@router.get("/parts/{part_number}", response_class=HTMLResponse)
async def get_part_seo_page(
    part_number: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    SEO-optimized page for a specific part with structured data and meta tags.
    Returns HTML with embedded JSON-LD for search engines.
    """
    try:
        # Check cache first
        cached_html = seo_cache.get_cached_part_page(part_number)
        if cached_html:
            return HTMLResponse(content=cached_html)

        # Get part by part number
        part = db.query(Part).filter(Part.part_number == part_number).first()
        if not part:
            raise HTTPException(status_code=404, detail="Part not found")
        
        # Get inventory for this part
        inventory_items = db.query(Inventory).filter(
            Inventory.part_id == part.id,
            Inventory.is_available == True
        ).all()
        
        # Generate meta tags
        meta_tags = SEOMetaTags.generate_part_meta(part, len(inventory_items))
        
        # Generate structured data
        structured_data = StructuredData.generate_part_structured_data(part, inventory_items)
        
        # Generate HTML response
        html_content = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{meta_tags['title']}</title>
            <meta name="description" content="{meta_tags['description']}">
            <meta name="keywords" content="{meta_tags['keywords']}">
            
            <!-- Open Graph -->
            <meta property="og:title" content="{meta_tags['og:title']}">
            <meta property="og:description" content="{meta_tags['og:description']}">
            <meta property="og:type" content="{meta_tags['og:type']}">
            <meta property="og:url" content="{request.base_url}{meta_tags['og:url']}">
            
            <!-- Twitter -->
            <meta name="twitter:card" content="{meta_tags['twitter:card']}">
            <meta name="twitter:title" content="{meta_tags['twitter:title']}">
            <meta name="twitter:description" content="{meta_tags['twitter:description']}">
            
            <!-- Canonical URL -->
            <link rel="canonical" href="{request.base_url}parts/{part_number}">
            
            <!-- Structured Data -->
            <script type="application/ld+json">
            {json.dumps(structured_data, ensure_ascii=False, indent=2)}
            </script>
        </head>
        <body>
            <h1>{part.name} - {part.brand}</h1>
            <p><strong>Código:</strong> {part.part_number}</p>
            <p><strong>Categoria:</strong> {part.category}</p>
            {f'<p><strong>Descrição:</strong> {part.description}</p>' if part.description else ''}
            
            <h2>Ofertas Disponíveis ({len(inventory_items)})</h2>
            {''.join([f'<div><p>R$ {item.price:.2f} - {item.condition.value.title()}</p></div>' for item in inventory_items])}
            
            <!-- Redirect to frontend -->
            <script>
                if (window.location.pathname.startsWith('/api/')) {{
                    window.location.href = '/parts/{part_number}';
                }}
            </script>
        </body>
        </html>
        """

        # Cache the generated HTML
        seo_cache.cache_part_page(part_number, html_content)

        return HTMLResponse(content=html_content)
        
    except Exception as e:
        logger.error(f"Error generating SEO page for part {part_number}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/parts/{part_number}/json")
async def get_part_seo_data(
    part_number: str,
    db: Session = Depends(get_db)
):
    """
    Get SEO data for a part in JSON format.
    Useful for frontend applications to generate meta tags.
    """
    try:
        # Get part by part number
        part = db.query(Part).filter(Part.part_number == part_number).first()
        if not part:
            raise HTTPException(status_code=404, detail="Part not found")
        
        # Get inventory for this part
        inventory_items = db.query(Inventory).filter(
            Inventory.part_id == part.id,
            Inventory.is_available == True
        ).all()
        
        # Generate meta tags and structured data
        meta_tags = SEOMetaTags.generate_part_meta(part, len(inventory_items))
        structured_data = StructuredData.generate_part_structured_data(part, inventory_items)
        
        return {
            "meta_tags": meta_tags,
            "structured_data": structured_data,
            "part": {
                "id": str(part.id),
                "part_number": part.part_number,
                "name": part.name,
                "brand": part.brand,
                "category": part.category
            },
            "inventory_count": len(inventory_items)
        }
        
    except Exception as e:
        logger.error(f"Error getting SEO data for part {part_number}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search/{query}", response_class=HTMLResponse)
async def get_search_seo_page(
    query: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    SEO-optimized page for search results with structured data.
    """
    try:
        # Search for parts
        parts = db.query(Part).filter(
            Part.name.ilike(f"%{query}%") |
            Part.part_number.ilike(f"%{query}%") |
            Part.brand.ilike(f"%{query}%")
        ).limit(20).all()
        
        # Generate meta tags
        meta_tags = SEOMetaTags.generate_search_meta(query, len(parts))
        
        # Generate HTML response
        html_content = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{meta_tags['title']}</title>
            <meta name="description" content="{meta_tags['description']}">
            <meta name="keywords" content="{meta_tags['keywords']}">
            
            <!-- Open Graph -->
            <meta property="og:title" content="{meta_tags['og:title']}">
            <meta property="og:description" content="{meta_tags['og:description']}">
            <meta property="og:type" content="{meta_tags['og:type']}">
            
            <!-- Twitter -->
            <meta name="twitter:card" content="{meta_tags['twitter:card']}">
            <meta name="twitter:title" content="{meta_tags['twitter:title']}">
            <meta name="twitter:description" content="{meta_tags['twitter:description']}">
            
            <!-- Canonical URL -->
            <link rel="canonical" href="{request.base_url}search/{query}">
        </head>
        <body>
            <h1>Busca por: {query}</h1>
            <p>{len(parts)} resultados encontrados</p>
            
            <div>
                {''.join([f'<div><h3><a href="/parts/{part.part_number}">{part.name} - {part.brand}</a></h3><p>Código: {part.part_number}</p></div>' for part in parts])}
            </div>
            
            <!-- Redirect to frontend -->
            <script>
                if (window.location.pathname.startsWith('/api/')) {{
                    window.location.href = '/search?q={query}';
                }}
            </script>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)

    except Exception as e:
        logger.error(f"Error generating SEO search page for query {query}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/dealerships/{dealership_id}", response_class=HTMLResponse)
async def get_dealership_seo_page(
    dealership_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    SEO-optimized page for a specific dealership with structured data.
    """
    try:
        # Get dealership by ID
        dealership = db.query(Dealership).filter(Dealership.id == dealership_id).first()
        if not dealership:
            raise HTTPException(status_code=404, detail="Dealership not found")

        # Get inventory count for this dealership
        inventory_count = db.query(Inventory).filter(
            Inventory.dealership_id == dealership.id,
            Inventory.is_available == True
        ).count()

        # Generate meta tags
        title = f"{dealership.name} - Concessionária em {dealership.city}, {dealership.state}"
        description = f"Encontre peças automotivas na {dealership.name} em {dealership.city}. {inventory_count} peças disponíveis. Concessionária verificada."

        meta_tags = {
            "title": title,
            "description": description,
            "keywords": f"{dealership.name}, concessionária, {dealership.city}, {dealership.state}, peças automotivas",
            "og:title": title,
            "og:description": description,
            "og:type": "business.business",
            "og:url": f"/dealerships/{dealership.id}",
            "twitter:card": "summary",
            "twitter:title": title,
            "twitter:description": description,
        }

        # Generate structured data
        structured_data = StructuredData.generate_dealership_structured_data(dealership)

        # Generate HTML response
        html_content = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{meta_tags['title']}</title>
            <meta name="description" content="{meta_tags['description']}">
            <meta name="keywords" content="{meta_tags['keywords']}">

            <!-- Open Graph -->
            <meta property="og:title" content="{meta_tags['og:title']}">
            <meta property="og:description" content="{meta_tags['og:description']}">
            <meta property="og:type" content="{meta_tags['og:type']}">
            <meta property="og:url" content="{request.base_url}{meta_tags['og:url']}">

            <!-- Twitter -->
            <meta name="twitter:card" content="{meta_tags['twitter:card']}">
            <meta name="twitter:title" content="{meta_tags['twitter:title']}">
            <meta name="twitter:description" content="{meta_tags['twitter:description']}">

            <!-- Canonical URL -->
            <link rel="canonical" href="{request.base_url}dealerships/{dealership_id}">

            <!-- Structured Data -->
            <script type="application/ld+json">
            {json.dumps(structured_data, ensure_ascii=False, indent=2)}
            </script>
        </head>
        <body>
            <h1>{dealership.name}</h1>
            <p><strong>Localização:</strong> {dealership.city}, {dealership.state}</p>
            <p><strong>Endereço:</strong> {dealership.address}</p>
            <p><strong>Telefone:</strong> {dealership.phone}</p>
            <p><strong>Email:</strong> {dealership.email}</p>

            <h2>Inventário ({inventory_count} peças disponíveis)</h2>
            <p>Esta concessionária possui {inventory_count} peças automotivas em estoque.</p>

            <!-- Redirect to frontend -->
            <script>
                if (window.location.pathname.startsWith('/api/')) {{
                    window.location.href = '/dealerships/{dealership_id}';
                }}
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content)

    except Exception as e:
        logger.error(f"Error generating SEO page for dealership {dealership_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/categories/{category}", response_class=HTMLResponse)
async def get_category_seo_page(
    category: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    SEO-optimized page for a specific category with parts listing.
    """
    try:
        # Get parts in this category
        parts = db.query(Part).filter(
            Part.category.ilike(f"%{category}%")
        ).limit(50).all()

        if not parts:
            raise HTTPException(status_code=404, detail="Category not found")

        # Generate meta tags
        title = f"Peças de {category.title()} - Catálogo Completo"
        description = f"Encontre peças de {category} para seu veículo. {len(parts)} opções disponíveis de concessionárias verificadas."

        meta_tags = {
            "title": title,
            "description": description,
            "keywords": f"{category}, peças automotivas, {category} original, peças {category}",
            "og:title": title,
            "og:description": description,
            "og:type": "website",
            "og:url": f"/categories/{category}",
            "twitter:card": "summary",
            "twitter:title": title,
            "twitter:description": description,
        }

        # Generate HTML response
        html_content = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{meta_tags['title']}</title>
            <meta name="description" content="{meta_tags['description']}">
            <meta name="keywords" content="{meta_tags['keywords']}">

            <!-- Open Graph -->
            <meta property="og:title" content="{meta_tags['og:title']}">
            <meta property="og:description" content="{meta_tags['og:description']}">
            <meta property="og:type" content="{meta_tags['og:type']}">
            <meta property="og:url" content="{request.base_url}{meta_tags['og:url']}">

            <!-- Twitter -->
            <meta name="twitter:card" content="{meta_tags['twitter:card']}">
            <meta name="twitter:title" content="{meta_tags['twitter:title']}">
            <meta name="twitter:description" content="{meta_tags['twitter:description']}">

            <!-- Canonical URL -->
            <link rel="canonical" href="{request.base_url}categories/{category}">
        </head>
        <body>
            <h1>Peças de {category.title()}</h1>
            <p>{len(parts)} peças encontradas na categoria {category}</p>

            <div>
                {''.join([f'<div><h3><a href="/parts/{part.part_number}">{part.name} - {part.brand}</a></h3><p>Código: {part.part_number}</p></div>' for part in parts[:20]])}
            </div>

            <!-- Redirect to frontend -->
            <script>
                if (window.location.pathname.startsWith('/api/')) {{
                    window.location.href = '/categories/{category}';
                }}
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content)

    except Exception as e:
        logger.error(f"Error generating SEO page for category {category}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sitemap.xml", response_class=PlainTextResponse)
async def get_sitemap(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Generate XML sitemap for search engines.
    Includes all parts, dealerships, and static pages.
    """
    try:
        # Check cache first
        cached_sitemap = seo_cache.get_cached_sitemap()
        if cached_sitemap:
            return PlainTextResponse(
                content=cached_sitemap,
                media_type="application/xml"
            )

        base_url = str(request.base_url).rstrip('/')

        # Start sitemap
        sitemap_content = ['<?xml version="1.0" encoding="UTF-8"?>']
        sitemap_content.append('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">')

        # Add static pages
        static_pages = [
            ('/', '1.0', 'daily'),
            ('/search', '0.8', 'daily'),
            ('/dealerships', '0.7', 'weekly'),
            ('/about', '0.5', 'monthly'),
            ('/contact', '0.5', 'monthly'),
        ]

        for url, priority, changefreq in static_pages:
            sitemap_content.append(f"""
  <url>
    <loc>{base_url}{url}</loc>
    <lastmod>{datetime.now(timezone.utc).strftime('%Y-%m-%d')}</lastmod>
    <changefreq>{changefreq}</changefreq>
    <priority>{priority}</priority>
  </url>""")

        # Add parts
        parts = db.query(Part).all()
        for part in parts:
            sitemap_content.append(f"""
  <url>
    <loc>{base_url}/parts/{part.part_number}</loc>
    <lastmod>{part.updated_at.strftime('%Y-%m-%d') if part.updated_at else datetime.now(timezone.utc).strftime('%Y-%m-%d')}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>""")

        # Add dealerships
        dealerships = db.query(Dealership).filter(Dealership.is_active == True).all()
        for dealership in dealerships:
            sitemap_content.append(f"""
  <url>
    <loc>{base_url}/dealerships/{dealership.id}</loc>
    <lastmod>{dealership.updated_at.strftime('%Y-%m-%d') if dealership.updated_at else datetime.now(timezone.utc).strftime('%Y-%m-%d')}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>""")

        # Add categories
        categories = db.query(Part.category).filter(Part.category.isnot(None)).distinct().all()
        for (category,) in categories:
            if category:
                sitemap_content.append(f"""
  <url>
    <loc>{base_url}/categories/{category}</loc>
    <lastmod>{datetime.now(timezone.utc).strftime('%Y-%m-%d')}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>""")

        # Add popular search terms
        popular_brands = db.query(Part.brand).filter(Part.brand.isnot(None)).distinct().limit(50).all()
        for (brand,) in popular_brands:
            if brand:
                sitemap_content.append(f"""
  <url>
    <loc>{base_url}/search/{brand}</loc>
    <lastmod>{datetime.now(timezone.utc).strftime('%Y-%m-%d')}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.5</priority>
  </url>""")

        sitemap_content.append('</urlset>')

        sitemap_xml = '\n'.join(sitemap_content)

        # Cache the generated sitemap
        seo_cache.cache_sitemap(sitemap_xml)

        return PlainTextResponse(
            content=sitemap_xml,
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Error generating sitemap: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/robots.txt", response_class=PlainTextResponse)
async def get_robots_txt(request: Request):
    """
    Generate robots.txt file for search engine crawlers.
    """
    base_url = str(request.base_url).rstrip('/')

    robots_content = f"""User-agent: *
Allow: /
Disallow: /api/v1/auth/
Disallow: /api/v1/admin/
Disallow: /admin/
Disallow: /private/

# Sitemap
Sitemap: {base_url}/api/v1/seo/sitemap.xml

# Crawl delay (be nice to our servers)
Crawl-delay: 1

# Popular search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 2

User-agent: Slurp
Allow: /
Crawl-delay: 2
"""

    return PlainTextResponse(content=robots_content)


@router.get("/meta/{part_number}")
async def get_part_meta_tags(
    part_number: str,
    db: Session = Depends(get_db)
):
    """
    Get meta tags for a specific part.
    Used by frontend for dynamic meta tag generation.
    """
    try:
        part = db.query(Part).filter(Part.part_number == part_number).first()
        if not part:
            raise HTTPException(status_code=404, detail="Part not found")

        inventory_count = db.query(Inventory).filter(
            Inventory.part_id == part.id,
            Inventory.is_available == True
        ).count()

        meta_tags = SEOMetaTags.generate_part_meta(part, inventory_count)

        return {"meta_tags": meta_tags}

    except Exception as e:
        logger.error(f"Error getting meta tags for part {part_number}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/structured-data/{part_number}")
async def get_part_structured_data(
    part_number: str,
    db: Session = Depends(get_db)
):
    """
    Get structured data (JSON-LD) for a specific part.
    """
    try:
        part = db.query(Part).filter(Part.part_number == part_number).first()
        if not part:
            raise HTTPException(status_code=404, detail="Part not found")

        inventory_items = db.query(Inventory).filter(
            Inventory.part_id == part.id,
            Inventory.is_available == True
        ).all()

        structured_data = StructuredData.generate_part_structured_data(part, inventory_items)

        return {"structured_data": structured_data}

    except Exception as e:
        logger.error(f"Error getting structured data for part {part_number}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cache/invalidate/{part_number}")
def invalidate_part_cache(part_number: str):
    """
    Invalidate SEO cache for a specific part.
    Useful when part data is updated.
    """
    try:
        success = seo_cache.invalidate_part_cache(part_number)

        return {
            "success": success,
            "message": f"Cache invalidated for part {part_number}" if success else "Failed to invalidate cache"
        }

    except Exception as e:
        logger.error(f"Error invalidating cache for part {part_number}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cache/invalidate-all")
def invalidate_all_seo_cache():
    """
    Invalidate all SEO cache.
    Use with caution - will clear all cached SEO data.
    """
    try:
        success = seo_cache.invalidate_all_seo_cache()

        return {
            "success": success,
            "message": "All SEO cache invalidated" if success else "Failed to invalidate cache"
        }

    except Exception as e:
        logger.error(f"Error invalidating all SEO cache: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cache/warm-popular")
def warm_popular_parts_cache(
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Pre-warm cache for popular parts.
    Generates SEO pages for the most popular parts.
    """
    try:
        warmed_count = seo_cache.warm_popular_parts_cache(db, limit)

        return {
            "success": True,
            "warmed_count": warmed_count,
            "message": f"Warmed cache for {warmed_count} popular parts"
        }

    except Exception as e:
        logger.error(f"Error warming popular parts cache: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cache/warm-searches")
def warm_popular_searches_cache(
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """
    Pre-warm cache for popular search terms.
    Generates cached pages for popular categories and brands.
    """
    try:
        warmed_count = seo_cache.warm_popular_searches_cache(db, limit)

        return {
            "success": True,
            "warmed_count": warmed_count,
            "message": f"Warmed search cache for {warmed_count} popular terms"
        }

    except Exception as e:
        logger.error(f"Error warming popular searches cache: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cache/invalidate-category/{category}")
def invalidate_category_cache(category: str):
    """
    Invalidate SEO cache for a specific category.
    Useful when category data is updated.
    """
    try:
        success = seo_cache.invalidate_category_cache(category)

        return {
            "success": success,
            "message": f"Cache invalidated for category {category}" if success else "Failed to invalidate cache"
        }

    except Exception as e:
        logger.error(f"Error invalidating cache for category {category}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/popular-parts")
def get_popular_parts_for_seo(
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    Get popular parts for SEO pre-generation.
    Returns parts with most inventory items.
    """
    try:
        popular_parts = db.query(Part).join(Inventory).group_by(Part.id).order_by(
            db.func.count(Inventory.id).desc()
        ).limit(limit).all()

        parts_data = []
        for part in popular_parts:
            inventory_count = db.query(Inventory).filter(
                Inventory.part_id == part.id,
                Inventory.is_available == True
            ).count()

            parts_data.append({
                "id": str(part.id),
                "part_number": part.part_number,
                "name": part.name,
                "brand": part.brand,
                "category": part.category,
                "inventory_count": inventory_count,
                "seo_url": f"/parts/{part.part_number}"
            })

        return {
            "popular_parts": parts_data,
            "total": len(parts_data)
        }

    except Exception as e:
        logger.error(f"Error getting popular parts: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/popular-searches")
def get_popular_searches_for_seo(
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    Get popular search terms for SEO pre-generation.
    Returns most common categories and brands.
    """
    try:
        # Get popular categories
        popular_categories = db.query(Part.category, db.func.count(Part.category).label('count')).filter(
            Part.category.isnot(None)
        ).group_by(Part.category).order_by(
            db.func.count(Part.category).desc()
        ).limit(limit).all()

        # Get popular brands
        popular_brands = db.query(Part.brand, db.func.count(Part.brand).label('count')).filter(
            Part.brand.isnot(None)
        ).group_by(Part.brand).order_by(
            db.func.count(Part.brand).desc()
        ).limit(limit).all()

        categories_data = [{"term": cat, "count": count, "seo_url": f"/categories/{cat}"} for cat, count in popular_categories]
        brands_data = [{"term": brand, "count": count, "seo_url": f"/search/{brand}"} for brand, count in popular_brands]

        return {
            "popular_categories": categories_data,
            "popular_brands": brands_data,
            "total_categories": len(categories_data),
            "total_brands": len(brands_data)
        }

    except Exception as e:
        logger.error(f"Error getting popular searches: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
