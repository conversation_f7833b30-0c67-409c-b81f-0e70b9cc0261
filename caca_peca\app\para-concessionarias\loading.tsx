import { Skeleton } from "@/components/ui/skeleton"

export default function DealershipGuideLoading() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Hero Section Skeleton */}
      <section className="bg-primary/10 py-20">
        <div className="container mx-auto px-4 text-center">
          <Skeleton className="mx-auto mb-6 h-12 w-3/4 max-w-2xl" />
          <Skeleton className="mx-auto mb-8 h-24 w-2/3 max-w-xl" />
          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Skeleton className="h-12 w-full sm:w-40" />
            <Skeleton className="h-12 w-full sm:w-40" />
          </div>
        </div>
      </section>

      {/* Benefits Section Skeleton */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="mx-auto mb-12 h-10 w-1/2" />
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="rounded-lg border p-6">
                <Skeleton className="mb-4 h-8 w-3/4" />
                <Skeleton className="h-20 w-full" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section Skeleton */}
      <section className="bg-secondary/10 py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="mx-auto mb-12 h-10 w-1/2" />
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="flex flex-col items-center text-center">
                <Skeleton className="mb-4 h-16 w-16 rounded-full" />
                <Skeleton className="mb-2 h-6 w-40" />
                <Skeleton className="h-16 w-full" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* File Format Section Skeleton */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="mx-auto mb-8 h-10 w-1/2" />
          <Skeleton className="mx-auto mb-12 h-16 w-2/3" />
          <div className="mx-auto max-w-4xl">
            <Skeleton className="mb-6 h-12 w-full" />
            <Skeleton className="h-64 w-full rounded-md" />
          </div>
        </div>
      </section>

      {/* Plans Section Skeleton */}
      <section className="bg-secondary/10 py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="mx-auto mb-8 h-10 w-1/2" />
          <Skeleton className="mx-auto mb-12 h-16 w-2/3" />
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="rounded-lg border p-6">
                <Skeleton className="mb-2 h-8 w-1/2" />
                <Skeleton className="mb-4 h-4 w-3/4" />
                <Skeleton className="mb-6 h-10 w-1/3" />
                <div className="space-y-2">
                  {[...Array(4)].map((_, j) => (
                    <Skeleton key={j} className="h-6 w-full" />
                  ))}
                </div>
                <Skeleton className="mt-6 h-10 w-full" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section Skeleton */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="mx-auto mb-12 h-10 w-1/2" />
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="rounded-lg border p-6">
                <div className="mb-4 flex items-center">
                  <Skeleton className="mr-4 h-12 w-12 rounded-full" />
                  <div>
                    <Skeleton className="mb-1 h-5 w-32" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                </div>
                <Skeleton className="h-24 w-full" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section Skeleton */}
      <section className="bg-secondary/10 py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="mx-auto mb-12 h-10 w-1/2" />
          <div className="mx-auto max-w-3xl space-y-6">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="rounded-lg border p-6">
                <Skeleton className="mb-4 h-6 w-3/4" />
                <Skeleton className="h-16 w-full" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section Skeleton */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <Skeleton className="mx-auto mb-6 h-10 w-2/3 max-w-xl" />
          <Skeleton className="mx-auto mb-8 h-16 w-1/2 max-w-lg" />
          <Skeleton className="mx-auto h-12 w-64" />
        </div>
      </section>
    </div>
  )
}

