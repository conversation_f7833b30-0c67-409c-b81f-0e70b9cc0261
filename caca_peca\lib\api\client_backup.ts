/**
 * AutoParts API Client
 * Comprehensive TypeScript client for the AutoParts backend API
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import { 
  ApiResponse, 
  ErrorResponse,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  User,
  UserCreate
} from '../types/api';

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';

// Token storage keys
const ACCESS_TOKEN_KEY = 'autoparts_access_token';
const REFRESH_TOKEN_KEY = 'autoparts_refresh_token';

class AutoPartsAPIClient {
  private client: AxiosInstance;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Initialize tokens from cookies
    this.loadTokensFromStorage();

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${this.accessToken}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            this.clearTokens();
            // Redirect to login or emit auth error event
            if (typeof window !== 'undefined') {
              window.location.href = '/auth';
            }
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Token management
  private loadTokensFromStorage(): void {
    if (typeof window !== 'undefined') {
      this.accessToken = Cookies.get(ACCESS_TOKEN_KEY) || null;
      this.refreshToken = Cookies.get(REFRESH_TOKEN_KEY) || null;
    }
  }

  private saveTokensToStorage(accessToken: string, refreshToken: string): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    
    if (typeof window !== 'undefined') {
      // Set secure cookies with appropriate expiration
      Cookies.set(ACCESS_TOKEN_KEY, accessToken, { 
        expires: 1/24, // 1 hour
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
      Cookies.set(REFRESH_TOKEN_KEY, refreshToken, { 
        expires: 7, // 7 days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    }
  }

  private clearTokens(): void {
    this.accessToken = null;
    this.refreshToken = null;
    
    if (typeof window !== 'undefined') {
      Cookies.remove(ACCESS_TOKEN_KEY);
      Cookies.remove(REFRESH_TOKEN_KEY);
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await this.client.post<LoginResponse>('/auth/login', credentials);
      const loginData = response.data;
      
      this.saveTokensToStorage(loginData.access_token, loginData.refresh_token);
      
      return {
        data: loginData,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async logout(): Promise<ApiResponse<void>> {
    try {
      await this.client.post('/auth/logout');
      this.clearTokens();
      
      return { success: true };
    } catch (error: any) {
      this.clearTokens(); // Clear tokens even if logout fails
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.client.post<RefreshTokenResponse>('/auth/refresh', {
      refresh_token: this.refreshToken
    });

    this.accessToken = response.data.access_token;
    
    if (typeof window !== 'undefined') {
      Cookies.set(ACCESS_TOKEN_KEY, this.accessToken, { 
        expires: 1/24,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    }
  }

  async register(userData: UserCreate): Promise<ApiResponse<User>> {
    try {
      const response = await this.client.post<User>('/auth/register', userData);
      
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await this.client.get<User>('/auth/me');
      
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Generic request method
  async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client(config);
      
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  // Error handling
  private handleError(error: any): ErrorResponse {
    if (error.response) {
      // Server responded with error status
      return {
        error: error.response.data?.error || 'API Error',
        message: error.response.data?.message || error.message,
        details: error.response.data?.details,
        timestamp: Date.now()
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        error: 'Network Error',
        message: 'Unable to connect to the server. Please check your internet connection.',
        timestamp: Date.now()
      };
    } else {
      // Something else happened
      return {
        error: 'Unknown Error',
        message: error.message || 'An unexpected error occurred',
        timestamp: Date.now()
      };
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    try {
      const response = await axios.get(`${API_BASE_URL}/health`);
      
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }
}

// Export singleton instance
export const apiClient = new AutoPartsAPIClient();
export default apiClient;
