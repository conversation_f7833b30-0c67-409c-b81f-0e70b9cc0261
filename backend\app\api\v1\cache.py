"""
Cache management endpoints for the AutoParts API.
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.cache import cache_manager
from app.core.cache_decorators import CacheService
from app.api.deps import get_current_admin_user
from app.models.user import User

router = APIRouter()


@router.get("/status")
async def get_cache_status(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get cache status and statistics (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Cache status and statistics
    """
    return CacheService.get_cache_statistics()


@router.get("/info")
async def get_cache_info(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get detailed cache information (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Detailed cache information
    """
    cache_info = cache_manager.get_cache_info()
    
    # Add additional information
    cache_info.update({
        "redis_url": "***HIDDEN***",  # Don't expose Redis URL
        "cache_available": cache_manager.is_available(),
        "cache_patterns_count": {
            "parts_search": len(cache_manager.redis_client.keys("parts:search:*")) if cache_manager.is_available() else 0,
            "part_details": len(cache_manager.redis_client.keys("part:details:*")) if cache_manager.is_available() else 0,
            "inventory_stats": len(cache_manager.redis_client.keys("inventory:stats:*")) if cache_manager.is_available() else 0,
            "dealership_info": len(cache_manager.redis_client.keys("dealership:info:*")) if cache_manager.is_available() else 0,
            "search_suggestions": len(cache_manager.redis_client.keys("search:suggestions:*")) if cache_manager.is_available() else 0,
        }
    })
    
    return cache_info


@router.post("/invalidate/parts")
async def invalidate_parts_cache(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Invalidate all parts-related cache (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Invalidation result
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    deleted_count = CacheService.invalidate_parts_cache()
    
    return {
        "success": True,
        "message": f"Invalidated {deleted_count} parts cache entries",
        "deleted_count": deleted_count
    }


@router.post("/invalidate/inventory")
async def invalidate_inventory_cache(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Invalidate all inventory-related cache (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Invalidation result
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    deleted_count = CacheService.invalidate_inventory_cache()
    
    return {
        "success": True,
        "message": f"Invalidated {deleted_count} inventory cache entries",
        "deleted_count": deleted_count
    }


@router.post("/invalidate/dealerships")
async def invalidate_dealership_cache(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Invalidate all dealership-related cache (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Invalidation result
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    deleted_count = CacheService.invalidate_dealership_cache()
    
    return {
        "success": True,
        "message": f"Invalidated {deleted_count} dealership cache entries",
        "deleted_count": deleted_count
    }


@router.post("/invalidate/all")
async def invalidate_all_cache(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Invalidate all cache (admin only - use with caution).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Invalidation result
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    success = cache_manager.flush_all()
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to flush cache"
        )
    
    return {
        "success": True,
        "message": "All cache data has been invalidated",
        "warning": "This operation cleared all cached data"
    }


@router.post("/warm-up/popular-searches")
async def warm_up_popular_searches(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Warm up cache with popular search terms (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Warm-up result
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    warmed_count = CacheService.warm_up_popular_searches()
    
    return {
        "success": True,
        "message": f"Warmed up cache for {warmed_count} popular search terms",
        "warmed_count": warmed_count
    }


@router.get("/keys/{pattern}")
async def get_cache_keys(
    pattern: str,
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get cache keys matching a pattern (admin only).
    
    Args:
        pattern: Pattern to match (e.g., "parts:*")
        current_user: Current admin user
        
    Returns:
        Matching cache keys
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    try:
        keys = cache_manager.redis_client.keys(pattern)
        
        # Get TTL for each key
        keys_with_ttl = []
        for key in keys[:50]:  # Limit to first 50 keys
            ttl = cache_manager.get_ttl(key)
            keys_with_ttl.append({
                "key": key,
                "ttl": ttl,
                "expires_in": f"{ttl}s" if ttl > 0 else "no expiry" if ttl == -1 else "expired"
            })
        
        return {
            "pattern": pattern,
            "total_keys": len(keys),
            "keys_shown": len(keys_with_ttl),
            "keys": keys_with_ttl
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cache keys: {str(e)}"
        )


@router.delete("/key/{key}")
async def delete_cache_key(
    key: str,
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Delete a specific cache key (admin only).
    
    Args:
        key: Cache key to delete
        current_user: Current admin user
        
    Returns:
        Deletion result
    """
    if not cache_manager.is_available():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service is not available"
        )
    
    success = cache_manager.delete(key)
    
    return {
        "success": success,
        "message": f"Cache key '{key}' {'deleted' if success else 'not found or failed to delete'}",
        "key": key
    }


@router.get("/health")
async def cache_health_check() -> Dict[str, Any]:
    """
    Check cache health status.
    
    Returns:
        Cache health status
    """
    is_available = cache_manager.is_available()
    
    health_status = {
        "cache_available": is_available,
        "status": "healthy" if is_available else "unhealthy",
        "timestamp": cache_manager.redis_client.time()[0] if is_available else None
    }
    
    if not is_available:
        health_status["error"] = "Redis connection is not available"
    
    return health_status
