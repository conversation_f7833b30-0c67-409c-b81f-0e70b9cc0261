/**
 * Login Page
 * Dedicated login page with enhanced form
 */

import React from 'react';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { LoginForm } from '@/components/auth/login-form';

export default function LoginPage() {
  return (
    <div className="container flex h-screen flex-col items-center justify-center">
      <Link href="/" className="mb-8 flex items-center space-x-2">
        <ShoppingCart className="h-6 w-6 text-primary" />
        <span className="text-xl font-bold">AutoParts</span>
      </Link>

      <LoginForm />

      <div className="mt-6 text-center text-sm text-muted-foreground">
        <p>
          Não tem uma conta?{' '}
          <Link href="/auth/register" className="text-primary hover:underline">
            Cadastre-se
          </Link>
        </p>
      </div>
    </div>
  );
}