-- Initial database setup for AutoParts
-- This script creates the database and basic configuration

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for full-text search (will be created after tables via Alembic)
-- These are commented out as they will be created by migrations
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_parts_search 
-- ON parts USING gin(to_tsvector('portuguese', name || ' ' || COALESCE(description, '')));

-- Create custom functions for search optimization
CREATE OR REPLACE FUNCTION normalize_part_code(code TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Remove common separators and normalize part codes
    RETURN UPPER(REGEXP_REPLACE(code, '[^A-Z0-9]', '', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- <PERSON>reate function to calculate inventory value
CREATE OR REPLACE FUNCTION calculate_inventory_value(quantity INTEGER, price DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
    RETURN quantity * price;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE autoparts TO autoparts;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO autoparts;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO autoparts;
