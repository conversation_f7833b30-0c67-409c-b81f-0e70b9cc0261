# Design Document

## Overview

A integração frontend-backend conectará a aplicação Next.js existente ao backend Python FastAPI já implementado. O design foca em criar uma arquitetura robusta de comunicação, implementar autenticação JWT, substituir dados mockados por APIs reais, e criar interfaces intuitivas para upload de planilhas e gestão de estoque.

### Arquitetura de Integração
- **Frontend**: Next.js 15 com App Router e TypeScript
- **Backend**: FastAPI Python já implementado
- **Comunicação**: REST APIs com JSON
- **Autenticação**: JWT com refresh tokens
- **Estado**: Zustand para gerenciamento de estado global
- **Cache**: React Query para cache de dados da API

## Architecture

### Arquitetura de Comunicação

```
┌─────────────────────────────────────┐
│         Next.js Frontend            │
│  ┌─────────────┐ ┌─────────────┐   │
│  │   Pages     │ │ Components  │   │
│  └─────────────┘ └─────────────┘   │
│  ┌─────────────┐ ┌─────────────┐   │
│  │ API Client  │ │   Stores    │   │
│  └─────────────┘ └─────────────┘   │
└─────────────┬───────────────────────┘
              │ HTTP/REST + JWT
┌─────────────▼───────────────────────┐
│         FastAPI Backend             │
│  ┌─────────────┐ ┌─────────────┐   │
│  │ Auth Routes │ │ API Routes  │   │
│  └─────────────┘ └─────────────┘   │
│  ┌─────────────┐ ┌─────────────┐   │
│  │  Services   │ │ Database    │   │
│  └─────────────┘ └─────────────┘   │
└─────────────────────────────────────┘
```

### Fluxo de Dados

```
User Action → Component → Store → API Client → Backend API → Database
     ↓                                                           ↓
UI Update ← Component ← Store ← Response ← API Client ← Backend Response
```

## Components and Interfaces

### 1. API Client Layer

#### HTTP Client Configuration
```typescript
// lib/api-client.ts
class APIClient {
  private baseURL: string
  private accessToken: string | null
  private refreshToken: string | null
  
  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL
    this.setupInterceptors()
  }
  
  // Automatic token refresh
  private setupInterceptors()
  
  // Request methods
  async get<T>(endpoint: string): Promise<T>
  async post<T>(endpoint: string, data: any): Promise<T>
  async put<T>(endpoint: string, data: any): Promise<T>
  async delete<T>(endpoint: string): Promise<T>
  
  // File upload
  async uploadFile(endpoint: string, file: File): Promise<any>
}
```

#### API Endpoints Mapping
```typescript
// lib/api-endpoints.ts
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    REGISTER: '/auth/register'
  },
  
  // Parts & Inventory
  PARTS: {
    SEARCH: '/parts/search',
    DETAILS: (id: string) => `/parts/${id}`,
    CATALOG: '/parts/catalog'
  },
  
  // Dealerships
  DEALERSHIPS: {
    PROFILE: '/dealerships/profile',
    INVENTORY: '/dealerships/inventory',
    UPLOAD: '/dealerships/upload',
    DASHBOARD: '/dealerships/dashboard'
  },
  
  // Admin
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    DEALERSHIPS: '/admin/dealerships',
    REPORTS: '/admin/reports'
  }
}
```

### 2. State Management

#### Authentication Store
```typescript
// stores/auth-store.ts
interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateProfile: (data: ProfileData) => Promise<void>
}
```

#### Parts Store
```typescript
// stores/parts-store.ts
interface PartsState {
  searchResults: Part[]
  selectedPart: Part | null
  searchQuery: string
  filters: SearchFilters
  isLoading: boolean
  
  // Actions
  searchParts: (query: string) => Promise<void>
  setFilters: (filters: SearchFilters) => void
  selectPart: (part: Part) => void
  clearSearch: () => void
}
```

#### Inventory Store
```typescript
// stores/inventory-store.ts
interface InventoryState {
  inventory: InventoryItem[]
  uploadProgress: number
  uploadStatus: 'idle' | 'uploading' | 'processing' | 'completed' | 'error'
  
  // Actions
  uploadFile: (file: File) => Promise<void>
  getInventory: () => Promise<void>
  updateItem: (id: string, data: Partial<InventoryItem>) => Promise<void>
}
```

### 3. Component Architecture

#### Page Components
```typescript
// app/dashboard/page.tsx - Dealership Dashboard
// app/busca/page.tsx - Parts Search
// app/admin/page.tsx - Admin Panel
// app/auth/login/page.tsx - Login Page
```

#### Feature Components
```typescript
// components/parts/
├── PartsSearch.tsx          # Search interface
├── PartsResults.tsx         # Results display
├── PartCard.tsx            # Individual part card
└── PartsFilters.tsx        # Search filters

// components/inventory/
├── InventoryUpload.tsx     # File upload interface
├── InventoryTable.tsx      # Inventory listing
├── UploadProgress.tsx      # Upload progress indicator
└── InventoryStats.tsx      # Statistics dashboard

// components/auth/
├── LoginForm.tsx           # Login interface
├── RegisterForm.tsx        # Registration
└── ProtectedRoute.tsx      # Route protection
```

### 4. Authentication Flow

#### JWT Implementation
```typescript
// lib/auth.ts
class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/login', credentials)
    this.setTokens(response.access_token, response.refresh_token)
    return response
  }
  
  async refreshToken(): Promise<void> {
    const refreshToken = this.getRefreshToken()
    const response = await apiClient.post('/auth/refresh', { refresh_token: refreshToken })
    this.setTokens(response.access_token, response.refresh_token)
  }
  
  private setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem('access_token', accessToken)
    localStorage.setItem('refresh_token', refreshToken)
  }
}
```

#### Route Protection
```typescript
// components/auth/ProtectedRoute.tsx
export function ProtectedRoute({ 
  children, 
  requiredRole 
}: {
  children: React.ReactNode
  requiredRole?: 'admin' | 'dealership' | 'customer'
}) {
  const { isAuthenticated, user } = useAuthStore()
  
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" />
  }
  
  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/unauthorized" />
  }
  
  return <>{children}</>
}
```

## Data Models

### Frontend Types
```typescript
// types/api.ts
export interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'dealership' | 'customer'
  dealership_id?: string
}

export interface Part {
  id: string
  code: string
  name: string
  description: string
  category: string
  brand: string
  model: string
  year: number
}

export interface InventoryItem {
  id: string
  part_id: string
  part: Part
  quantity: number
  price: number
  dealership_id: string
  dealership: Dealership
  last_updated: string
}

export interface Dealership {
  id: string
  name: string
  cnpj: string
  email: string
  phone: string
  address: Address
  subscription_status: 'active' | 'expired' | 'cancelled'
}

export interface SearchFilters {
  category?: string
  brand?: string
  model?: string
  year_min?: number
  year_max?: number
  price_min?: number
  price_max?: number
  location?: string
  radius?: number
}
```

## Error Handling

### Error Types
```typescript
// types/errors.ts
export interface APIError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
}

export class APIException extends Error {
  constructor(
    public code: string,
    public message: string,
    public details?: Record<string, any>
  ) {
    super(message)
  }
}
```

### Error Handling Strategy
```typescript
// lib/error-handler.ts
export function handleAPIError(error: APIError): void {
  switch (error.code) {
    case 'UNAUTHORIZED':
      // Redirect to login
      window.location.href = '/auth/login'
      break
    case 'SUBSCRIPTION_EXPIRED':
      // Show subscription renewal modal
      showSubscriptionModal()
      break
    case 'INVALID_FILE_FORMAT':
      // Show file format error
      toast.error('Formato de arquivo inválido. Use Excel ou CSV.')
      break
    default:
      // Generic error message
      toast.error(error.message || 'Erro interno do sistema')
  }
}
```

## Testing Strategy

### Component Testing
```typescript
// __tests__/components/PartsSearch.test.tsx
describe('PartsSearch', () => {
  it('should search parts when query is entered', async () => {
    render(<PartsSearch />)
    
    const searchInput = screen.getByPlaceholderText('Digite o código da peça')
    fireEvent.change(searchInput, { target: { value: '46751179' } })
    
    await waitFor(() => {
      expect(screen.getByText('Filtro de óleo')).toBeInTheDocument()
    })
  })
})
```

### API Integration Testing
```typescript
// __tests__/api/parts.test.ts
describe('Parts API', () => {
  it('should fetch parts from backend', async () => {
    const mockParts = [{ id: '1', code: '46751179', name: 'Filtro de óleo' }]
    
    nock(process.env.NEXT_PUBLIC_API_URL)
      .get('/parts/search?q=46751179')
      .reply(200, { results: mockParts })
    
    const result = await apiClient.searchParts('46751179')
    expect(result.results).toEqual(mockParts)
  })
})
```

### E2E Testing
```typescript
// e2e/dealership-flow.spec.ts
test('dealership can upload inventory file', async ({ page }) => {
  await page.goto('/auth/login')
  await page.fill('[data-testid=email]', '<EMAIL>')
  await page.fill('[data-testid=password]', 'password')
  await page.click('[data-testid=login-button]')
  
  await page.goto('/dashboard')
  await page.setInputFiles('[data-testid=file-upload]', 'test-inventory.xlsx')
  await page.click('[data-testid=upload-button]')
  
  await expect(page.locator('[data-testid=upload-success]')).toBeVisible()
})
```

## Performance Considerations

### Data Fetching Optimization
```typescript
// hooks/use-parts-query.ts
export function usePartsQuery(query: string) {
  return useQuery({
    queryKey: ['parts', query],
    queryFn: () => apiClient.searchParts(query),
    enabled: query.length > 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}
```

### File Upload Optimization
```typescript
// components/inventory/InventoryUpload.tsx
const uploadFile = async (file: File) => {
  const chunkSize = 1024 * 1024 // 1MB chunks
  const totalChunks = Math.ceil(file.size / chunkSize)
  
  for (let i = 0; i < totalChunks; i++) {
    const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize)
    await apiClient.uploadChunk(chunk, i, totalChunks)
    setProgress((i + 1) / totalChunks * 100)
  }
}
```

### SEO Integration
```typescript
// app/parts/[code]/page.tsx
export async function generateMetadata({ params }: { params: { code: string } }) {
  const part = await apiClient.getPartByCode(params.code)
  
  return {
    title: `${part.name} - ${part.code} | AutoParts`,
    description: `${part.description} disponível em concessionárias. Encontre preços e disponibilidade.`,
    openGraph: {
      title: `${part.name} - ${part.code}`,
      description: part.description,
      url: `https://autoparts.com/parts/${part.code}`,
    }
  }
}
```