"""
Basic tests to verify test setup is working.
"""
import pytest
from sqlalchemy.orm import Session

from app.models.user import User
from app.services.auth import AuthService


@pytest.mark.unit
def test_database_connection(db_session: Session):
    """Test that database connection is working."""
    from sqlalchemy import text
    # Simple query to test connection
    result = db_session.execute(text("SELECT 1")).scalar()
    assert result == 1


@pytest.mark.unit
def test_user_creation_basic(db_session: Session):
    """Test basic user creation without complex dependencies."""
    # Create user directly with minimal required fields
    user = User(
        email="<EMAIL>",
        full_name="Test User",
        password_hash="hashed_password_here"
    )

    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)

    assert user.id is not None
    assert user.email == "<EMAIL>"
    assert user.full_name == "Test User"
    assert user.password_hash == "hashed_password_here"


@pytest.mark.unit
def test_auth_service_initialization(db_session: Session):
    """Test that AuthService can be initialized."""
    auth_service = AuthService(db_session)
    assert auth_service.db == db_session


@pytest.mark.unit
def test_sample_fixtures(sample_user_data: dict, sample_part_data: dict):
    """Test that sample fixtures are working."""
    assert sample_user_data["email"] == "<EMAIL>"
    assert sample_part_data["code"] == "TEST001"


@pytest.mark.unit
def test_environment_variables():
    """Test that test environment variables are set."""
    import os
    assert os.environ.get("TESTING") == "1"
    assert "sqlite" in os.environ.get("DATABASE_URL", "")


def test_simple_math():
    """Simple test to verify pytest is working."""
    assert 1 + 1 == 2
    assert "hello" + " world" == "hello world"


def test_imports():
    """Test that all main modules can be imported."""
    from app.main import app
    from app.core.database import get_db
    from app.core.config import settings
    from app.models.user import User
    from app.models.part import Part
    from app.models.dealership import Dealership
    from app.models.inventory import Inventory
    
    assert app is not None
    assert get_db is not None
    assert settings is not None
    assert User is not None
    assert Part is not None
    assert Dealership is not None
    assert Inventory is not None
