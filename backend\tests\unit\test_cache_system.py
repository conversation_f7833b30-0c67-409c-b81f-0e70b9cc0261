"""
Unit tests for cache system.
"""
import pytest
import time
from unittest.mock import Mock, patch, MagicMock

from app.core.cache import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>acheTTL, CacheKeys
from app.core.cache_decorators import cached, cache_invalidate_pattern, CacheService


@pytest.mark.unit
@pytest.mark.cache
class TestCacheManager:
    """Test cases for CacheManager."""
    
    def test_cache_manager_initialization(self):
        """Test cache manager initialization."""
        with patch('redis.from_url') as mock_redis:
            mock_redis_client = Mock()
            mock_redis_client.ping.return_value = True
            mock_redis.return_value = mock_redis_client
            
            cache_manager = CacheManager()
            
            assert cache_manager.redis_client == mock_redis_client
            mock_redis_client.ping.assert_called_once()
    
    def test_cache_manager_connection_failure(self):
        """Test cache manager with connection failure."""
        with patch('redis.from_url') as mock_redis:
            mock_redis.side_effect = Exception("Connection failed")
            
            cache_manager = CacheManager()
            
            assert cache_manager.redis_client is None
    
    def test_is_available_true(self):
        """Test is_available when Redis is available."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        
        assert cache_manager.is_available() is True
    
    def test_is_available_false_no_client(self):
        """Test is_available when no Redis client."""
        cache_manager = CacheManager()
        cache_manager.redis_client = None
        
        assert cache_manager.is_available() is False
    
    def test_is_available_false_ping_fails(self):
        """Test is_available when ping fails."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.side_effect = Exception("Ping failed")
        
        assert cache_manager.is_available() is False
    
    def test_generate_cache_key_simple(self):
        """Test cache key generation with simple parameters."""
        cache_manager = CacheManager()
        
        key = cache_manager.generate_cache_key("test", param1="value1", param2="value2")
        
        assert key == "test:param1=value1&param2=value2"
    
    def test_generate_cache_key_long_params(self):
        """Test cache key generation with long parameters."""
        cache_manager = CacheManager()
        
        long_value = "x" * 200
        key = cache_manager.generate_cache_key("test", long_param=long_value)
        
        # Should use hash for long parameters
        assert key.startswith("test:")
        assert len(key) < 100  # Should be shorter due to hashing
    
    def test_generate_cache_key_none_values(self):
        """Test cache key generation with None values."""
        cache_manager = CacheManager()
        
        key = cache_manager.generate_cache_key("test", param1="value1", param2=None)
        
        assert key == "test:param1=value1"  # None values should be excluded
    
    def test_set_success(self):
        """Test successful cache set operation."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.setex.return_value = True
        
        result = cache_manager.set("test_key", "test_value", ttl=300)
        
        assert result is True
        cache_manager.redis_client.setex.assert_called_once_with("test_key", 300, '"test_value"')
    
    def test_set_without_ttl(self):
        """Test cache set operation without TTL."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.set.return_value = True
        
        result = cache_manager.set("test_key", "test_value")
        
        assert result is True
        cache_manager.redis_client.set.assert_called_once_with("test_key", '"test_value"')
    
    def test_set_unavailable(self):
        """Test cache set when Redis is unavailable."""
        cache_manager = CacheManager()
        cache_manager.redis_client = None
        
        result = cache_manager.set("test_key", "test_value")
        
        assert result is False
    
    def test_get_success(self):
        """Test successful cache get operation."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.get.return_value = '"test_value"'
        
        result = cache_manager.get("test_key")
        
        assert result == "test_value"
        cache_manager.redis_client.get.assert_called_once_with("test_key")
    
    def test_get_not_found(self):
        """Test cache get when key not found."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.get.return_value = None
        
        result = cache_manager.get("test_key")
        
        assert result is None
    
    def test_get_unavailable(self):
        """Test cache get when Redis is unavailable."""
        cache_manager = CacheManager()
        cache_manager.redis_client = None
        
        result = cache_manager.get("test_key")
        
        assert result is None
    
    def test_delete_success(self):
        """Test successful cache delete operation."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.delete.return_value = 1
        
        result = cache_manager.delete("test_key")
        
        assert result is True
        cache_manager.redis_client.delete.assert_called_once_with("test_key")
    
    def test_delete_pattern_success(self):
        """Test successful cache delete pattern operation."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.keys.return_value = ["key1", "key2", "key3"]
        cache_manager.redis_client.delete.return_value = 3
        
        result = cache_manager.delete_pattern("test:*")
        
        assert result == 3
        cache_manager.redis_client.keys.assert_called_once_with("test:*")
        cache_manager.redis_client.delete.assert_called_once_with("key1", "key2", "key3")
    
    def test_delete_pattern_no_keys(self):
        """Test cache delete pattern with no matching keys."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.keys.return_value = []
        
        result = cache_manager.delete_pattern("test:*")
        
        assert result == 0
        cache_manager.redis_client.delete.assert_not_called()
    
    def test_exists_true(self):
        """Test cache exists when key exists."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.exists.return_value = 1
        
        result = cache_manager.exists("test_key")
        
        assert result is True
    
    def test_exists_false(self):
        """Test cache exists when key doesn't exist."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.exists.return_value = 0
        
        result = cache_manager.exists("test_key")
        
        assert result is False
    
    def test_get_ttl(self):
        """Test getting TTL for a key."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.ttl.return_value = 300
        
        result = cache_manager.get_ttl("test_key")
        
        assert result == 300
    
    def test_increment(self):
        """Test incrementing a numeric value."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.incrby.return_value = 5
        
        result = cache_manager.increment("test_key", 2)
        
        assert result == 5
        cache_manager.redis_client.incrby.assert_called_once_with("test_key", 2)
    
    def test_get_cache_info(self):
        """Test getting cache information."""
        cache_manager = CacheManager()
        cache_manager.redis_client = Mock()
        cache_manager.redis_client.ping.return_value = True
        cache_manager.redis_client.info.return_value = {
            "connected_clients": 5,
            "used_memory_human": "1.5M",
            "keyspace_hits": 100,
            "keyspace_misses": 20
        }
        
        result = cache_manager.get_cache_info()
        
        assert result["status"] == "available"
        assert result["connected_clients"] == 5
        assert result["used_memory"] == "1.5M"
        assert result["hit_rate"] == 83.33  # 100/(100+20)*100


@pytest.mark.unit
@pytest.mark.cache
class TestCacheDecorators:
    """Test cases for cache decorators."""
    
    def test_cached_decorator_cache_miss(self):
        """Test cached decorator with cache miss."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.get.return_value = None
            mock_cache.set.return_value = True
            
            @cached(key_prefix="test", ttl=300)
            def test_function(param1, param2):
                return f"result_{param1}_{param2}"
            
            result = test_function("a", "b")
            
            assert result == "result_a_b"
            mock_cache.get.assert_called_once()
            mock_cache.set.assert_called_once()
    
    def test_cached_decorator_cache_hit(self):
        """Test cached decorator with cache hit."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.get.return_value = "cached_result"
            
            @cached(key_prefix="test", ttl=300)
            def test_function(param1, param2):
                return f"result_{param1}_{param2}"
            
            result = test_function("a", "b")
            
            assert result == "cached_result"
            mock_cache.get.assert_called_once()
            mock_cache.set.assert_not_called()
    
    def test_cache_invalidate_pattern_decorator(self):
        """Test cache invalidate pattern decorator."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.delete_pattern.return_value = 5
            
            @cache_invalidate_pattern("test:*")
            def test_function():
                return "result"
            
            result = test_function()
            
            assert result == "result"
            mock_cache.delete_pattern.assert_called_once_with("test:*")
    
    def test_cache_service_invalidate_parts_cache(self):
        """Test CacheService invalidate parts cache."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.delete_pattern.side_effect = [3, 2, 1]  # Return values for each pattern
            
            result = CacheService.invalidate_parts_cache()
            
            assert result == 6  # 3 + 2 + 1
            assert mock_cache.delete_pattern.call_count == 3
    
    def test_cache_service_invalidate_inventory_cache(self):
        """Test CacheService invalidate inventory cache."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.delete_pattern.side_effect = [2, 3, 1]
            
            result = CacheService.invalidate_inventory_cache()
            
            assert result == 6
            assert mock_cache.delete_pattern.call_count == 3
    
    def test_cache_service_invalidate_dealership_cache(self):
        """Test CacheService invalidate dealership cache."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.delete_pattern.side_effect = [1, 2]
            
            result = CacheService.invalidate_dealership_cache()
            
            assert result == 3
            assert mock_cache.delete_pattern.call_count == 2
    
    def test_cache_service_invalidate_dealership_cache_specific(self):
        """Test CacheService invalidate specific dealership cache."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.delete_pattern.side_effect = [1, 1]
            
            result = CacheService.invalidate_dealership_cache("dealership-123")
            
            assert result == 2
            assert mock_cache.delete_pattern.call_count == 2
            # Verify specific dealership ID was used in patterns
            calls = mock_cache.delete_pattern.call_args_list
            assert "dealership-123" in calls[0][0][0]
            assert "dealership-123" in calls[1][0][0]
    
    def test_cache_service_warm_up_popular_searches(self):
        """Test CacheService warm up popular searches."""
        result = CacheService.warm_up_popular_searches()
        
        assert result == 8  # Number of popular terms defined
    
    def test_cache_service_get_cache_statistics(self):
        """Test CacheService get cache statistics."""
        with patch('app.core.cache_decorators.cache_manager') as mock_cache:
            mock_cache.get_cache_info.return_value = {
                "status": "available",
                "connected_clients": 5
            }
            mock_cache.is_available.return_value = True
            mock_cache.redis_client.keys.side_effect = [
                ["parts:search:1", "parts:search:2"],  # parts_search
                ["part:details:1"],  # part_details
                ["inventory:stats:1", "inventory:stats:2", "inventory:stats:3"],  # inventory_stats
                ["dealership:info:1"]  # dealership_info
            ]
            
            result = CacheService.get_cache_statistics()
            
            assert result["status"] == "available"
            assert result["connected_clients"] == 5
            assert result["cache_patterns"]["parts_search"] == 2
            assert result["cache_patterns"]["part_details"] == 1
            assert result["cache_patterns"]["inventory_stats"] == 3
            assert result["cache_patterns"]["dealership_info"] == 1


@pytest.mark.unit
@pytest.mark.cache
class TestCacheConstants:
    """Test cases for cache constants."""
    
    def test_cache_ttl_constants(self):
        """Test cache TTL constants are properly defined."""
        assert CacheTTL.PARTS_SEARCH == 15 * 60
        assert CacheTTL.PART_DETAILS == 30 * 60
        assert CacheTTL.DEALERSHIP_INFO == 60 * 60
        assert CacheTTL.INVENTORY_STATS == 10 * 60
        assert CacheTTL.USER_SESSION == 30 * 60
        assert CacheTTL.SEARCH_SUGGESTIONS == 60 * 60
        assert CacheTTL.POPULAR_SEARCHES == 24 * 60 * 60
    
    def test_cache_keys_constants(self):
        """Test cache key constants are properly defined."""
        assert CacheKeys.PARTS_SEARCH == "parts:search"
        assert CacheKeys.PART_DETAILS == "part:details"
        assert CacheKeys.DEALERSHIP_INFO == "dealership:info"
        assert CacheKeys.INVENTORY_STATS == "inventory:stats"
        assert CacheKeys.USER_SESSION == "user:session"
        assert CacheKeys.SEARCH_SUGGESTIONS == "search:suggestions"
        assert CacheKeys.POPULAR_SEARCHES == "search:popular"
        assert CacheKeys.INVENTORY_AVAILABILITY == "inventory:availability"
