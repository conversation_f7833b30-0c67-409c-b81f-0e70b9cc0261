# AutoParts Frontend-Backend Integration Implementation Plan

## Overview

This document provides a comprehensive implementation plan for integrating the AutoParts Next.js frontend with the FastAPI backend. The plan addresses all missing integration points and incomplete features identified in the system analysis.

## Project Structure

```
caca_peca/
├── app/                          # Next.js App Router pages
│   ├── admin/                    # Admin panel pages
│   ├── auth/                     # Authentication pages
│   ├── painel/                   # Dealership panel pages
│   └── busca/                    # Search and catalog pages
├── components/                   # React components
│   ├── ui/                       # shadcn/ui base components
│   ├── admin/                    # Admin-specific components
│   ├── dealership/               # Dealership-specific components
│   └── parts/                    # Parts catalog components
├── lib/                          # Utilities and services
│   ├── api/                      # API client services
│   ├── hooks/                    # Custom React hooks
│   ├── types/                    # TypeScript type definitions
│   └── utils/                    # Utility functions
└── backend/                      # FastAPI backend
    ├── app/api/v1/              # API endpoints
    ├── app/services/            # Business logic services
    └── app/models/              # Database models
```

## Implementation Phases

### Phase 1: Critical Features (2-3 weeks)

#### 1.1 Authentication System Enhancement

**Timeline: 3-4 days**

**Missing Components:**
- Password reset flow UI
- Email verification interface
- User profile management
- Session management improvements

**Implementation Steps:**

1. **Password Reset Flow**
   ```typescript
   // File: caca_peca/app/auth/reset-password/page.tsx
   // API: POST /api/v1/auth/password-reset
   // API: POST /api/v1/auth/password-reset/confirm
   ```

2. **User Profile Management**
   ```typescript
   // File: caca_peca/app/painel/perfil/page.tsx
   // API: GET /api/v1/auth/me
   // API: PUT /api/v1/auth/me
   ```

3. **Session Management Enhancement**
   ```typescript
   // File: caca_peca/lib/auth/context.tsx
   // Add token refresh logic and session timeout handling
   ```

**Prerequisites:**
- Backend auth endpoints are functional
- JWT token validation working
- Email service configured (if using email verification)

**Code Example:**
```typescript
// caca_peca/lib/api/auth.ts
export const authApi = {
  async resetPassword(email: string): Promise<ApiResponse<void>> {
    return apiClient.request({
      method: 'POST',
      url: '/auth/password-reset',
      data: { email }
    });
  },
  
  async confirmPasswordReset(token: string, newPassword: string): Promise<ApiResponse<void>> {
    return apiClient.request({
      method: 'POST',
      url: '/auth/password-reset/confirm',
      data: { token, new_password: newPassword }
    });
  }
};
```

#### 1.2 Dealership Inventory Management

**Timeline: 5-7 days**

**Missing Components:**
- Inventory CRUD interface
- Bulk import functionality
- Stock alerts and warnings
- Inventory statistics dashboard

**Implementation Steps:**

1. **Inventory List Component**
   ```typescript
   // File: caca_peca/app/painel/estoque/page.tsx
   // API: GET /api/v1/inventory/search
   ```

2. **Inventory Form Component**
   ```typescript
   // File: caca_peca/components/dealership/inventory-form.tsx
   // API: POST /api/v1/inventory (create)
   // API: PUT /api/v1/inventory/{id} (update)
   ```

3. **Bulk Import Interface**
   ```typescript
   // File: caca_peca/app/painel/importar-estoque/page.tsx
   // API: POST /api/v1/imports/inventory
   ```

**Prerequisites:**
- Backend inventory endpoints enabled
- File upload service configured
- Database migrations completed

**Code Example:**
```typescript
// caca_peca/lib/api/inventory.ts
export const inventoryApi = {
  async searchInventory(params: InventorySearchParams): Promise<ApiResponse<PaginatedResponse<Inventory>>> {
    return apiClient.request({
      method: 'GET',
      url: '/inventory/search',
      params
    });
  },
  
  async createInventoryItem(data: InventoryCreate): Promise<ApiResponse<Inventory>> {
    return apiClient.request({
      method: 'POST',
      url: '/inventory',
      data
    });
  }
};
```

#### 1.3 Parts Catalog Enhancement

**Timeline: 4-5 days**

**Missing Components:**
- Part details page
- Advanced search filters
- Vehicle compatibility display
- Search suggestions

**Implementation Steps:**

1. **Part Details Page**
   ```typescript
   // File: caca_peca/app/produto/[id]/page.tsx
   // API: GET /api/v1/parts/{id}
   ```

2. **Advanced Search Component**
   ```typescript
   // File: caca_peca/components/parts/advanced-search.tsx
   // API: GET /api/v1/parts/search (with all filters)
   ```

3. **Search Suggestions**
   ```typescript
   // File: caca_peca/components/search-bar.tsx (enhancement)
   // API: GET /api/v1/parts/suggestions
   ```

**Prerequisites:**
- Parts API endpoints functional
- Search indexing configured
- Vehicle compatibility data populated

#### 1.4 Subscription Management Interface

**Timeline: 3-4 days**

**Missing Components:**
- Subscription signup flow
- Payment method selection
- Billing history display
- Plan management

**Implementation Steps:**

1. **Subscription Signup**
   ```typescript
   // File: caca_peca/app/painel/assinatura/page.tsx
   // API: POST /api/v1/subscriptions
   ```

2. **Billing History**
   ```typescript
   // File: caca_peca/app/painel/faturas/page.tsx
   // API: GET /api/v1/subscriptions/billing-history
   ```

**Prerequisites:**
- Asaas integration configured
- Subscription endpoints enabled
- Payment webhook handling working

### Phase 2: Important Features (3-4 weeks)

#### 2.1 Admin Analytics Dashboard

**Timeline: 5-6 days**

**Components to Build:**
- System metrics display
- User analytics charts
- Dealership performance metrics
- Revenue tracking

**Implementation Steps:**

1. **Analytics Dashboard**
   ```typescript
   // File: caca_peca/app/admin/analytics/page.tsx
   // API: GET /api/v1/monitoring/metrics/summary
   ```

2. **Charts Components**
   ```typescript
   // File: caca_peca/components/admin/analytics-charts.tsx
   // Using recharts library
   ```

#### 2.2 Advanced Search Implementation

**Timeline: 4-5 days**

**Components to Build:**
- Category browsing interface
- Filter sidebar
- Search result sorting
- Pagination component

#### 2.3 User Profile Management

**Timeline: 3-4 days**

**Components to Build:**
- Profile editing forms
- Password change interface
- Account settings
- Notification preferences

### Phase 3: Enhancement Features (2-3 weeks)

#### 3.1 Real-time Notifications

**Timeline: 4-5 days**

**Components to Build:**
- WebSocket connection management
- Notification center
- Real-time inventory updates
- Live search results

#### 3.2 SEO-Optimized Pages

**Timeline: 3-4 days**

**Components to Build:**
- Category landing pages
- Brand pages
- Product listing pages
- Sitemap generation

### Phase 4: Optimization (1-2 weeks)

#### 4.1 Performance Optimization

**Timeline: 3-4 days**

**Enhancements:**
- Caching strategies
- Image optimization
- Code splitting
- Bundle optimization

#### 4.2 Testing Implementation

**Timeline: 3-4 days**

**Testing Strategy:**
- Unit tests for components
- Integration tests for API calls
- E2E tests for user flows
- Performance testing

## Technical Specifications

### API Client Architecture

```typescript
// caca_peca/lib/api/client.ts
export class ApiClient {
  private baseURL: string;
  private axiosInstance: AxiosInstance;
  
  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    this.axiosInstance = axios.create({
      baseURL: `${this.baseURL}/api/v1`,
      timeout: 10000,
    });
    
    this.setupInterceptors();
  }
  
  private setupInterceptors() {
    // Request interceptor for auth tokens
    // Response interceptor for error handling
    // Token refresh logic
  }
}
```

### State Management Strategy

```typescript
// caca_peca/lib/hooks/useApi.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const useInventorySearch = (params: InventorySearchParams) => {
  return useQuery({
    queryKey: ['inventory', 'search', params],
    queryFn: () => inventoryApi.searchInventory(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
```

### Component Organization

```
components/
├── ui/                    # Base UI components (shadcn/ui)
├── layout/               # Layout components
├── auth/                 # Authentication components
├── admin/                # Admin panel components
├── dealership/           # Dealership panel components
├── parts/                # Parts catalog components
└── common/               # Shared components
```

## Environment Configuration

### Frontend Environment Variables

```env
# caca_peca/.env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_ASAAS_ENV=sandbox
```

### Backend Environment Variables

```env
# backend/.env
DATABASE_URL=postgresql://autoparts:autoparts123@localhost:5432/autoparts
REDIS_URL=redis://localhost:6379/0
ASAAS_API_KEY=your_asaas_api_key
ASAAS_WEBHOOK_SECRET=your_webhook_secret
BACKEND_CORS_ORIGINS=["http://localhost:3000"]
```

## Database Migration Requirements

### Required Migrations

1. **User Profile Extensions**
   ```sql
   ALTER TABLE users ADD COLUMN phone VARCHAR(20);
   ALTER TABLE users ADD COLUMN avatar_url VARCHAR(500);
   ```

2. **Subscription Enhancements**
   ```sql
   ALTER TABLE subscriptions ADD COLUMN trial_end_date DATE;
   ALTER TABLE subscriptions ADD COLUMN discount_percentage DECIMAL(5,2);
   ```

## Testing Strategy

### Unit Testing

```typescript
// __tests__/components/inventory-form.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { InventoryForm } from '@/components/dealership/inventory-form';

describe('InventoryForm', () => {
  it('should submit form with valid data', async () => {
    // Test implementation
  });
});
```

### Integration Testing

```typescript
// __tests__/api/inventory.test.ts
import { inventoryApi } from '@/lib/api/inventory';

describe('Inventory API', () => {
  it('should search inventory with filters', async () => {
    // Test implementation
  });
});
```

### E2E Testing

```typescript
// e2e/dealership-flow.spec.ts
import { test, expect } from '@playwright/test';

test('dealership can manage inventory', async ({ page }) => {
  // Test implementation
});
```

## Troubleshooting Guide

### Common Issues

1. **CORS Errors**
   - Verify BACKEND_CORS_ORIGINS includes frontend URL
   - Check preflight request handling

2. **Authentication Issues**
   - Verify JWT token format and expiration
   - Check token storage and retrieval

3. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check PostgreSQL service status

4. **API Endpoint Not Found**
   - Verify router inclusion in main.py
   - Check endpoint URL formatting

### Performance Issues

1. **Slow API Responses**
   - Check database query optimization
   - Verify Redis caching configuration

2. **Large Bundle Size**
   - Implement code splitting
   - Use dynamic imports for heavy components

## Next Steps

1. Set up development environment
2. Enable all backend router endpoints
3. Complete database migrations
4. Begin Phase 1 implementation
5. Set up testing framework
6. Implement CI/CD pipeline

## Detailed Implementation Guide by Feature Area

### 🔐 Authentication System

#### Password Reset Flow Implementation

**Files to Create/Modify:**
```
caca_peca/app/auth/reset-password/page.tsx
caca_peca/app/auth/reset-password/confirm/page.tsx
caca_peca/lib/api/auth.ts (extend)
caca_peca/components/auth/password-reset-form.tsx
```

**Step-by-Step Implementation:**

1. **Create Password Reset Request Page**
```typescript
// caca_peca/app/auth/reset-password/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { authApi } from '@/lib/api/auth';

export default function PasswordResetPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await authApi.resetPassword(email);
      setMessage('Email de recuperação enviado! Verifique sua caixa de entrada.');
    } catch (error) {
      setMessage('Erro ao enviar email. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto mt-8">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          type="email"
          placeholder="Seu email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? 'Enviando...' : 'Enviar Email de Recuperação'}
        </Button>
        {message && <p className="text-sm text-center">{message}</p>}
      </form>
    </div>
  );
}
```

2. **Extend Auth API Service**
```typescript
// caca_peca/lib/api/auth.ts (add to existing file)
export const authApi = {
  // ... existing methods

  async resetPassword(email: string): Promise<ApiResponse<void>> {
    return apiClient.request({
      method: 'POST',
      url: '/auth/password-reset',
      data: { email }
    });
  },

  async confirmPasswordReset(token: string, newPassword: string): Promise<ApiResponse<void>> {
    return apiClient.request({
      method: 'POST',
      url: '/auth/password-reset/confirm',
      data: { token, new_password: newPassword }
    });
  },

  async updateProfile(data: UserProfileUpdate): Promise<ApiResponse<User>> {
    return apiClient.request({
      method: 'PUT',
      url: '/auth/me',
      data
    });
  }
};
```

#### User Profile Management

**Files to Create:**
```
caca_peca/app/painel/perfil/page.tsx
caca_peca/components/dealership/profile-form.tsx
caca_peca/lib/types/user.ts (extend)
```

**Implementation:**
```typescript
// caca_peca/app/painel/perfil/page.tsx
'use client';

import { useAuth } from '@/lib/auth/context';
import { ProfileForm } from '@/components/dealership/profile-form';

export default function ProfilePage() {
  const { user } = useAuth();

  if (!user) return <div>Carregando...</div>;

  return (
    <div className="max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Meu Perfil</h1>
      <ProfileForm user={user} />
    </div>
  );
}
```

### 🏪 Dealership Panel Features

#### Inventory Management Implementation

**Files to Create/Modify:**
```
caca_peca/app/painel/estoque/page.tsx
caca_peca/app/painel/estoque/novo/page.tsx
caca_peca/app/painel/estoque/[id]/page.tsx
caca_peca/components/dealership/inventory-table.tsx
caca_peca/components/dealership/inventory-form.tsx
caca_peca/components/dealership/inventory-filters.tsx
caca_peca/lib/api/inventory.ts (complete)
```

**Step-by-Step Implementation:**

1. **Inventory List Page**
```typescript
// caca_peca/app/painel/estoque/page.tsx
'use client';

import { useState } from 'react';
import { useInventorySearch } from '@/lib/hooks/useApi';
import { InventoryTable } from '@/components/dealership/inventory-table';
import { InventoryFilters } from '@/components/dealership/inventory-filters';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';

export default function InventoryPage() {
  const [filters, setFilters] = useState({
    page: 1,
    size: 20,
    search: '',
    category: '',
    condition: ''
  });

  const { data, isLoading, error } = useInventorySearch(filters);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Gerenciar Estoque</h1>
        <Button asChild>
          <Link href="/painel/estoque/novo">
            <Plus className="w-4 h-4 mr-2" />
            Adicionar Item
          </Link>
        </Button>
      </div>

      <InventoryFilters filters={filters} onFiltersChange={setFilters} />

      {isLoading && <div>Carregando...</div>}
      {error && <div>Erro ao carregar estoque</div>}
      {data && <InventoryTable items={data.data.items} />}
    </div>
  );
}
```

2. **Inventory Table Component**
```typescript
// caca_peca/components/dealership/inventory-table.tsx
'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Eye } from 'lucide-react';
import { Inventory } from '@/lib/types/api';

interface InventoryTableProps {
  items: Inventory[];
}

export function InventoryTable({ items }: InventoryTableProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getConditionBadge = (condition: string) => {
    const variants = {
      new: 'default',
      used: 'secondary',
      refurbished: 'outline'
    };

    return (
      <Badge variant={variants[condition] || 'default'}>
        {condition === 'new' ? 'Novo' : condition === 'used' ? 'Usado' : 'Recondicionado'}
      </Badge>
    );
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Código</TableHead>
          <TableHead>Nome</TableHead>
          <TableHead>Categoria</TableHead>
          <TableHead>Condição</TableHead>
          <TableHead>Quantidade</TableHead>
          <TableHead>Preço</TableHead>
          <TableHead>Ações</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => (
          <TableRow key={item.id}>
            <TableCell className="font-mono">{item.part.code}</TableCell>
            <TableCell>{item.part.name}</TableCell>
            <TableCell>{item.part.category}</TableCell>
            <TableCell>{getConditionBadge(item.condition)}</TableCell>
            <TableCell>
              <span className={item.quantity <= item.minimum_stock ? 'text-red-600' : ''}>
                {item.quantity}
              </span>
            </TableCell>
            <TableCell>{formatPrice(item.price)}</TableCell>
            <TableCell>
              <div className="flex space-x-2">
                <Button variant="ghost" size="sm">
                  <Eye className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Edit className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
```

#### Bulk Import Implementation

**Files to Create:**
```
caca_peca/app/painel/importar-estoque/page.tsx
caca_peca/components/dealership/file-upload.tsx
caca_peca/components/dealership/import-progress.tsx
```

**Implementation:**
```typescript
// caca_peca/app/painel/importar-estoque/page.tsx
'use client';

import { useState } from 'react';
import { FileUpload } from '@/components/dealership/file-upload';
import { ImportProgress } from '@/components/dealership/import-progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function ImportInventoryPage() {
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'completed'>('idle');
  const [progress, setProgress] = useState(0);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <h1 className="text-2xl font-bold">Importar Estoque</h1>

      <Card>
        <CardHeader>
          <CardTitle>Upload de Arquivo</CardTitle>
        </CardHeader>
        <CardContent>
          <FileUpload
            onUploadStart={() => setUploadStatus('uploading')}
            onUploadProgress={setProgress}
            onUploadComplete={() => setUploadStatus('completed')}
          />
        </CardContent>
      </Card>

      {uploadStatus !== 'idle' && (
        <ImportProgress status={uploadStatus} progress={progress} />
      )}
    </div>
  );
}
```

### 🔍 Parts Catalog Enhancement

#### Part Details Page Implementation

**Files to Create:**
```
caca_peca/app/produto/[id]/page.tsx
caca_peca/components/parts/part-details.tsx
caca_peca/components/parts/compatibility-info.tsx
caca_peca/components/parts/alternative-parts.tsx
```

**Implementation:**
```typescript
// caca_peca/app/produto/[id]/page.tsx
import { notFound } from 'next/navigation';
import { PartDetails } from '@/components/parts/part-details';
import { partsApi } from '@/lib/api/parts';

interface PartPageProps {
  params: { id: string };
}

export default async function PartPage({ params }: PartPageProps) {
  try {
    const response = await partsApi.getPartById(params.id);

    if (!response.data.success) {
      notFound();
    }

    const part = response.data.data;

    return (
      <div className="container mx-auto py-8">
        <PartDetails part={part} />
      </div>
    );
  } catch (error) {
    notFound();
  }
}
```

### 💳 Subscription Management

#### Subscription Interface Implementation

**Files to Create:**
```
caca_peca/app/painel/assinatura/page.tsx
caca_peca/app/painel/faturas/page.tsx
caca_peca/components/dealership/subscription-card.tsx
caca_peca/components/dealership/billing-history.tsx
caca_peca/components/dealership/plan-selector.tsx
```

**Implementation:**
```typescript
// caca_peca/app/painel/assinatura/page.tsx
'use client';

import { useSubscription } from '@/lib/hooks/useApi';
import { SubscriptionCard } from '@/components/dealership/subscription-card';
import { PlanSelector } from '@/components/dealership/plan-selector';

export default function SubscriptionPage() {
  const { data: subscription, isLoading } = useSubscription();

  if (isLoading) return <div>Carregando...</div>;

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Gerenciar Assinatura</h1>

      {subscription ? (
        <SubscriptionCard subscription={subscription} />
      ) : (
        <PlanSelector />
      )}
    </div>
  );
}
```

## API Endpoint Mappings

### Authentication Endpoints
```
POST /api/v1/auth/login              → authApi.login()
POST /api/v1/auth/logout             → authApi.logout()
POST /api/v1/auth/register           → authApi.register()
POST /api/v1/auth/refresh            → authApi.refreshToken()
GET  /api/v1/auth/me                 → authApi.getCurrentUser()
PUT  /api/v1/auth/me                 → authApi.updateProfile()
POST /api/v1/auth/password-reset     → authApi.resetPassword()
POST /api/v1/auth/password-reset/confirm → authApi.confirmPasswordReset()
```

### Inventory Management Endpoints
```
GET    /api/v1/inventory/search      → inventoryApi.searchInventory()
POST   /api/v1/inventory             → inventoryApi.createInventoryItem()
GET    /api/v1/inventory/{id}        → inventoryApi.getInventoryItem()
PUT    /api/v1/inventory/{id}        → inventoryApi.updateInventoryItem()
DELETE /api/v1/inventory/{id}        → inventoryApi.deleteInventoryItem()
GET    /api/v1/inventory/stats       → inventoryApi.getInventoryStats()
POST   /api/v1/imports/inventory     → inventoryApi.bulkImport()
```

### Parts Catalog Endpoints
```
GET  /api/v1/parts/search            → partsApi.searchParts()
GET  /api/v1/parts/{id}              → partsApi.getPartById()
GET  /api/v1/parts/categories        → partsApi.getCategories()
GET  /api/v1/parts/brands            → partsApi.getBrands()
GET  /api/v1/parts/suggestions       → partsApi.getSuggestions()
GET  /api/v1/parts/compatibility/{id} → partsApi.getCompatibility()
```

### Subscription Management Endpoints
```
GET    /api/v1/subscriptions/me      → subscriptionApi.getCurrentSubscription()
POST   /api/v1/subscriptions         → subscriptionApi.createSubscription()
PUT    /api/v1/subscriptions/{id}    → subscriptionApi.updateSubscription()
DELETE /api/v1/subscriptions/{id}    → subscriptionApi.cancelSubscription()
GET    /api/v1/subscriptions/billing-history → subscriptionApi.getBillingHistory()
```

### Admin Panel Endpoints
```
GET  /api/v1/monitoring/metrics/summary → adminApi.getMetricsSummary()
GET  /api/v1/monitoring/alerts      → adminApi.getCurrentAlerts()
GET  /api/v1/dealerships/stats       → adminApi.getDealershipStats()
GET  /api/v1/parts/stats             → adminApi.getPartStats()
```

## React Query Hook Examples

### Custom Hooks Implementation
```typescript
// caca_peca/lib/hooks/useApi.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { inventoryApi, partsApi, subscriptionApi } from '@/lib/api';

// Inventory Hooks
export const useInventorySearch = (params: InventorySearchParams) => {
  return useQuery({
    queryKey: ['inventory', 'search', params],
    queryFn: () => inventoryApi.searchInventory(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!params
  });
};

export const useCreateInventoryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: inventoryApi.createInventoryItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
    }
  });
};

// Parts Hooks
export const usePartsSearch = (params: PartSearchParams) => {
  return useQuery({
    queryKey: ['parts', 'search', params],
    queryFn: () => partsApi.searchParts(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const usePartDetails = (id: string) => {
  return useQuery({
    queryKey: ['parts', 'detail', id],
    queryFn: () => partsApi.getPartById(id),
    enabled: !!id
  });
};

// Subscription Hooks
export const useSubscription = () => {
  return useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: subscriptionApi.getCurrentSubscription,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const useCreateSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: subscriptionApi.createSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    }
  });
};
```

## TypeScript Type Definitions

### Core API Types
```typescript
// caca_peca/lib/types/api.ts
export interface ApiResponse<T> {
  data: {
    success: boolean;
    data: T;
    message?: string;
  };
  status: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN' | 'DEALERSHIP' | 'CUSTOMER';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Inventory {
  id: string;
  dealership_id: string;
  part_id: string;
  part: Part;
  quantity: number;
  price: number;
  cost: number;
  location?: string;
  condition: 'new' | 'used' | 'refurbished';
  warranty_months?: number;
  is_available: boolean;
  minimum_stock: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Part {
  id: string;
  code: string;
  name: string;
  description?: string;
  category?: string;
  subcategory?: string;
  brand?: string;
  model?: string;
  year?: number;
  year_start?: number;
  year_end?: number;
  oem_codes: string[];
  created_at: string;
  updated_at: string;
}

export interface Subscription {
  id: string;
  dealership_id: string;
  asaas_subscription_id: string;
  plan_type: 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'CANCELLED';
  value: number;
  next_due_date?: string;
  billing_type: string;
  cycle: string;
  created_at: string;
  updated_at: string;
}
```

### Form Validation Schemas
```typescript
// caca_peca/lib/schemas/validation.ts
import { z } from 'zod';

export const inventoryCreateSchema = z.object({
  part_id: z.string().uuid(),
  quantity: z.number().min(0),
  price: z.number().min(0),
  cost: z.number().min(0),
  location: z.string().optional(),
  condition: z.enum(['new', 'used', 'refurbished']),
  warranty_months: z.number().min(0).max(120).optional(),
  minimum_stock: z.number().min(0),
  notes: z.string().max(500).optional()
});

export const subscriptionCreateSchema = z.object({
  plan_type: z.enum(['BASIC', 'PREMIUM', 'ENTERPRISE']),
  billing_type: z.enum(['BOLETO', 'CREDIT_CARD', 'PIX']),
  cycle: z.enum(['MONTHLY', 'YEARLY'])
});

export type InventoryCreateData = z.infer<typeof inventoryCreateSchema>;
export type SubscriptionCreateData = z.infer<typeof subscriptionCreateSchema>;
```

## Error Handling Strategy

### Global Error Handler
```typescript
// caca_peca/lib/utils/error-handler.ts
export class ApiError extends Error {
  constructor(
    public status: number,
    public message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const handleApiError = (error: any): ApiError => {
  if (error.response) {
    const { status, data } = error.response;
    return new ApiError(
      status,
      data.message || 'Erro na API',
      data.code
    );
  }

  if (error.request) {
    return new ApiError(0, 'Erro de conexão');
  }

  return new ApiError(500, error.message || 'Erro desconhecido');
};
```

### Error Boundary Component
```typescript
// caca_peca/components/error-boundary.tsx
'use client';

import { Component, ReactNode } from 'react';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <h2 className="text-xl font-semibold">Algo deu errado</h2>
          <p className="text-muted-foreground">
            Ocorreu um erro inesperado. Tente recarregar a página.
          </p>
          <Button onClick={() => window.location.reload()}>
            Recarregar Página
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Performance Optimization Guidelines

### Code Splitting Strategy
```typescript
// caca_peca/app/painel/layout.tsx
import dynamic from 'next/dynamic';

// Lazy load heavy components
const InventoryTable = dynamic(() => import('@/components/dealership/inventory-table'), {
  loading: () => <div>Carregando tabela...</div>
});

const AnalyticsCharts = dynamic(() => import('@/components/admin/analytics-charts'), {
  loading: () => <div>Carregando gráficos...</div>
});
```

### Caching Strategy
```typescript
// caca_peca/lib/api/client.ts
export class ApiClient {
  private setupCaching() {
    // Cache GET requests for 5 minutes
    this.axiosInstance.interceptors.request.use((config) => {
      if (config.method === 'get') {
        config.headers['Cache-Control'] = 'max-age=300';
      }
      return config;
    });
  }
}
```

### Image Optimization
```typescript
// caca_peca/components/parts/part-image.tsx
import Image from 'next/image';

interface PartImageProps {
  src: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg';
}

export function PartImage({ src, alt, size = 'md' }: PartImageProps) {
  const dimensions = {
    sm: { width: 100, height: 100 },
    md: { width: 200, height: 200 },
    lg: { width: 400, height: 400 }
  };

  return (
    <Image
      src={src}
      alt={alt}
      width={dimensions[size].width}
      height={dimensions[size].height}
      className="object-cover rounded-lg"
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      priority={size === 'lg'}
    />
  );
}
```

## Security Implementation

### Authentication Security
```typescript
// caca_peca/lib/auth/security.ts
export const securityConfig = {
  tokenStorage: 'httpOnly', // Use httpOnly cookies for tokens
  csrfProtection: true,
  sessionTimeout: 30 * 60 * 1000, // 30 minutes
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000 // 15 minutes
};

export const validateToken = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp > Date.now() / 1000;
  } catch {
    return false;
  }
};
```

### Input Sanitization
```typescript
// caca_peca/lib/utils/sanitize.ts
import DOMPurify from 'isomorphic-dompurify';

export const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] });
};

export const sanitizeHtml = (html: string): string => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: []
  });
};
```

## Deployment Configuration

### Docker Configuration
```dockerfile
# caca_peca/Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Environment Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build: ./caca_peca
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
    depends_on:
      - backend
    networks:
      - autoparts-network

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************************/autoparts
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    networks:
      - autoparts-network

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_USER=autoparts
      - POSTGRES_PASSWORD=autoparts123
      - POSTGRES_DB=autoparts
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - autoparts-network

  redis:
    image: redis:7-alpine
    networks:
      - autoparts-network

volumes:
  postgres_data:

networks:
  autoparts-network:
    driver: bridge
```

## Monitoring and Logging

### Frontend Monitoring
```typescript
// caca_peca/lib/monitoring/analytics.ts
export class Analytics {
  static trackPageView(page: string) {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
        page_title: page,
        page_location: window.location.href
      });
    }
  }

  static trackEvent(action: string, category: string, label?: string) {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label
      });
    }
  }

  static trackError(error: Error, context?: string) {
    console.error('Application Error:', error, context);

    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: false
      });
    }
  }
}
```

### Performance Monitoring
```typescript
// caca_peca/lib/monitoring/performance.ts
export class PerformanceMonitor {
  static measureApiCall<T>(
    apiCall: () => Promise<T>,
    endpoint: string
  ): Promise<T> {
    const startTime = performance.now();

    return apiCall()
      .then((result) => {
        const duration = performance.now() - startTime;
        this.logApiPerformance(endpoint, duration, 'success');
        return result;
      })
      .catch((error) => {
        const duration = performance.now() - startTime;
        this.logApiPerformance(endpoint, duration, 'error');
        throw error;
      });
  }

  private static logApiPerformance(
    endpoint: string,
    duration: number,
    status: 'success' | 'error'
  ) {
    console.log(`API Call: ${endpoint} - ${duration.toFixed(2)}ms - ${status}`);

    // Send to monitoring service
    if (duration > 1000) {
      console.warn(`Slow API call detected: ${endpoint} took ${duration.toFixed(2)}ms`);
    }
  }
}
```

This comprehensive implementation plan provides detailed guidance for completing the AutoParts frontend-backend integration with specific code examples, security considerations, deployment strategies, and monitoring solutions.
