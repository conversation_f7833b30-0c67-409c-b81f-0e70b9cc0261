#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Default values
DATABASE_HOST=${DATABASE_HOST:-localhost}
DATABASE_PORT=${DATABASE_PORT:-5432}
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}
ENVIRONMENT=${ENVIRONMENT:-development}
MAX_WAIT_TIME=${MAX_WAIT_TIME:-60}

log "Starting AutoParts API..."
log "Environment: $ENVIRONMENT"
log "Database: $DATABASE_HOST:$DATABASE_PORT"
log "Redis: $REDIS_HOST:$REDIS_PORT"

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local wait_time=0
    
    log "Waiting for $service_name at $host:$port..."
    
    while ! nc -z "$host" "$port"; do
        if [ $wait_time -ge $MAX_WAIT_TIME ]; then
            error "$service_name is not available after ${MAX_WAIT_TIME}s"
            exit 1
        fi
        sleep 1
        wait_time=$((wait_time + 1))
    done
    
    success "$service_name is ready!"
}

# Wait for database
wait_for_service "$DATABASE_HOST" "$DATABASE_PORT" "PostgreSQL"

# Wait for Redis (optional)
if [ "$REDIS_HOST" != "localhost" ] || [ "$ENVIRONMENT" = "production" ]; then
    wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis"
fi

# Run database migrations
log "Running database migrations..."
if alembic upgrade head; then
    success "Database migrations completed successfully"
else
    error "Database migrations failed"
    exit 1
fi

# Create initial data if needed
if [ "$ENVIRONMENT" = "development" ] && [ "$CREATE_SAMPLE_DATA" = "true" ]; then
    log "Creating sample data..."
    python -c "
from app.core.database import get_db
from app.core.init_db import init_db
init_db(next(get_db()))
" && success "Sample data created" || warning "Sample data creation failed"
fi

# Validate configuration
log "Validating configuration..."
python -c "
from app.core.config import settings
print(f'Database URL: {settings.DATABASE_URL[:20]}...')
print(f'Redis URL: {settings.REDIS_URL[:20]}...')
print(f'Environment: {settings.ENVIRONMENT}')
print(f'Debug: {settings.DEBUG}')
" && success "Configuration validated"

# Start the application based on environment
log "Starting AutoParts API server..."

if [ "$ENVIRONMENT" = "production" ]; then
    log "Starting production server with Gunicorn..."
    exec gunicorn app.main:app \
        --bind 0.0.0.0:8000 \
        --workers ${WORKERS:-4} \
        --worker-class uvicorn.workers.UvicornWorker \
        --worker-connections ${WORKER_CONNECTIONS:-1000} \
        --max-requests ${MAX_REQUESTS:-1000} \
        --max-requests-jitter ${MAX_REQUESTS_JITTER:-100} \
        --timeout ${TIMEOUT:-30} \
        --keep-alive ${KEEP_ALIVE:-5} \
        --access-logfile - \
        --error-logfile - \
        --log-level ${LOG_LEVEL:-info} \
        --preload
elif [ "$ENVIRONMENT" = "staging" ]; then
    log "Starting staging server with Gunicorn..."
    exec gunicorn app.main:app \
        --bind 0.0.0.0:8000 \
        --workers 2 \
        --worker-class uvicorn.workers.UvicornWorker \
        --access-logfile - \
        --error-logfile - \
        --log-level info
else
    log "Starting development server with Uvicorn..."
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --reload \
        --log-level ${LOG_LEVEL:-debug}
fi
