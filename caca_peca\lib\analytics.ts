type EventType = "phone_click" | "whatsapp_click" | "page_view"

interface AnalyticsEvent {
  type: EventType
  timestamp: number
  data?: any
}

// Armazenamento local de eventos para demonstração
const events: AnalyticsEvent[] = []

// Em um sistema real, isso enviaria dados para um backend
export function trackEvent(type: EventType, data?: any) {
  const event: AnalyticsEvent = {
    type,
    timestamp: Date.now(),
    data,
  }

  events.push(event)

  // Em produção, você enviaria para um backend
  console.log("Evento rastreado:", event)

  // Armazenar localmente para demonstração
  try {
    if (typeof window !== "undefined") {
      const storedEvents = JSON.parse(localStorage.getItem("analytics_events") || "[]")
      storedEvents.push(event)
      localStorage.setItem("analytics_events", JSON.stringify(storedEvents))
    }
  } catch (error) {
    console.error("Erro ao armazenar evento:", error)
  }

  return event
}

// Obter eventos por tipo
export function getEventsByType(type: EventType): AnalyticsEvent[] {
  if (typeof window === "undefined") return []

  try {
    const storedEvents = JSON.parse(localStorage.getItem("analytics_events") || "[]")
    return storedEvents.filter((event: AnalyticsEvent) => event.type === type)
  } catch (error) {
    console.error("Erro ao recuperar eventos:", error)
    return []
  }
}

// Obter contagem de eventos por tipo
export function getEventCountByType(type: EventType): number {
  return getEventsByType(type).length
}

// Limpar todos os eventos (para testes)
export function clearEvents() {
  if (typeof window !== "undefined") {
    localStorage.removeItem("analytics_events")
  }
}

