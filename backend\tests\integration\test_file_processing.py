"""
Integration tests for file processing functionality.
"""
import pytest
import os
from unittest.mock import patch
from sqlalchemy.orm import Session

from app.services.file_processor import FileProcessorService
from app.models.file_import import ImportType, ImportStatus
from app.schemas.file_import import ImportMappingConfig


@pytest.mark.integration
@pytest.mark.file_processing
class TestFileProcessing:
    """Test cases for file processing functionality."""
    
    def test_validate_excel_file_success(self, db_session: Session, sample_excel_file_path: str):
        """Test successful Excel file validation."""
        file_processor = FileProcessorService(db_session)
        
        result = file_processor.validate_excel_file(sample_excel_file_path)
        
        assert result.is_valid is True
        assert result.total_rows == 5
        assert len(result.detected_columns) == 7
        assert "EmpresaNome" in result.detected_columns
        assert "CodigoReferenciaProduto" in result.detected_columns
        assert "QtdeSaldo" in result.detected_columns
        assert len(result.sample_data) == 5
        assert len(result.mapping_suggestions) > 0
        assert len(result.validation_errors) == 0
    
    def test_validate_excel_file_not_found(self, db_session: Session):
        """Test Excel file validation with non-existent file."""
        file_processor = FileProcessorService(db_session)
        
        result = file_processor.validate_excel_file("nonexistent.xlsx")
        
        assert result.is_valid is False
        assert result.total_rows == 0
        assert len(result.detected_columns) == 0
        assert len(result.validation_errors) > 0
        assert "Error reading file" in result.validation_errors[0]
    
    def test_create_file_import_record(self, db_session: Session, sample_excel_file_path: str):
        """Test creating file import record."""
        file_processor = FileProcessorService(db_session)
        
        file_import = file_processor.create_file_import_record(
            file_path=sample_excel_file_path,
            filename="test_inventory.xlsx",
            import_type=ImportType.INVENTORY,
            created_by=None
        )
        
        assert file_import.id is not None
        assert file_import.filename == "test_inventory.xlsx"
        assert file_import.import_type == ImportType.INVENTORY
        assert file_import.status == ImportStatus.PENDING
        assert file_import.file_size > 0
        assert file_import.file_hash is not None
        assert len(file_import.file_hash) == 64  # SHA256 hash length
    
    def test_process_inventory_import_dry_run(self, db_session: Session, sample_excel_file_path: str):
        """Test inventory import processing in dry run mode."""
        file_processor = FileProcessorService(db_session)
        
        # Create file import record
        file_import = file_processor.create_file_import_record(
            file_path=sample_excel_file_path,
            filename="test_inventory.xlsx",
            import_type=ImportType.INVENTORY
        )
        
        # Create mapping configuration
        mapping_config = ImportMappingConfig(
            dealership_mapping={},
            part_mapping={},
            inventory_mapping={},
            default_values={},
            import_options={"dry_run": True}
        )
        
        # Process import
        result = file_processor.process_inventory_import(
            file_import, mapping_config, dry_run=True
        )
        
        assert result["status"] == "completed"
        assert result["total_rows"] == 5
        assert result["successful_rows"] >= 0
        assert result["failed_rows"] >= 0
        assert result["skipped_rows"] >= 0
        assert result["successful_rows"] + result["failed_rows"] + result["skipped_rows"] == result["total_rows"]
        
        # Verify file import status
        assert file_import.status == ImportStatus.COMPLETED
        assert file_import.total_rows == 5
        assert file_import.started_at is not None
        assert file_import.completed_at is not None
    
    def test_process_inventory_import_actual(self, db_session: Session, sample_excel_file_path: str):
        """Test actual inventory import processing."""
        file_processor = FileProcessorService(db_session)
        
        # Create file import record
        file_import = file_processor.create_file_import_record(
            file_path=sample_excel_file_path,
            filename="test_inventory.xlsx",
            import_type=ImportType.INVENTORY
        )
        
        # Create mapping configuration
        mapping_config = ImportMappingConfig(
            dealership_mapping={},
            part_mapping={},
            inventory_mapping={},
            default_values={},
            import_options={"dry_run": False}
        )
        
        # Process import
        result = file_processor.process_inventory_import(
            file_import, mapping_config, dry_run=False
        )
        
        assert result["status"] == "completed"
        assert result["total_rows"] == 5
        assert result["successful_rows"] >= 0
        
        # Verify data was actually created
        from app.models.dealership import Dealership
        from app.models.part import Part
        from app.models.inventory import Inventory
        
        # Check if dealership was created
        dealership = db_session.query(Dealership).filter(
            Dealership.name == "Test Dealership"
        ).first()
        assert dealership is not None
        
        # Check if parts were created
        parts = db_session.query(Part).filter(
            Part.code.in_(["TEST001", "TEST002", "TEST003", "TEST004", "TEST005"])
        ).all()
        assert len(parts) >= 1
        
        # Check if inventory items were created
        inventory_items = db_session.query(Inventory).filter(
            Inventory.dealership_id == dealership.id
        ).all()
        assert len(inventory_items) >= 1
    
    def test_process_csv_file(self, db_session: Session, sample_csv_file_path: str):
        """Test CSV file processing."""
        file_processor = FileProcessorService(db_session)
        
        # Validate CSV file (should work with pandas)
        result = file_processor.validate_excel_file(sample_csv_file_path)
        
        assert result.is_valid is True
        assert result.total_rows == 3
        assert "EmpresaNome" in result.detected_columns
        assert "CodigoReferenciaProduto" in result.detected_columns
    
    def test_file_hash_calculation(self, db_session: Session, sample_excel_file_path: str):
        """Test file hash calculation consistency."""
        file_processor = FileProcessorService(db_session)
        
        # Calculate hash twice
        hash1 = file_processor.calculate_file_hash(sample_excel_file_path)
        hash2 = file_processor.calculate_file_hash(sample_excel_file_path)
        
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA256 hash length
        assert isinstance(hash1, str)
    
    def test_mapping_suggestions_generation(self, db_session: Session):
        """Test mapping suggestions generation."""
        file_processor = FileProcessorService(db_session)
        
        columns = [
            "EmpresaNome",
            "CodigoReferenciaProduto",
            "Produto_Descricao",
            "QtdeSaldo",
            "ValorCustoMedio",
            "ValorPublico",
            "Marca_Descricao"
        ]
        
        suggestions = file_processor._generate_mapping_suggestions(columns)
        
        assert "EmpresaNome" in suggestions
        assert suggestions["EmpresaNome"] == "dealership.name"
        assert "CodigoReferenciaProduto" in suggestions
        assert suggestions["CodigoReferenciaProduto"] == "part.code"
        assert "QtdeSaldo" in suggestions
        assert suggestions["QtdeSaldo"] == "inventory.quantity"
    
    def test_data_type_validation(self, db_session: Session):
        """Test data type validation in sample data."""
        file_processor = FileProcessorService(db_session)
        
        sample_data = [
            {
                "QtdeSaldo": 10,
                "ValorCustoMedio": 50.00,
                "ValorPublico": "invalid_number"  # Invalid numeric value
            }
        ]
        
        warnings = []
        file_processor._validate_sample_data_types(sample_data, warnings)
        
        assert len(warnings) > 0
        assert any("Non-numeric value" in warning for warning in warnings)
    
    def test_get_import_progress(self, db_session: Session, sample_excel_file_path: str):
        """Test getting import progress."""
        file_processor = FileProcessorService(db_session)
        
        # Create file import record
        file_import = file_processor.create_file_import_record(
            file_path=sample_excel_file_path,
            filename="test_inventory.xlsx",
            import_type=ImportType.INVENTORY
        )
        
        # Get progress
        progress = file_processor.get_import_progress(file_import.id)
        
        assert progress is not None
        assert progress.id == file_import.id
        assert progress.status == ImportStatus.PENDING
    
    def test_get_import_errors(self, db_session: Session, sample_excel_file_path: str):
        """Test getting import errors."""
        file_processor = FileProcessorService(db_session)
        
        # Create file import record
        file_import = file_processor.create_file_import_record(
            file_path=sample_excel_file_path,
            filename="test_inventory.xlsx",
            import_type=ImportType.INVENTORY
        )
        
        # Get errors (should be empty for new import)
        errors = file_processor.get_import_errors(file_import.id)
        
        assert isinstance(errors, list)
        assert len(errors) == 0
    
    def test_find_or_create_dealership(self, db_session: Session):
        """Test finding or creating dealership."""
        file_processor = FileProcessorService(db_session)
        
        dealership_name = "New Test Dealership"
        
        # First call should create
        dealership1 = file_processor._find_or_create_dealership(dealership_name)
        assert dealership1.name == dealership_name
        assert dealership1.id is not None
        
        # Second call should find existing
        dealership2 = file_processor._find_or_create_dealership(dealership_name)
        assert dealership2.id == dealership1.id
    
    def test_find_or_create_part(self, db_session: Session):
        """Test finding or creating part."""
        file_processor = FileProcessorService(db_session)
        
        import pandas as pd
        row = pd.Series({
            "CodigoReferenciaProduto": "NEWPART001",
            "Produto_Descricao": "New Test Part",
            "Marca_Descricao": "New Brand"
        })
        
        mapping_config = ImportMappingConfig(
            dealership_mapping={},
            part_mapping={},
            inventory_mapping={},
            default_values={},
            import_options={}
        )
        
        # First call should create
        part1 = file_processor._find_or_create_part(row, mapping_config)
        assert part1.code == "NEWPART001"
        assert part1.name == "New Test Part"
        assert part1.brand == "New Brand"
        
        # Second call should find existing
        part2 = file_processor._find_or_create_part(row, mapping_config)
        assert part2.id == part1.id
    
    def test_error_handling_invalid_file(self, db_session: Session, tmp_path):
        """Test error handling with invalid file."""
        file_processor = FileProcessorService(db_session)
        
        # Create invalid file
        invalid_file = tmp_path / "invalid.xlsx"
        invalid_file.write_text("This is not an Excel file")
        
        result = file_processor.validate_excel_file(str(invalid_file))
        
        assert result.is_valid is False
        assert len(result.validation_errors) > 0
        assert "Error reading file" in result.validation_errors[0]
    
    def test_large_file_handling(self, db_session: Session, tmp_path):
        """Test handling of larger files."""
        import pandas as pd
        
        file_processor = FileProcessorService(db_session)
        
        # Create larger test file
        data = {
            "EmpresaNome": ["Large Test Dealership"] * 100,
            "CodigoReferenciaProduto": [f"LARGE{i:03d}" for i in range(100)],
            "Produto_Descricao": [f"Large Test Part {i}" for i in range(100)],
            "QtdeSaldo": [i + 1 for i in range(100)],
            "ValorCustoMedio": [50.00 + i for i in range(100)],
            "ValorPublico": [99.99 + i for i in range(100)]
        }
        
        df = pd.DataFrame(data)
        large_file = tmp_path / "large_test.xlsx"
        df.to_excel(large_file, index=False)
        
        # Validate large file
        result = file_processor.validate_excel_file(str(large_file))
        
        assert result.is_valid is True
        assert result.total_rows == 100
        assert len(result.sample_data) == 5  # Should still limit sample data
