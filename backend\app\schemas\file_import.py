"""
Pydantic schemas for file import operations.
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from uuid import UUID

from app.models.file_import import ImportStatus, ImportType


class FileImportBase(BaseModel):
    """Base file import schema."""
    filename: str = Field(..., min_length=1, max_length=255, description="Original filename")
    import_type: ImportType = Field(..., description="Type of import")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")


class FileImportCreate(FileImportBase):
    """Schema for creating a new file import."""
    file_path: str = Field(..., description="Path to the uploaded file")
    file_size: int = Field(..., gt=0, description="File size in bytes")
    file_hash: Optional[str] = Field(None, description="SHA256 hash of the file")
    mapping_config: Optional[Dict[str, Any]] = Field(None, description="Column mapping configuration")


class FileImportUpdate(BaseModel):
    """Schema for updating file import information."""
    status: Optional[ImportStatus] = None
    total_rows: Optional[int] = Field(None, ge=0)
    processed_rows: Optional[int] = Field(None, ge=0)
    successful_rows: Optional[int] = Field(None, ge=0)
    failed_rows: Optional[int] = Field(None, ge=0)
    skipped_rows: Optional[int] = Field(None, ge=0)
    error_details: Optional[Dict[str, Any]] = None
    validation_errors: Optional[Dict[str, Any]] = None
    notes: Optional[str] = Field(None, max_length=1000)


class FileImportInDB(FileImportBase):
    """Schema for file import in database."""
    id: UUID
    file_path: str
    file_size: int
    file_hash: Optional[str] = None
    status: ImportStatus
    mapping_config: Optional[Dict[str, Any]] = None
    total_rows: int
    processed_rows: int
    successful_rows: int
    failed_rows: int
    skipped_rows: int
    error_details: Optional[Dict[str, Any]] = None
    validation_errors: Optional[Dict[str, Any]] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class FileImport(FileImportInDB):
    """Public file import schema."""
    progress_percentage: float = Field(0.0, description="Import progress percentage")
    success_rate: float = Field(0.0, description="Success rate percentage")


class FileImportSummary(BaseModel):
    """Summary schema for file import listings."""
    id: UUID
    filename: str
    import_type: ImportType
    status: ImportStatus
    total_rows: int
    processed_rows: int
    successful_rows: int
    failed_rows: int
    progress_percentage: float
    success_rate: float
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class FileImportListResponse(BaseModel):
    """Response schema for file import listing."""
    imports: List[FileImportSummary]
    total: int
    page: int
    per_page: int
    pages: int


class ImportRowErrorBase(BaseModel):
    """Base schema for import row errors."""
    row_number: int = Field(..., ge=1, description="Row number in the original file")
    error_type: str = Field(..., min_length=1, max_length=50, description="Type of error")
    error_message: str = Field(..., min_length=1, description="Detailed error message")
    field_name: Optional[str] = Field(None, max_length=100, description="Field that caused the error")
    field_value: Optional[str] = Field(None, description="Value that caused the error")


class ImportRowErrorCreate(ImportRowErrorBase):
    """Schema for creating import row error."""
    import_id: UUID = Field(..., description="Reference to the file import")
    row_data: Optional[Dict[str, Any]] = Field(None, description="Original row data")


class ImportRowError(ImportRowErrorBase):
    """Public import row error schema."""
    id: UUID
    import_id: UUID
    row_data: Optional[Dict[str, Any]] = None
    created_at: datetime

    class Config:
        from_attributes = True


class FileUploadResponse(BaseModel):
    """Response schema for file upload."""
    success: bool
    message: str
    file_id: Optional[UUID] = None
    filename: str
    file_size: int
    upload_time: datetime


class ImportProgressResponse(BaseModel):
    """Response schema for import progress."""
    import_id: UUID
    status: ImportStatus
    progress_percentage: float
    total_rows: int
    processed_rows: int
    successful_rows: int
    failed_rows: int
    skipped_rows: int
    success_rate: float
    started_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    current_operation: Optional[str] = None


class ImportValidationResult(BaseModel):
    """Schema for import validation results."""
    is_valid: bool
    total_rows: int
    sample_data: List[Dict[str, Any]]
    detected_columns: List[str]
    mapping_suggestions: Dict[str, str]
    validation_errors: List[str]
    warnings: List[str]


class ColumnMappingConfig(BaseModel):
    """Schema for column mapping configuration."""
    excel_column: str = Field(..., description="Excel column name")
    database_field: str = Field(..., description="Database field name")
    is_required: bool = Field(False, description="Whether the field is required")
    data_type: str = Field("string", description="Expected data type")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    transformation_rules: Optional[Dict[str, Any]] = Field(None, description="Data transformation rules")


class ImportMappingConfig(BaseModel):
    """Schema for complete import mapping configuration."""
    dealership_mapping: Dict[str, ColumnMappingConfig]
    part_mapping: Dict[str, ColumnMappingConfig]
    inventory_mapping: Dict[str, ColumnMappingConfig]
    default_values: Optional[Dict[str, Any]] = Field(None, description="Default values for missing fields")
    import_options: Optional[Dict[str, Any]] = Field(None, description="Import options and settings")


class BulkImportRequest(BaseModel):
    """Schema for bulk import request."""
    file_id: UUID = Field(..., description="File import ID")
    mapping_config: ImportMappingConfig = Field(..., description="Column mapping configuration")
    import_options: Optional[Dict[str, Any]] = Field(None, description="Import options")
    dry_run: bool = Field(False, description="Whether to perform a dry run")


class BulkImportResponse(BaseModel):
    """Response schema for bulk import operation."""
    import_id: UUID
    status: ImportStatus
    message: str
    total_rows: int
    processed_rows: int
    successful_rows: int
    failed_rows: int
    skipped_rows: int
    errors: List[ImportRowError]
    warnings: List[str]
    processing_time_seconds: float


class ImportStats(BaseModel):
    """Schema for import statistics."""
    total_imports: int
    successful_imports: int
    failed_imports: int
    processing_imports: int
    total_rows_processed: int
    total_rows_successful: int
    average_success_rate: float
    recent_imports: List[FileImportSummary]
