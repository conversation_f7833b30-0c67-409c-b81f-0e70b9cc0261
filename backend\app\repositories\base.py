"""
Base repository with common CRUD operations.
"""
from typing import Generic, TypeVar, Type, Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import IntegrityError

from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


class BaseRepository(Generic[ModelType]):
    """
    Base repository class with common CRUD operations.
    
    This class provides standard database operations that can be
    inherited by specific model repositories.
    """

    def __init__(self, model: Type[ModelType], db: Session):
        """
        Initialize repository with model and database session.
        
        Args:
            model: SQLAlchemy model class
            db: Database session
        """
        self.model = model
        self.db = db

    def create(self, obj_in: Dict[str, Any]) -> ModelType:
        """
        Create a new record.
        
        Args:
            obj_in: Dictionary with object data
            
        Returns:
            Created model instance
            
        Raises:
            IntegrityError: If there's a database constraint violation
        """
        try:
            db_obj = self.model(**obj_in)
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            self.db.rollback()
            raise e

    def get_by_id(self, id: UUID) -> Optional[ModelType]:
        """
        Get record by ID.
        
        Args:
            id: Record UUID
            
        Returns:
            Model instance or None if not found
        """
        return self.db.query(self.model).filter(self.model.id == id).first()

    def get_by_field(self, field_name: str, value: Any) -> Optional[ModelType]:
        """
        Get record by specific field.
        
        Args:
            field_name: Name of the field to filter by
            value: Value to search for
            
        Returns:
            Model instance or None if not found
        """
        field = getattr(self.model, field_name)
        return self.db.query(self.model).filter(field == value).first()

    def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        Get multiple records with pagination and filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Dictionary of field filters
            order_by: Field name to order by
            order_desc: Whether to order in descending order
            
        Returns:
            List of model instances
        """
        query = self.db.query(self.model)
        
        # Apply filters
        if filters:
            for field_name, value in filters.items():
                if hasattr(self.model, field_name):
                    field = getattr(self.model, field_name)
                    if isinstance(value, list):
                        query = query.filter(field.in_(value))
                    else:
                        query = query.filter(field == value)
        
        # Apply ordering
        if order_by and hasattr(self.model, order_by):
            field = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(field))
            else:
                query = query.order_by(asc(field))
        
        return query.offset(skip).limit(limit).all()

    def update(self, id: UUID, obj_in: Dict[str, Any]) -> Optional[ModelType]:
        """
        Update record by ID.
        
        Args:
            id: Record UUID
            obj_in: Dictionary with updated data
            
        Returns:
            Updated model instance or None if not found
        """
        db_obj = self.get_by_id(id)
        if not db_obj:
            return None
        
        try:
            for field, value in obj_in.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
            
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            self.db.rollback()
            raise e

    def delete(self, id: UUID) -> bool:
        """
        Delete record by ID.
        
        Args:
            id: Record UUID
            
        Returns:
            True if deleted, False if not found
        """
        db_obj = self.get_by_id(id)
        if not db_obj:
            return False
        
        self.db.delete(db_obj)
        self.db.commit()
        return True

    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filtering.
        
        Args:
            filters: Dictionary of field filters
            
        Returns:
            Number of records
        """
        query = self.db.query(self.model)
        
        if filters:
            for field_name, value in filters.items():
                if hasattr(self.model, field_name):
                    field = getattr(self.model, field_name)
                    if isinstance(value, list):
                        query = query.filter(field.in_(value))
                    else:
                        query = query.filter(field == value)
        
        return query.count()

    def exists(self, id: UUID) -> bool:
        """
        Check if record exists by ID.
        
        Args:
            id: Record UUID
            
        Returns:
            True if exists, False otherwise
        """
        return self.db.query(self.model).filter(self.model.id == id).first() is not None
