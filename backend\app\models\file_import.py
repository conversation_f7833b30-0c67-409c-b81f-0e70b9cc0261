"""
File import models for tracking import operations.
"""
from sqlalchemy import Column, String, DateTime, Integer, Text, Boolean, DECIMAL, JSON
from datetime import datetime
from enum import Enum

from app.models.base import BaseModel
from app.core.database_types import UUID


class ImportStatus(str, Enum):
    """Import status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ImportType(str, Enum):
    """Import type enumeration."""
    INVENTORY = "inventory"
    PARTS = "parts"
    DEALERSHIPS = "dealerships"
    MIXED = "mixed"


class FileImport(BaseModel):
    """
    File import model for tracking import operations.
    
    Attributes:
        filename: Original filename
        file_path: Path to the uploaded file
        file_size: File size in bytes
        import_type: Type of import (inventory, parts, etc.)
        status: Current import status
        total_rows: Total number of rows in the file
        processed_rows: Number of rows processed
        successful_rows: Number of successfully imported rows
        failed_rows: Number of failed rows
        error_details: JSON object with error details
        started_at: When import started
        completed_at: When import completed
        created_by: User who initiated the import
    """
    __tablename__ = "file_imports"

    # File Information
    filename = Column(
        String(255),
        nullable=False,
        doc="Original filename"
    )
    
    file_path = Column(
        String(500),
        nullable=False,
        doc="Path to the uploaded file"
    )
    
    file_size = Column(
        Integer,
        nullable=False,
        doc="File size in bytes"
    )
    
    file_hash = Column(
        String(64),
        nullable=True,
        doc="SHA256 hash of the file for duplicate detection"
    )

    # Import Configuration
    import_type = Column(
        String(20),
        nullable=False,
        doc="Type of import: inventory, parts, dealerships, mixed"
    )
    
    status = Column(
        String(20),
        nullable=False,
        default=ImportStatus.PENDING,
        doc="Current import status"
    )
    
    mapping_config = Column(
        JSON,
        nullable=True,
        doc="Column mapping configuration"
    )

    # Progress Tracking
    total_rows = Column(
        Integer,
        nullable=True,
        default=0,
        doc="Total number of rows in the file"
    )
    
    processed_rows = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of rows processed"
    )
    
    successful_rows = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of successfully imported rows"
    )
    
    failed_rows = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of failed rows"
    )
    
    skipped_rows = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of skipped rows"
    )

    # Error Tracking
    error_details = Column(
        JSON,
        nullable=True,
        doc="JSON object with error details and failed row information"
    )

    validation_errors = Column(
        JSON,
        nullable=True,
        doc="Validation errors found during processing"
    )

    # Timestamps
    started_at = Column(
        DateTime,
        nullable=True,
        doc="When import processing started"
    )
    
    completed_at = Column(
        DateTime,
        nullable=True,
        doc="When import processing completed"
    )

    # User Information
    created_by = Column(
        UUID(),
        nullable=True,
        doc="User who initiated the import"
    )

    # Additional Information
    notes = Column(
        Text,
        nullable=True,
        doc="Additional notes about the import"
    )

    def __repr__(self):
        return f"<FileImport(id={self.id}, filename='{self.filename}', status='{self.status}')>"

    @property
    def progress_percentage(self):
        """Calculate import progress percentage."""
        if not self.total_rows or self.total_rows == 0:
            return 0
        return round((self.processed_rows / self.total_rows) * 100, 2)

    @property
    def success_rate(self):
        """Calculate success rate percentage."""
        if not self.processed_rows or self.processed_rows == 0:
            return 0
        return round((self.successful_rows / self.processed_rows) * 100, 2)

    def update_progress(self, processed: int, successful: int, failed: int, skipped: int = 0):
        """Update import progress."""
        self.processed_rows = processed
        self.successful_rows = successful
        self.failed_rows = failed
        self.skipped_rows = skipped

    def mark_started(self):
        """Mark import as started."""
        self.status = ImportStatus.PROCESSING
        self.started_at = datetime.utcnow()

    def mark_completed(self):
        """Mark import as completed."""
        self.status = ImportStatus.COMPLETED
        self.completed_at = datetime.utcnow()

    def mark_failed(self, error_message: str = None):
        """Mark import as failed."""
        self.status = ImportStatus.FAILED
        self.completed_at = datetime.utcnow()
        if error_message:
            if not self.error_details:
                self.error_details = {}
            self.error_details['general_error'] = error_message


class ImportRowError(BaseModel):
    """
    Model for tracking individual row import errors.
    """
    __tablename__ = "import_row_errors"

    # Reference to import
    import_id = Column(
        UUID(),
        nullable=False,
        doc="Reference to the file import"
    )

    # Row Information
    row_number = Column(
        Integer,
        nullable=False,
        doc="Row number in the original file"
    )
    
    row_data = Column(
        JSON,
        nullable=True,
        doc="Original row data"
    )

    # Error Information
    error_type = Column(
        String(50),
        nullable=False,
        doc="Type of error (validation, database, etc.)"
    )
    
    error_message = Column(
        Text,
        nullable=False,
        doc="Detailed error message"
    )
    
    field_name = Column(
        String(100),
        nullable=True,
        doc="Field that caused the error"
    )
    
    field_value = Column(
        Text,
        nullable=True,
        doc="Value that caused the error"
    )

    def __repr__(self):
        return f"<ImportRowError(import_id={self.import_id}, row={self.row_number}, error='{self.error_type}')>"
