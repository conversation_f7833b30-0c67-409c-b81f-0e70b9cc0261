# AutoParts Backend API

A FastAPI-based backend for the AutoParts B2B marketplace, providing APIs for automotive parts inventory management, dealership management, and payment processing through Asaas integration.

## Features

- **FastAPI Framework**: Modern, fast web framework for building APIs
- **PostgreSQL Database**: Robust relational database with SQLAlchemy ORM
- **Redis Caching**: High-performance caching layer
- **JWT Authentication**: Secure authentication with role-based access control
- **Asaas Integration**: Payment processing and subscription management
- **File Processing**: Excel/CSV inventory file processing with Pandas
- **Background Tasks**: Celery for asynchronous task processing
- **API Documentation**: Automatic OpenAPI/Swagger documentation

## Project Structure

```
backend/
├── app/
│   ├── api/                 # API endpoints
│   │   └── v1/             # API version 1
│   ├── core/               # Core configuration
│   ├── models/             # SQLAlchemy models
│   ├── schemas/            # Pydantic schemas
│   ├── services/           # Business logic
│   ├── repositories/       # Data access layer
│   └── main.py            # FastAPI application
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
└── README.md              # This file
```

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL 14+
- Redis 6+

### Installation

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run the application:
```bash
python -m app.main
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/api/v1/docs
- **Health Check**: http://localhost:8000/health

## Development

### Code Quality

```bash
# Format code
black app/

# Lint code
flake8 app/

# Type checking
mypy app/
```

### Testing

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=app
```

## API Endpoints

### Health Check
- `GET /health` - Application health status

### API v1
- `GET /api/v1/` - API information
- `GET /api/v1/docs` - Interactive API documentation
- `GET /api/v1/redoc` - Alternative API documentation

## Environment Variables

See `.env.example` for all available configuration options.

## License

This project is proprietary software for AutoParts marketplace.
