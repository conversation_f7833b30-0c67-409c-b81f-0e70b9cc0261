"""
Logging middleware for request/response tracking and performance monitoring.
"""
import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import json

from app.core.logging import get_logger, performance_logger, security_logger, request_context_filter
from app.core.config import settings


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for comprehensive request/response logging and monitoring.
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("app.middleware.logging")
        self.excluded_paths = {
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with comprehensive logging.
        
        Args:
            request: FastAPI request
            call_next: Next middleware/endpoint
            
        Returns:
            Response with logging
        """
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        # Extract user information if available
        user_id = None
        if hasattr(request.state, 'current_user') and request.state.current_user:
            user_id = str(request.state.current_user.id)
        
        # Set request context for logging
        request_context_filter.set_request_context(request_id, user_id)
        
        # Skip logging for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        # Start timing
        start_time = time.time()
        
        # Extract request information
        request_info = await self._extract_request_info(request)
        
        # Log incoming request
        self.logger.info(
            f"Incoming Request: {request.method} {request.url.path}",
            request_id=request_id,
            user_id=user_id,
            **request_info
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Extract response information
            response_info = self._extract_response_info(response, processing_time)
            
            # Log response
            self.logger.info(
                f"Response: {request.method} {request.url.path} - {response.status_code}",
                request_id=request_id,
                user_id=user_id,
                **response_info
            )
            
            # Log performance metrics
            performance_logger.log_api_request(
                status_code=response.status_code,
                duration=processing_time,
                request_id=request_id,
                user_id=user_id,
                **request_info
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(round(processing_time * 1000, 2))
            
            # Log slow requests
            if processing_time > 2.0:  # Requests taking more than 2 seconds
                self.logger.warning(
                    f"Slow Request: {request.method} {request.url.path} took {processing_time:.2f}s",
                    request_id=request_id,
                    user_id=user_id,
                    processing_time=processing_time,
                    **request_info
                )
            
            # Log security events
            await self._log_security_events(request, response, request_id, user_id)
            
            return response
            
        except Exception as e:
            # Calculate processing time for failed requests
            processing_time = time.time() - start_time
            
            # Log error
            self.logger.error(
                f"Request Error: {request.method} {request.url.path} - {str(e)}",
                request_id=request_id,
                user_id=user_id,
                error_type=type(e).__name__,
                error_message=str(e),
                processing_time=processing_time,
                **request_info
            )
            
            # Re-raise the exception
            raise e
    
    async def _extract_request_info(self, request: Request) -> dict:
        """Extract relevant information from request."""
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Get user agent
        user_agent = request.headers.get("user-agent", "Unknown")
        
        # Get query parameters
        query_params = dict(request.query_params) if request.query_params else {}
        
        # Get path parameters
        path_params = dict(request.path_params) if request.path_params else {}
        
        # Get content type
        content_type = request.headers.get("content-type", "")
        
        # Get content length
        content_length = request.headers.get("content-length", "0")
        
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "content_type": content_type,
            "content_length": int(content_length) if content_length.isdigit() else 0,
            "query_params": query_params,
            "path_params": path_params,
            "headers": dict(request.headers) if settings.DEBUG else {}
        }
        
        # Add request body for POST/PUT/PATCH requests (if small enough)
        if request.method in ["POST", "PUT", "PATCH"] and int(content_length or 0) < 10000:
            try:
                # Read body without consuming it
                body = await request.body()
                if body:
                    if content_type.startswith("application/json"):
                        try:
                            request_info["body"] = json.loads(body.decode())
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            request_info["body"] = "<binary_data>"
                    else:
                        request_info["body"] = "<non_json_data>"
            except Exception:
                request_info["body"] = "<error_reading_body>"
        
        return request_info
    
    def _extract_response_info(self, response: Response, processing_time: float) -> dict:
        """Extract relevant information from response."""
        return {
            "status_code": response.status_code,
            "processing_time_ms": round(processing_time * 1000, 2),
            "response_headers": dict(response.headers) if settings.DEBUG else {},
            "content_type": response.headers.get("content-type", ""),
            "content_length": response.headers.get("content-length", "0")
        }
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address considering proxies."""
        # Check for forwarded headers (common in production with load balancers)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    async def _log_security_events(
        self,
        request: Request,
        response: Response,
        request_id: str,
        user_id: str
    ):
        """Log security-related events."""
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "Unknown")
        
        # Log authentication attempts
        if request.url.path.startswith("/api/v1/auth/"):
            if request.method == "POST":
                if response.status_code == 200:
                    security_logger.log_authentication_attempt(
                        email="<extracted_from_request>",  # Would extract from request body
                        success=True,
                        ip_address=client_ip,
                        user_agent=user_agent,
                        request_id=request_id
                    )
                elif response.status_code in [401, 403]:
                    security_logger.log_authentication_attempt(
                        email="<extracted_from_request>",  # Would extract from request body
                        success=False,
                        ip_address=client_ip,
                        user_agent=user_agent,
                        request_id=request_id
                    )
        
        # Log authorization failures
        if response.status_code == 403:
            security_logger.log_authorization_failure(
                user_id=user_id or "anonymous",
                resource=request.url.path,
                action=request.method,
                ip_address=client_ip,
                request_id=request_id
            )
        
        # Log suspicious activity patterns
        if response.status_code == 429:  # Rate limiting
            security_logger.log_suspicious_activity(
                activity="rate_limit_exceeded",
                user_id=user_id,
                ip_address=client_ip,
                details={
                    "path": request.url.path,
                    "method": request.method,
                    "user_agent": user_agent
                },
                request_id=request_id
            )


class DatabaseQueryLoggingMiddleware:
    """
    Middleware for logging database query performance.
    """
    
    def __init__(self):
        self.logger = get_logger("app.database")
    
    def log_query(
        self,
        query: str,
        params: dict,
        duration: float,
        rows_affected: int = None
    ):
        """Log database query with performance metrics."""
        # Extract table name from query
        query_lower = query.lower().strip()
        table_name = "unknown"
        
        if query_lower.startswith("select"):
            # Extract table from SELECT query
            if " from " in query_lower:
                parts = query_lower.split(" from ")[1].split()
                if parts:
                    table_name = parts[0].strip()
        elif query_lower.startswith("insert"):
            # Extract table from INSERT query
            if " into " in query_lower:
                parts = query_lower.split(" into ")[1].split()
                if parts:
                    table_name = parts[0].strip()
        elif query_lower.startswith("update"):
            # Extract table from UPDATE query
            parts = query_lower.split("update ")[1].split()
            if parts:
                table_name = parts[0].strip()
        elif query_lower.startswith("delete"):
            # Extract table from DELETE query
            if " from " in query_lower:
                parts = query_lower.split(" from ")[1].split()
                if parts:
                    table_name = parts[0].strip()
        
        # Determine query type
        query_type = query_lower.split()[0].upper() if query_lower else "UNKNOWN"
        
        # Log query performance
        performance_logger.log_database_query(
            query_type=query_type,
            table=table_name,
            duration=duration,
            rows_affected=rows_affected
        )
        
        # Log slow queries
        if duration > 1.0:  # Queries taking more than 1 second
            self.logger.warning(
                f"Slow Query: {query_type} on {table_name} took {duration:.2f}s",
                query_type=query_type,
                table=table_name,
                duration=duration,
                query=query[:200] + "..." if len(query) > 200 else query,
                params=params if settings.DEBUG else "<hidden>"
            )


# Global database query logger
db_query_logger = DatabaseQueryLoggingMiddleware()
