"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { <PERSON>u, User, ShoppingCart } from "lucide-react"
import { useState } from "react"
import { usePathname } from "next/navigation"
import { ThemeToggleButton } from "@/components/theme-toggle-button"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  const navigation = [
    { name: "Home", href: "/" },
    { name: "Concessionárias Parceiras", href: "/concessionarias" },
    { name: "Anuncie seu Estoque", href: "/para-concessionarias" },
    { name: "Contato", href: "/contato" },
  ]

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-colors">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6 md:gap-10">
          <Link href="/" className="flex items-center space-x-2">
            <ShoppingCart className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">AutoParts</span>
          </Link>

          <nav className="hidden gap-6 md:flex">
            {navigation.map((item) => {
              const isActive = pathname === item.href || (item.href !== "/" && pathname?.startsWith(item.href))

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    isActive ? "text-primary font-semibold border-b-2 border-primary" : "text-muted-foreground"
                  }`}
                  scroll={true}
                  replace={item.href === "/para-concessionarias"}
                >
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <ThemeToggleButton />
          <Button variant="ghost" size="icon" asChild className="hidden md:flex">
            <Link href="/auth" scroll={true}>
              <User className="h-5 w-5" />
              <span className="sr-only">Minha conta</span>
            </Link>
          </Button>
          <Button asChild className="hidden md:flex">
            <Link href="/auth" scroll={true}>
              Entrar
            </Link>
          </Button>

          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Abrir menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col gap-6 py-6">
                <Link href="/" className="flex items-center space-x-2" onClick={() => setIsOpen(false)}>
                  <ShoppingCart className="h-6 w-6 text-primary" />
                  <span className="text-xl font-bold">AutoParts</span>
                </Link>
                <nav className="flex flex-col gap-4">
                  {navigation.map((item) => {
                    const isActive = pathname === item.href || (item.href !== "/" && pathname?.startsWith(item.href))

                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`text-sm font-medium transition-colors hover:text-primary ${
                          isActive ? "text-primary font-semibold" : "text-muted-foreground"
                        }`}
                        onClick={() => setIsOpen(false)}
                        scroll={true}
                        replace={item.href === "/para-concessionarias"}
                      >
                        {item.name}
                      </Link>
                    )
                  })}
                </nav>
                <div className="flex flex-col gap-2">
                  <Button asChild>
                    <Link href="/auth" onClick={() => setIsOpen(false)} scroll={true}>
                      Entrar
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/auth?register=true" onClick={() => setIsOpen(false)} scroll={true}>
                      Cadastrar
                    </Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}

