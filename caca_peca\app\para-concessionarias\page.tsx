import Link from "next/link"
import Image from "next/image"
import {
  UserPlus,
  CreditCard,
  Upload,
  Tag,
  Settings,
  MessageSquare,
  CheckCircle,
  FileSpreadsheet,
  FileText,
  FileType,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

export default function DealershipGuidePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      {/* Hero Section */}
      <section className="bg-primary/10 py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="mb-6 text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl">
            Aumente suas vendas de peças com a AutoParts
          </h1>
          <p className="mx-auto mb-8 max-w-3xl text-xl text-muted-foreground">
            Conecte-se diretamente com clientes que buscam peças genuínas para seus veículos
          </p>
          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Link href="/cadastro-concessionaria">
              <Button size="lg" className="w-full sm:w-auto">
                Cadastre sua concessionária
              </Button>
            </Link>
            <Link href="#como-funciona">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Saiba como funciona
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold tracking-tight md:text-4xl">
            Benefícios para sua concessionária
          </h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Aumente suas vendas</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Alcance clientes que buscam peças genuínas para seus veículos, aumentando seu volume de vendas.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Reduza seu estoque parado</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Dê visibilidade ao seu estoque de peças e reduza o tempo de permanência em prateleira.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Geração de leads qualificados</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Receba contatos de clientes realmente interessados em comprar peças específicas.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Gestão simplificada</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Painel administrativo intuitivo para gerenciar seu catálogo, promoções e leads.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Importação facilitada</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Importe seu estoque facilmente a partir de arquivos CSV, TXT ou XLS.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Visibilidade online</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Destaque sua concessionária em nossa plataforma e atraia novos clientes.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="como-funciona" className="bg-secondary/10 py-16">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold tracking-tight md:text-4xl">
            Como funciona para concessionárias
          </h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <UserPlus className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">1. Cadastro</h3>
              <p className="text-muted-foreground">
                Registre sua concessionária em nossa plataforma com informações básicas como nome, CNPJ e contato.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <CreditCard className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">2. Escolha do plano</h3>
              <p className="text-muted-foreground">
                Selecione o plano que melhor atende às necessidades da sua concessionária.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <Upload className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">3. Importação de estoque</h3>
              <p className="text-muted-foreground">Importe seu estoque de peças através de arquivos CSV, TXT ou XLS.</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <Tag className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">4. Criação de promoções</h3>
              <p className="text-muted-foreground">
                Configure promoções especiais para destacar determinadas peças ou categorias.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <Settings className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">5. Personalização do perfil</h3>
              <p className="text-muted-foreground">
                Personalize o perfil da sua concessionária com logo, fotos e informações adicionais.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <MessageSquare className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">6. Recebimento de leads</h3>
              <p className="text-muted-foreground">
                Comece a receber contatos de clientes interessados em suas peças via WhatsApp.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* File Format Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="mb-8 text-center text-3xl font-bold tracking-tight md:text-4xl">
            Formatos de arquivo suportados
          </h2>
          <p className="mx-auto mb-12 max-w-3xl text-center text-lg text-muted-foreground">
            Nossa plataforma suporta diversos formatos de arquivo para importação do seu estoque de peças.
          </p>

          <Tabs defaultValue="csv" className="mx-auto max-w-4xl">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="csv" className="flex items-center gap-2">
                <FileSpreadsheet className="h-4 w-4" />
                CSV
              </TabsTrigger>
              <TabsTrigger value="txt" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                TXT
              </TabsTrigger>
              <TabsTrigger value="xls" className="flex items-center gap-2">
                <FileType className="h-4 w-4" />
                XLS/XLSX
              </TabsTrigger>
            </TabsList>
            <TabsContent value="csv" className="mt-6 rounded-md border p-6">
              <h3 className="mb-4 text-xl font-semibold">Formato CSV</h3>
              <p className="mb-4">Arquivo CSV (Comma-Separated Values) com as seguintes colunas:</p>
              <div className="overflow-x-auto">
                <pre className="rounded-md bg-muted p-4 text-sm">
                  codigo,descricao,preco,estoque,marca,modelo,ano 12345678,Filtro de Óleo,89.90,15,Toyota,Corolla,2020
                  87654321,Pastilha de Freio,120.50,8,Honda,Civic,2019
                </pre>
              </div>
              <div className="mt-4">
                <h4 className="mb-2 font-semibold">Requisitos:</h4>
                <ul className="ml-6 list-disc space-y-1">
                  <li>Arquivo com cabeçalho</li>
                  <li>Campos separados por vírgula</li>
                  <li>Codificação UTF-8</li>
                </ul>
              </div>
            </TabsContent>
            <TabsContent value="txt" className="mt-6 rounded-md border p-6">
              <h3 className="mb-4 text-xl font-semibold">Formato TXT</h3>
              <p className="mb-4">Arquivo TXT com campos separados por ponto e vírgula:</p>
              <div className="overflow-x-auto">
                <pre className="rounded-md bg-muted p-4 text-sm">
                  codigo;descricao;preco;estoque;marca;modelo;ano 12345678;Filtro de Óleo;89.90;15;Toyota;Corolla;2020
                  87654321;Pastilha de Freio;120.50;8;Honda;Civic;2019
                </pre>
              </div>
              <div className="mt-4">
                <h4 className="mb-2 font-semibold">Requisitos:</h4>
                <ul className="ml-6 list-disc space-y-1">
                  <li>Arquivo com cabeçalho</li>
                  <li>Campos separados por ponto e vírgula</li>
                  <li>Codificação UTF-8</li>
                </ul>
              </div>
            </TabsContent>
            <TabsContent value="xls" className="mt-6 rounded-md border p-6">
              <h3 className="mb-4 text-xl font-semibold">Formato XLS/XLSX</h3>
              <p className="mb-4">Planilha Excel com as seguintes colunas:</p>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-muted">
                      <th className="border p-2 text-left">codigo</th>
                      <th className="border p-2 text-left">descricao</th>
                      <th className="border p-2 text-left">preco</th>
                      <th className="border p-2 text-left">estoque</th>
                      <th className="border p-2 text-left">marca</th>
                      <th className="border p-2 text-left">modelo</th>
                      <th className="border p-2 text-left">ano</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border p-2">12345678</td>
                      <td className="border p-2">Filtro de Óleo</td>
                      <td className="border p-2">89.90</td>
                      <td className="border p-2">15</td>
                      <td className="border p-2">Toyota</td>
                      <td className="border p-2">Corolla</td>
                      <td className="border p-2">2020</td>
                    </tr>
                    <tr>
                      <td className="border p-2">87654321</td>
                      <td className="border p-2">Pastilha de Freio</td>
                      <td className="border p-2">120.50</td>
                      <td className="border p-2">8</td>
                      <td className="border p-2">Honda</td>
                      <td className="border p-2">Civic</td>
                      <td className="border p-2">2019</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div className="mt-4">
                <h4 className="mb-2 font-semibold">Requisitos:</h4>
                <ul className="ml-6 list-disc space-y-1">
                  <li>Primeira linha como cabeçalho</li>
                  <li>Formatos .xls ou .xlsx</li>
                  <li>Máximo de 10.000 linhas por arquivo</li>
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Plans Section */}
      <section className="bg-secondary/10 py-16">
        <div className="container mx-auto px-4">
          <h2 className="mb-8 text-center text-3xl font-bold tracking-tight md:text-4xl">
            Planos para concessionárias
          </h2>
          <p className="mx-auto mb-12 max-w-3xl text-center text-lg text-muted-foreground">
            Escolha o plano ideal para o tamanho e necessidades da sua concessionária
          </p>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>Básico</CardTitle>
                <CardDescription>Para concessionárias de pequeno porte</CardDescription>
                <div className="mt-4 text-3xl font-bold">
                  R$ 299<span className="text-lg font-normal text-muted-foreground">/mês</span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Até 1.000 peças no catálogo</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Importação mensal de estoque</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Perfil básico da concessionária</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Suporte por e-mail</span>
                  </li>
                </ul>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/cadastro-concessionaria?plano=basico">
                  <Button className="w-full">Selecionar plano</Button>
                </Link>
              </div>
            </Card>

            <Card className="flex flex-col border-primary">
              <CardHeader>
                <div className="mb-2 w-fit rounded-full bg-primary px-3 py-1 text-xs font-semibold text-primary-foreground">
                  MAIS POPULAR
                </div>
                <CardTitle>Profissional</CardTitle>
                <CardDescription>Para concessionárias de médio porte</CardDescription>
                <div className="mt-4 text-3xl font-bold">
                  R$ 599<span className="text-lg font-normal text-muted-foreground">/mês</span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Até 5.000 peças no catálogo</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Importação semanal de estoque</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Perfil destacado da concessionária</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Criação de até 10 promoções</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Suporte por e-mail e telefone</span>
                  </li>
                </ul>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/cadastro-concessionaria?plano=profissional">
                  <Button className="w-full">Selecionar plano</Button>
                </Link>
              </div>
            </Card>

            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>Premium</CardTitle>
                <CardDescription>Para redes de concessionárias</CardDescription>
                <div className="mt-4 text-3xl font-bold">
                  R$ 999<span className="text-lg font-normal text-muted-foreground">/mês</span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Peças ilimitadas no catálogo</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Importação diária de estoque</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Perfil premium com destaque na home</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Promoções ilimitadas</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Relatórios avançados de desempenho</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    <span>Suporte prioritário 24/7</span>
                  </li>
                </ul>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/cadastro-concessionaria?plano=premium">
                  <Button className="w-full">Selecionar plano</Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold tracking-tight md:text-4xl">
            O que dizem nossos parceiros
          </h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 flex items-center">
                  <div className="mr-4 h-12 w-12 overflow-hidden rounded-full bg-gray-200">
                    <Image
                      src="/placeholder.svg?height=48&width=48"
                      alt="Avatar"
                      width={48}
                      height={48}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold">Carlos Mendes</h4>
                    <p className="text-sm text-muted-foreground">Gerente de Peças - Toyota São Paulo</p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "Desde que começamos a utilizar a plataforma, aumentamos nossas vendas de peças em 30%. O processo de
                  importação do estoque é simples e a interface do painel é muito intuitiva."
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 flex items-center">
                  <div className="mr-4 h-12 w-12 overflow-hidden rounded-full bg-gray-200">
                    <Image
                      src="/placeholder.svg?height=48&width=48"
                      alt="Avatar"
                      width={48}
                      height={48}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold">Ana Oliveira</h4>
                    <p className="text-sm text-muted-foreground">Diretora - Honda Rio de Janeiro</p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "A plataforma nos ajudou a reduzir significativamente nosso estoque parado. Agora conseguimos dar
                  visibilidade às peças que antes ficavam esquecidas no depósito."
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 flex items-center">
                  <div className="mr-4 h-12 w-12 overflow-hidden rounded-full bg-gray-200">
                    <Image
                      src="/placeholder.svg?height=48&width=48"
                      alt="Avatar"
                      width={48}
                      height={48}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold">Roberto Almeida</h4>
                    <p className="text-sm text-muted-foreground">Proprietário - Rede Hyundai Minas Gerais</p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "O retorno sobre o investimento foi rápido. Em apenas dois meses, o aumento nas vendas já compensou o
                  valor do plano anual. Recomendo para todas as concessionárias."
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-secondary/10 py-16">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold tracking-tight md:text-4xl">Perguntas frequentes</h2>
          <div className="mx-auto max-w-3xl space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Quanto tempo leva para meu estoque aparecer na plataforma?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Após a importação do arquivo, seu estoque estará disponível na plataforma em até 24 horas, dependendo
                  do volume de dados e do seu plano.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Como são gerados os leads?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Quando um cliente encontra uma peça em nossa plataforma e tem interesse em adquiri-la, ele clica no
                  botão de WhatsApp para entrar em contato diretamente com sua concessionária.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Posso cancelar meu plano a qualquer momento?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Sim, você pode cancelar seu plano a qualquer momento. Os planos são cobrados mensalmente, sem
                  fidelidade.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Como faço para atualizar meu estoque?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Você pode atualizar seu estoque através do painel administrativo, importando um novo arquivo nos
                  formatos suportados (CSV, TXT ou XLS). A frequência de atualização depende do seu plano.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">É possível integrar com meu sistema de gestão?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Sim, oferecemos APIs para integração com os principais sistemas de gestão de concessionárias. Entre em
                  contato com nosso suporte para mais informações.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="mb-6 text-3xl font-bold tracking-tight md:text-4xl">
            Pronto para aumentar suas vendas de peças?
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-xl text-muted-foreground">
            Junte-se a centenas de concessionárias que já estão aproveitando os benefícios da nossa plataforma.
          </p>
          <Link href="/cadastro-concessionaria">
            <Button size="lg" className="px-8">
              Cadastre sua concessionária agora
            </Button>
          </Link>
        </div>
      </section>
      <Footer />
    </div>
  )
}

