"""
Gunicorn configuration for AutoParts API production deployment.
"""
import os
import multiprocessing

# Server socket
bind = "0.0.0.0:8000"
backlog = 2048

# Worker processes
workers = int(os.environ.get("WORKERS", multiprocessing.cpu_count() * 2 + 1))
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = int(os.environ.get("WORKER_CONNECTIONS", 1000))
max_requests = int(os.environ.get("MAX_REQUESTS", 1000))
max_requests_jitter = int(os.environ.get("MAX_REQUESTS_JITTER", 100))

# Timeout settings
timeout = int(os.environ.get("TIMEOUT", 30))
keepalive = int(os.environ.get("KEEP_ALIVE", 5))
graceful_timeout = 30

# Process naming
proc_name = "autoparts_api"

# User and group
user = "appuser"
group = "appuser"

# Logging
accesslog = "-"  # stdout
errorlog = "-"   # stderr
loglevel = os.environ.get("LOG_LEVEL", "info").lower()
access_log_format = (
    '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s '
    '"%(f)s" "%(a)s" %(D)s'
)

# Process management
preload_app = True
daemon = False
pidfile = "/tmp/gunicorn.pid"
tmp_upload_dir = None

# SSL (if needed)
keyfile = os.environ.get("SSL_KEYFILE")
certfile = os.environ.get("SSL_CERTFILE")

# Worker lifecycle hooks
def on_starting(server):
    """Called just before the master process is initialized."""
    server.log.info("Starting AutoParts API server...")

def on_reload(server):
    """Called to recycle workers during a reload via SIGHUP."""
    server.log.info("Reloading AutoParts API server...")

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("AutoParts API server is ready. Listening on: %s", server.address)

def worker_int(worker):
    """Called just after a worker exited on SIGINT or SIGQUIT."""
    worker.log.info("Worker received INT or QUIT signal")

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_worker_init(worker):
    """Called just after a worker has initialized the application."""
    worker.log.info("Worker initialized (pid: %s)", worker.pid)

def worker_abort(worker):
    """Called when a worker received the SIGABRT signal."""
    worker.log.info("Worker received SIGABRT signal")

def pre_exec(server):
    """Called just before a new master process is forked."""
    server.log.info("Forked child, re-executing.")

def pre_request(worker, req):
    """Called just before a worker processes the request."""
    worker.log.debug("%s %s", req.method, req.path)

def post_request(worker, req, environ, resp):
    """Called after a worker processes the request."""
    pass

def child_exit(server, worker):
    """Called just after a worker has been exited, in the master process."""
    server.log.info("Worker exited (pid: %s)", worker.pid)

def worker_exit(server, worker):
    """Called just after a worker has been exited, in the worker process."""
    worker.log.info("Worker exiting (pid: %s)", worker.pid)

def nworkers_changed(server, new_value, old_value):
    """Called just after num_workers has been changed."""
    server.log.info("Number of workers changed from %s to %s", old_value, new_value)

def on_exit(server):
    """Called just before exiting."""
    server.log.info("Shutting down AutoParts API server...")

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Performance tuning
worker_tmp_dir = "/dev/shm"  # Use memory for worker temp files
forwarded_allow_ips = "*"    # Trust all proxies (behind nginx)
secure_scheme_headers = {
    "X-FORWARDED-PROTOCOL": "ssl",
    "X-FORWARDED-PROTO": "https",
    "X-FORWARDED-SSL": "on"
}

# Environment-specific configurations
environment = os.environ.get("ENVIRONMENT", "production")

if environment == "development":
    # Development settings
    reload = True
    workers = 1
    loglevel = "debug"
    timeout = 0  # No timeout for debugging
elif environment == "staging":
    # Staging settings
    workers = 2
    loglevel = "info"
elif environment == "production":
    # Production settings (use defaults)
    pass

# Custom configuration based on environment variables
if os.environ.get("GUNICORN_RELOAD", "false").lower() == "true":
    reload = True

if os.environ.get("GUNICORN_DEBUG", "false").lower() == "true":
    loglevel = "debug"
