/**
 * JWT Service Unit Tests
 * Tests for JWT token management and authentication utilities
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Cookies from 'js-cookie';
import { JWTService } from '../../lib/auth/jwt-service';

// Mock js-cookie
vi.mock('js-cookie');
const mockCookies = vi.mocked(Cookies);

// Mock fetch for token refresh
global.fetch = vi.fn();
const mockFetch = vi.mocked(fetch);

// Mock JWT tokens
const mockAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
const mockRefreshToken = 'refresh_token_123';

describe('JWTService', () => {
  let jwtService: JWTService;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset localStorage mock
    vi.mocked(window.localStorage.getItem).mockReturnValue(null);
    vi.mocked(window.localStorage.setItem).mockImplementation(() => {});
    vi.mocked(window.localStorage.removeItem).mockImplementation(() => {});

    // Create new instance for each test
    jwtService = new JWTService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Token Storage', () => {
    it('should set tokens in cookies with correct options', () => {
      const accessToken = mockAccessToken;
      const refreshToken = mockRefreshToken;

      jwtService.setTokens(accessToken, refreshToken);

      expect(mockCookies.set).toHaveBeenCalledWith(
        'autoparts_access_token',
        accessToken,
        expect.objectContaining({
          expires: 1/24,
          secure: false, // NODE_ENV is test
          sameSite: 'strict',
          httpOnly: false,
        })
      );

      expect(mockCookies.set).toHaveBeenCalledWith(
        'autoparts_refresh_token',
        refreshToken,
        expect.objectContaining({
          expires: 7,
          secure: false,
          sameSite: 'strict',
          httpOnly: false,
        })
      );
    });

    it('should clear tokens from cookies', () => {
      jwtService.clearTokens();

      expect(mockCookies.remove).toHaveBeenCalledWith('autoparts_access_token');
      expect(mockCookies.remove).toHaveBeenCalledWith('autoparts_refresh_token');
    });

    it('should load tokens from cookies on initialization', () => {
      mockCookies.get.mockImplementation((key) => {
        if (key === 'autoparts_access_token') return mockAccessToken;
        if (key === 'autoparts_refresh_token') return mockRefreshToken;
        return undefined;
      });

      const service = new JWTService();

      expect(service.getAccessToken()).toBe(mockAccessToken);
      expect(service.getRefreshToken()).toBe(mockRefreshToken);
    });
  });

  describe('Authentication Status', () => {
    it('should return false for isAuthenticated when no tokens', () => {
      expect(jwtService.isAuthenticated()).toBe(false);
    });

    it('should return true for isAuthenticated when valid tokens exist', () => {
      jwtService.setTokens(mockAccessToken, mockRefreshToken);
      expect(jwtService.isAuthenticated()).toBe(true);
    });

    it('should return false for isAuthenticated when token is expired', () => {
      // Create expired token (exp: 1516239022 = Jan 18, 2018)
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.4Adcj3UFYzPUVaVF43FmMab6RlaQD8A9V8wFzzht-KQ';
      
      jwtService.setTokens(expiredToken, mockRefreshToken);
      expect(jwtService.isAuthenticated()).toBe(false);
    });
  });

  describe('Token Payload Extraction', () => {
    beforeEach(() => {
      jwtService.setTokens(mockAccessToken, mockRefreshToken);
    });

    it('should extract user ID from token', () => {
      expect(jwtService.getUserId()).toBe('1234567890');
    });

    it('should extract user email from token', () => {
      expect(jwtService.getUserEmail()).toBe('<EMAIL>');
    });

    it('should extract user role from token', () => {
      expect(jwtService.getUserRole()).toBe('dealership');
    });

    it('should get token expiration date', () => {
      const expiration = jwtService.getTokenExpiration();
      expect(expiration).toBeInstanceOf(Date);
      // Token expires at 9999999999 (far in the future)
      expect(expiration?.getTime()).toBe(9999999999 * 1000);
    });

    it('should calculate time until expiry', () => {
      const timeUntilExpiry = jwtService.getTimeUntilExpiry();
      expect(timeUntilExpiry).toBeGreaterThan(0);
    });
  });

  describe('Token Refresh', () => {
    it('should refresh access token successfully', async () => {
      const newAccessToken = 'new_access_token';
      
      jwtService.setTokens(mockAccessToken, mockRefreshToken);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ access_token: newAccessToken }),
      } as Response);

      const result = await jwtService.refreshAccessToken();

      expect(result).toBe(newAccessToken);
      expect(jwtService.getAccessToken()).toBe(newAccessToken);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/auth/refresh',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refresh_token: mockRefreshToken }),
        })
      );
    });

    it('should throw error when no refresh token available', async () => {
      await expect(jwtService.refreshAccessToken()).rejects.toThrow('No refresh token available');
    });

    it('should throw error when refresh request fails', async () => {
      jwtService.setTokens(mockAccessToken, mockRefreshToken);

      mockFetch.mockResolvedValueOnce({
        ok: false,
      } as Response);

      await expect(jwtService.refreshAccessToken()).rejects.toThrow('Token refresh failed');
    });

    it('should detect when token needs refresh', () => {
      // Create token that expires soon (5 minutes from now)
      const soonToExpireTime = Math.floor(Date.now() / 1000) + (5 * 60);
      const soonToExpireToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.${btoa(JSON.stringify({
        sub: '1234567890',
        email: '<EMAIL>',
        role: 'dealership',
        exp: soonToExpireTime,
        iat: Math.floor(Date.now() / 1000),
      }))}.signature`;

      jwtService.setTokens(soonToExpireToken, mockRefreshToken);
      expect(jwtService.shouldRefreshToken()).toBe(true);
    });
  });

  describe('Role-based Access', () => {
    beforeEach(() => {
      jwtService.setTokens(mockAccessToken, mockRefreshToken);
    });

    it('should check if user has specific role', () => {
      expect(jwtService.hasRole('dealership')).toBe(true);
      expect(jwtService.hasRole('admin')).toBe(false);
    });

    it('should check if user is admin', () => {
      expect(jwtService.isAdmin()).toBe(false);
    });

    it('should check if user is dealership', () => {
      expect(jwtService.isDealership()).toBe(true);
    });

    it('should check if user is customer', () => {
      expect(jwtService.isCustomer()).toBe(false);
    });
  });

  describe('Authorization Header', () => {
    it('should return null when no access token', () => {
      expect(jwtService.getAuthorizationHeader()).toBeNull();
    });

    it('should return Bearer token when access token exists', () => {
      jwtService.setTokens(mockAccessToken, mockRefreshToken);
      expect(jwtService.getAuthorizationHeader()).toBe(`Bearer ${mockAccessToken}`);
    });
  });

  describe('Token Validation', () => {
    it('should validate valid token', () => {
      expect(jwtService.validateToken(mockAccessToken)).toBe(true);
    });

    it('should reject invalid token format', () => {
      expect(jwtService.validateToken('invalid_token')).toBe(false);
    });

    it('should reject token with missing required fields', () => {
      const incompleteToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.signature';
      expect(jwtService.validateToken(incompleteToken)).toBe(false);
    });
  });

  describe('Token Scheduling', () => {
    it('should schedule token refresh', () => {
      const setTimeoutSpy = vi.spyOn(global, 'setTimeout');
      
      jwtService.setTokens(mockAccessToken, mockRefreshToken);
      jwtService.scheduleTokenRefresh();

      expect(setTimeoutSpy).toHaveBeenCalled();
    });
  });
});