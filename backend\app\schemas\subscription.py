"""
Pydantic schemas for subscription management.
"""
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime, date
from decimal import Decimal
from uuid import UUID

from app.models.subscription import SubscriptionStatus, PlanType


class SubscriptionBase(BaseModel):
    """Base subscription schema."""
    plan_type: PlanType = Field(default=PlanType.BASIC, description="Subscription plan type")
    value: Decimal = Field(..., gt=0, description="Monthly subscription value")
    billing_type: str = Field(default="BOLETO", description="Payment method")
    cycle: str = Field(default="MONTHLY", description="Billing cycle")
    description: Optional[str] = Field(None, description="Subscription description")
    
    @validator('billing_type')
    def validate_billing_type(cls, v):
        """Validate billing type."""
        valid_types = ["BOLETO", "CREDIT_CARD", "PIX", "DEBIT_CARD"]
        if v not in valid_types:
            raise ValueError(f"Billing type must be one of: {', '.join(valid_types)}")
        return v
    
    @validator('cycle')
    def validate_cycle(cls, v):
        """Validate billing cycle."""
        valid_cycles = ["MONTHLY", "YEARLY"]
        if v not in valid_cycles:
            raise ValueError(f"Cycle must be one of: {', '.join(valid_cycles)}")
        return v


class SubscriptionCreate(SubscriptionBase):
    """Schema for creating a new subscription."""
    dealership_id: UUID = Field(..., description="Dealership UUID")
    next_due_date: Optional[date] = Field(None, description="Next payment due date")


class SubscriptionUpdate(BaseModel):
    """Schema for updating subscription information."""
    plan_type: Optional[PlanType] = None
    value: Optional[Decimal] = Field(None, gt=0)
    billing_type: Optional[str] = None
    cycle: Optional[str] = None
    description: Optional[str] = None
    next_due_date: Optional[date] = None
    discount_value: Optional[Decimal] = Field(None, ge=0)
    interest_value: Optional[Decimal] = Field(None, ge=0)
    
    @validator('billing_type')
    def validate_billing_type(cls, v):
        """Validate billing type."""
        if v is None:
            return v
        valid_types = ["BOLETO", "CREDIT_CARD", "PIX", "DEBIT_CARD"]
        if v not in valid_types:
            raise ValueError(f"Billing type must be one of: {', '.join(valid_types)}")
        return v
    
    @validator('cycle')
    def validate_cycle(cls, v):
        """Validate billing cycle."""
        if v is None:
            return v
        valid_cycles = ["MONTHLY", "YEARLY"]
        if v not in valid_cycles:
            raise ValueError(f"Cycle must be one of: {', '.join(valid_cycles)}")
        return v


class SubscriptionInDB(SubscriptionBase):
    """Schema for subscription in database."""
    id: UUID
    dealership_id: UUID
    asaas_subscription_id: str
    asaas_customer_id: str
    status: SubscriptionStatus
    next_due_date: Optional[date] = None
    discount_value: Optional[Decimal] = None
    interest_value: Optional[Decimal] = None
    external_reference: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Subscription(SubscriptionInDB):
    """Public subscription schema."""
    pass


class SubscriptionSummary(BaseModel):
    """Summary schema for subscription listings."""
    id: UUID
    dealership_id: UUID
    plan_type: PlanType
    status: SubscriptionStatus
    value: Decimal
    next_due_date: Optional[date] = None
    is_active: bool
    
    class Config:
        from_attributes = True


class SubscriptionListResponse(BaseModel):
    """Response schema for subscription listing."""
    subscriptions: List[SubscriptionSummary]
    total: int
    page: int
    per_page: int
    pages: int


class SubscriptionStats(BaseModel):
    """Schema for subscription statistics."""
    total_subscriptions: int
    active_subscriptions: int
    overdue_subscriptions: int
    cancelled_subscriptions: int
    monthly_revenue: Decimal
    subscriptions_by_plan: List[dict]


class AsaasCustomerCreate(BaseModel):
    """Schema for creating Asaas customer."""
    name: str = Field(..., min_length=1, max_length=255)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    cpf_cnpj: str = Field(..., min_length=11, max_length=18)
    phone: Optional[str] = Field(None, max_length=20)
    address: Optional[str] = Field(None, max_length=500)
    address_number: Optional[str] = Field(None, max_length=10)
    complement: Optional[str] = Field(None, max_length=100)
    province: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=10)


class AsaasSubscriptionCreate(BaseModel):
    """Schema for creating Asaas subscription."""
    customer_id: str = Field(..., description="Asaas customer ID")
    billing_type: str = Field(..., description="Payment method")
    value: Decimal = Field(..., gt=0, description="Subscription value")
    next_due_date: date = Field(..., description="Next payment due date")
    cycle: str = Field(default="MONTHLY", description="Billing cycle")
    description: Optional[str] = Field(None, description="Subscription description")
    external_reference: Optional[str] = Field(None, description="External reference")


class AsaasWebhookPayload(BaseModel):
    """Schema for Asaas webhook payload."""
    event: str = Field(..., description="Webhook event type")
    payment: Optional[dict] = Field(None, description="Payment data")
    subscription: Optional[dict] = Field(None, description="Subscription data")
    customer: Optional[dict] = Field(None, description="Customer data")


class PaymentWebhookData(BaseModel):
    """Schema for payment webhook data."""
    id: str
    status: str
    billing_type: str
    value: Decimal
    net_value: Optional[Decimal] = None
    due_date: date
    payment_date: Optional[date] = None
    customer: str
    subscription: Optional[str] = None
    external_reference: Optional[str] = None


class SubscriptionWebhookData(BaseModel):
    """Schema for subscription webhook data."""
    id: str
    status: str
    customer: str
    billing_type: str
    value: Decimal
    next_due_date: date
    cycle: str
    description: Optional[str] = None
    external_reference: Optional[str] = None


class WebhookResponse(BaseModel):
    """Schema for webhook response."""
    success: bool
    message: str
    processed_at: datetime


class SubscriptionStatusUpdate(BaseModel):
    """Schema for subscription status update."""
    status: SubscriptionStatus
    reason: Optional[str] = None


class BillingInfo(BaseModel):
    """Schema for billing information."""
    subscription_id: UUID
    current_period_start: date
    current_period_end: date
    next_billing_date: date
    amount_due: Decimal
    status: str
    payment_method: str


class PlanInfo(BaseModel):
    """Schema for plan information."""
    plan_type: PlanType
    name: str
    description: str
    price: Decimal
    features: List[str]
    max_inventory_items: Optional[int] = None
    max_users: Optional[int] = None
