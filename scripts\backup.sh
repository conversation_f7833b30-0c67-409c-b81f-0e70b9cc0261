#!/bin/bash

# AutoParts API Backup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Configuration
BACKUP_DIR="./database/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPOSE_FILE="docker-compose.yml"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# Load environment variables
if [ -f ".env" ]; then
    set -a
    source ".env"
    set +a
fi

# Database configuration
DB_CONTAINER=${DB_CONTAINER:-autoparts_postgres}
DB_NAME=${POSTGRES_DB:-autoparts}
DB_USER=${POSTGRES_USER:-autoparts}
DB_PASSWORD=${POSTGRES_PASSWORD:-autoparts123}

# Backup files
DB_BACKUP_FILE="$BACKUP_DIR/autoparts_db_$TIMESTAMP.sql"
UPLOADS_BACKUP_FILE="$BACKUP_DIR/autoparts_uploads_$TIMESTAMP.tar.gz"
FULL_BACKUP_FILE="$BACKUP_DIR/autoparts_full_$TIMESTAMP.tar.gz"

# Create backup directory
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log "Created backup directory: $BACKUP_DIR"
    fi
}

# Backup database
backup_database() {
    log "Starting database backup..."
    
    # Check if container is running
    if ! docker ps | grep -q "$DB_CONTAINER"; then
        error "Database container $DB_CONTAINER is not running!"
        return 1
    fi
    
    # Create database dump
    docker exec "$DB_CONTAINER" pg_dump \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --no-password \
        --verbose \
        --clean \
        --if-exists \
        --create \
        --format=plain > "$DB_BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        # Compress the backup
        gzip "$DB_BACKUP_FILE"
        DB_BACKUP_FILE="${DB_BACKUP_FILE}.gz"
        
        local size=$(du -h "$DB_BACKUP_FILE" | cut -f1)
        success "Database backup completed: $DB_BACKUP_FILE ($size)"
        return 0
    else
        error "Database backup failed!"
        return 1
    fi
}

# Backup uploaded files
backup_uploads() {
    log "Starting uploads backup..."
    
    local uploads_dir="./uploads"
    
    if [ ! -d "$uploads_dir" ]; then
        warning "Uploads directory not found: $uploads_dir"
        return 0
    fi
    
    # Check if uploads directory has content
    if [ -z "$(ls -A $uploads_dir)" ]; then
        warning "Uploads directory is empty"
        return 0
    fi
    
    # Create tar.gz archive of uploads
    tar -czf "$UPLOADS_BACKUP_FILE" -C "$(dirname $uploads_dir)" "$(basename $uploads_dir)"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$UPLOADS_BACKUP_FILE" | cut -f1)
        success "Uploads backup completed: $UPLOADS_BACKUP_FILE ($size)"
        return 0
    else
        error "Uploads backup failed!"
        return 1
    fi
}

# Create full backup (database + uploads + config)
create_full_backup() {
    log "Creating full backup archive..."
    
    local temp_dir="/tmp/autoparts_backup_$TIMESTAMP"
    mkdir -p "$temp_dir"
    
    # Copy database backup
    if [ -f "$DB_BACKUP_FILE" ]; then
        cp "$DB_BACKUP_FILE" "$temp_dir/"
    fi
    
    # Copy uploads backup
    if [ -f "$UPLOADS_BACKUP_FILE" ]; then
        cp "$UPLOADS_BACKUP_FILE" "$temp_dir/"
    fi
    
    # Copy configuration files
    local config_files=(
        ".env"
        "docker-compose.yml"
        "nginx/nginx.conf"
        "nginx/conf.d/autoparts.conf"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            local dir_path=$(dirname "$file")
            mkdir -p "$temp_dir/$dir_path"
            cp "$file" "$temp_dir/$file"
        fi
    done
    
    # Create backup info file
    cat > "$temp_dir/backup_info.txt" << EOF
AutoParts API Backup Information
================================
Backup Date: $(date)
Backup Type: Full Backup
Database: $DB_NAME
Environment: ${ENVIRONMENT:-unknown}
Version: $(git rev-parse HEAD 2>/dev/null || echo "unknown")

Files Included:
- Database dump (PostgreSQL)
- Uploaded files
- Configuration files
- This info file

Restore Instructions:
1. Extract this archive
2. Restore database: gunzip -c autoparts_db_*.sql.gz | docker exec -i <db_container> psql -U $DB_USER -d $DB_NAME
3. Restore uploads: tar -xzf autoparts_uploads_*.tar.gz
4. Restore configuration files as needed
EOF
    
    # Create full backup archive
    tar -czf "$FULL_BACKUP_FILE" -C "/tmp" "autoparts_backup_$TIMESTAMP"
    
    # Cleanup temp directory
    rm -rf "$temp_dir"
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "$FULL_BACKUP_FILE" | cut -f1)
        success "Full backup completed: $FULL_BACKUP_FILE ($size)"
        return 0
    else
        error "Full backup failed!"
        return 1
    fi
}

# Upload to S3 (if configured)
upload_to_s3() {
    if [ -n "$AWS_ACCESS_KEY_ID" ] && [ -n "$BACKUP_S3_BUCKET" ]; then
        log "Uploading backup to S3..."
        
        # Check if AWS CLI is available
        if ! command -v aws &> /dev/null; then
            warning "AWS CLI not found, skipping S3 upload"
            return 0
        fi
        
        # Upload full backup to S3
        aws s3 cp "$FULL_BACKUP_FILE" "s3://$BACKUP_S3_BUCKET/autoparts/" \
            --region "${AWS_REGION:-us-east-1}" \
            --storage-class STANDARD_IA
        
        if [ $? -eq 0 ]; then
            success "Backup uploaded to S3: s3://$BACKUP_S3_BUCKET/autoparts/$(basename $FULL_BACKUP_FILE)"
        else
            warning "Failed to upload backup to S3"
        fi
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (keeping last $RETENTION_DAYS days)..."
    
    # Find and delete old backup files
    find "$BACKUP_DIR" -name "autoparts_*" -type f -mtime +$RETENTION_DAYS -delete
    
    local remaining=$(find "$BACKUP_DIR" -name "autoparts_*" -type f | wc -l)
    success "Cleanup completed. $remaining backup files remaining."
}

# Verify backup integrity
verify_backup() {
    log "Verifying backup integrity..."
    
    # Test database backup
    if [ -f "$DB_BACKUP_FILE" ]; then
        if gunzip -t "$DB_BACKUP_FILE" 2>/dev/null; then
            success "Database backup integrity verified"
        else
            error "Database backup is corrupted!"
            return 1
        fi
    fi
    
    # Test uploads backup
    if [ -f "$UPLOADS_BACKUP_FILE" ]; then
        if tar -tzf "$UPLOADS_BACKUP_FILE" >/dev/null 2>&1; then
            success "Uploads backup integrity verified"
        else
            error "Uploads backup is corrupted!"
            return 1
        fi
    fi
    
    # Test full backup
    if [ -f "$FULL_BACKUP_FILE" ]; then
        if tar -tzf "$FULL_BACKUP_FILE" >/dev/null 2>&1; then
            success "Full backup integrity verified"
        else
            error "Full backup is corrupted!"
            return 1
        fi
    fi
    
    return 0
}

# Show backup summary
show_summary() {
    log "Backup Summary:"
    echo ""
    
    if [ -f "$DB_BACKUP_FILE" ]; then
        local db_size=$(du -h "$DB_BACKUP_FILE" | cut -f1)
        echo "  Database: $DB_BACKUP_FILE ($db_size)"
    fi
    
    if [ -f "$UPLOADS_BACKUP_FILE" ]; then
        local uploads_size=$(du -h "$UPLOADS_BACKUP_FILE" | cut -f1)
        echo "  Uploads: $UPLOADS_BACKUP_FILE ($uploads_size)"
    fi
    
    if [ -f "$FULL_BACKUP_FILE" ]; then
        local full_size=$(du -h "$FULL_BACKUP_FILE" | cut -f1)
        echo "  Full Backup: $FULL_BACKUP_FILE ($full_size)"
    fi
    
    echo ""
    success "Backup process completed successfully!"
}

# Main backup function
main() {
    log "AutoParts API Backup Script"
    log "==========================="
    log "Starting backup process..."
    
    create_backup_dir
    
    # Perform backups
    backup_database
    backup_uploads
    create_full_backup
    
    # Verify backups
    verify_backup
    
    # Upload to cloud (if configured)
    upload_to_s3
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Show summary
    show_summary
}

# Handle script arguments
case "${1:-backup}" in
    "backup")
        main
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "verify")
        verify_backup
        ;;
    *)
        echo "Usage: $0 [backup|cleanup|verify]"
        echo "  backup  - Create full backup (default)"
        echo "  cleanup - Clean old backups"
        echo "  verify  - Verify backup integrity"
        exit 1
        ;;
esac
