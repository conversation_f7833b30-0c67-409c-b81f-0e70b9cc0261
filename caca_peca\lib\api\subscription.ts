import { apiClient } from './client'
import {
  ApiResponse,
  PaginatedResponse,
  Subscription,
  SubscriptionCreate,
  SubscriptionUpdate,
  BillingHistory,
  SubscriptionPlanInfo
} from '@/lib/types/api'

// Fixed import issue - using apiClient as api

export const subscriptionApi = {
  // Get current subscription
  getCurrentSubscription: async (): Promise<ApiResponse<Subscription | null>> => {
    const response = await apiClient.get('/subscriptions/current')
    return response.data
  },

  // Create new subscription
  createSubscription: async (data: SubscriptionCreate): Promise<ApiResponse<Subscription>> => {
    const response = await apiClient.post('/subscriptions', data)
    return response.data
  },

  // Update subscription
  updateSubscription: async (id: string, data: SubscriptionUpdate): Promise<ApiResponse<Subscription>> => {
    const response = await apiClient.put(`/subscriptions/${id}`, data)
    return response.data
  },

  // Cancel subscription
  cancelSubscription: async (id: string): Promise<ApiResponse<{ message: string }>> => {
    const response = await apiClient.delete(`/subscriptions/${id}`)
    return response.data
  },

  // Reactivate subscription
  reactivateSubscription: async (id: string): Promise<ApiResponse<Subscription>> => {
    const response = await apiClient.post(`/subscriptions/${id}/reactivate`)
    return response.data
  },

  // Get billing history
  getBillingHistory: async (
    page: number = 1, 
    size: number = 20,
    status?: string,
    start_date?: string,
    end_date?: string
  ): Promise<ApiResponse<PaginatedResponse<BillingHistory>>> => {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString()
    })
    
    if (status) params.append('status', status)
    if (start_date) params.append('start_date', start_date)
    if (end_date) params.append('end_date', end_date)

    const response = await apiClient.get(`/subscriptions/billing-history?${params}`)
    return response.data
  },

  // Get specific invoice
  getInvoice: async (invoiceId: string): Promise<ApiResponse<BillingHistory>> => {
    const response = await apiClient.get(`/subscriptions/invoices/${invoiceId}`)
    return response.data
  },

  // Download invoice PDF
  downloadInvoice: async (invoiceId: string): Promise<Blob> => {
    const response = await apiClient.get(`/subscriptions/invoices/${invoiceId}/pdf`, {
      responseType: 'blob'
    })
    return response.data
  },

  // Get available subscription plans
  getSubscriptionPlans: async (): Promise<ApiResponse<SubscriptionPlanInfo[]>> => {
    const response = await apiClient.get('/subscriptions/plans')
    return response.data
  },

  // Update payment method
  updatePaymentMethod: async (
    subscriptionId: string,
    billingType: string
  ): Promise<ApiResponse<Subscription>> => {
    const response = await apiClient.put(`/subscriptions/${subscriptionId}/payment-method`, {
      billing_type: billingType
    })
    return response.data
  },

  // Get subscription usage/metrics
  getSubscriptionUsage: async (): Promise<ApiResponse<{
    current_period_start: string;
    current_period_end: string;
    inventory_items_count: number;
    inventory_items_limit: number;
    api_calls_count: number;
    api_calls_limit: number;
    storage_used_mb: number;
    storage_limit_mb: number;
  }>> => {
    const response = await apiClient.get('/subscriptions/usage')
    return response.data
  },

  // Preview plan change
  previewPlanChange: async (
    currentPlanType: string,
    newPlanType: string,
    cycle: string
  ): Promise<ApiResponse<{
    current_plan: SubscriptionPlanInfo;
    new_plan: SubscriptionPlanInfo;
    price_difference: number;
    proration_amount: number;
    next_billing_date: string;
    immediate_charge: number;
  }>> => {
    const response = await apiClient.post('/subscriptions/preview-plan-change', {
      current_plan_type: currentPlanType,
      new_plan_type: newPlanType,
      cycle
    })
    return response.data
  },

  // Apply discount coupon
  applyCoupon: async (
    subscriptionId: string,
    couponCode: string
  ): Promise<ApiResponse<{
    discount_applied: number;
    new_value: number;
    coupon_description: string;
  }>> => {
    const response = await apiClient.post(`/subscriptions/${subscriptionId}/apply-coupon`, {
      coupon_code: couponCode
    })
    return response.data
  },

  // Get subscription analytics
  getSubscriptionAnalytics: async (
    period: 'month' | 'quarter' | 'year' = 'month'
  ): Promise<ApiResponse<{
    total_revenue: number;
    payment_success_rate: number;
    churn_rate: number;
    average_revenue_per_user: number;
    payment_methods_distribution: Record<string, number>;
    revenue_by_month: Array<{
      month: string;
      revenue: number;
      payments_count: number;
    }>;
  }>> => {
    const response = await apiClient.get(`/subscriptions/analytics?period=${period}`)
    return response.data
  },

  // Retry failed payment
  retryPayment: async (paymentId: string): Promise<ApiResponse<BillingHistory>> => {
    const response = await apiClient.post(`/subscriptions/payments/${paymentId}/retry`)
    return response.data
  },

  // Get payment methods
  getPaymentMethods: async (): Promise<ApiResponse<Array<{
    type: string;
    name: string;
    description: string;
    enabled: boolean;
    processing_fee: number;
  }>>> => {
    const response = await apiClient.get('/subscriptions/payment-methods')
    return response.data
  },

  // Sync subscription with Asaas
  syncSubscription: async (subscriptionId: string): Promise<ApiResponse<Subscription>> => {
    const response = await apiClient.post(`/subscriptions/${subscriptionId}/sync`)
    return response.data
  },

  // Get subscription notifications/alerts
  getSubscriptionNotifications: async (): Promise<ApiResponse<Array<{
    id: string;
    type: 'payment_due' | 'payment_failed' | 'subscription_expired' | 'plan_limit_reached';
    title: string;
    message: string;
    severity: 'info' | 'warning' | 'error';
    created_at: string;
    read: boolean;
  }>>> => {
    const response = await apiClient.get('/subscriptions/notifications')
    return response.data
  },

  // Mark notification as read
  markNotificationAsRead: async (notificationId: string): Promise<ApiResponse<{ success: boolean }>> => {
    const response = await apiClient.put(`/subscriptions/notifications/${notificationId}/read`)
    return response.data
  }
}
