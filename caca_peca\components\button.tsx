"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  icon?: React.ReactNode
  phone?: string
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, icon, phone, children, onClick, ...props }, ref) => {
    // If asChild is true, use Slot to render the children as the root element
    if (asChild) {
      return (
        <Slot className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props}>
          {children}
        </Slot>
      )
    }

    // If phone is present, render a button with phone number
    if (phone) {
      return (
        <button
          className={cn(buttonVariants({ variant, size, className }))}
          ref={ref}
          {...props}
          onClick={(e) => {
            // Prevent default behavior for buttons with phone
            e.preventDefault()
            // Still call the original onClick if it exists
            if (onClick) onClick(e)
          }}
        >
          {icon && <span className="mr-2">{icon}</span>}
          <span className="flex items-center">
            {children}
            <span className="ml-2 text-xs opacity-80 cursor-default">{phone}</span>
          </span>
        </button>
      )
    }

    // Otherwise, render a regular button
    return (
      <button className={cn(buttonVariants({ variant, size, className }))} ref={ref} onClick={onClick} {...props}>
        {icon && <span className="mr-2">{icon}</span>}
        {children}
      </button>
    )
  },
)
Button.displayName = "Button"

export { Button, buttonVariants }

