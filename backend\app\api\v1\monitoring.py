"""
Monitoring and health check endpoints for the AutoParts API.
"""
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.monitoring import health_checker, metrics_collector, alert_manager
from app.api.deps import get_current_admin_user, get_current_active_user
from app.models.user import User
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("app.api.monitoring")


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Comprehensive health check endpoint.
    
    Returns:
        Health status of all system components
    """
    try:
        # Get health status for all components
        health_statuses = await health_checker.check_all()
        
        # Determine overall status
        overall_status = "healthy"
        unhealthy_services = []
        degraded_services = []
        
        for service_name, health_status in health_statuses.items():
            if health_status.status == "unhealthy":
                unhealthy_services.append(service_name)
                overall_status = "unhealthy"
            elif health_status.status == "degraded":
                degraded_services.append(service_name)
                if overall_status == "healthy":
                    overall_status = "degraded"
        
        # Convert health statuses to dict format
        services = {
            name: status.to_dict()
            for name, status in health_statuses.items()
        }
        
        return {
            "status": overall_status,
            "timestamp": health_statuses[list(health_statuses.keys())[0]].timestamp.isoformat(),
            "services": services,
            "summary": {
                "total_services": len(health_statuses),
                "healthy_services": len([s for s in health_statuses.values() if s.status == "healthy"]),
                "degraded_services": len(degraded_services),
                "unhealthy_services": len(unhealthy_services),
                "degraded_service_names": degraded_services,
                "unhealthy_service_names": unhealthy_services
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": None,
            "services": {},
            "summary": {
                "total_services": 0,
                "healthy_services": 0,
                "degraded_services": 0,
                "unhealthy_services": 0,
                "degraded_service_names": [],
                "unhealthy_service_names": []
            }
        }


@router.get("/health/{service}")
async def health_check_service(service: str) -> Dict[str, Any]:
    """
    Health check for a specific service.
    
    Args:
        service: Service name to check
        
    Returns:
        Health status of the specified service
        
    Raises:
        HTTPException: If service not found
    """
    try:
        health_statuses = await health_checker.check_all()
        
        if service not in health_statuses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service '{service}' not found"
            )
        
        return health_statuses[service].to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check for service {service} failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )


@router.get("/metrics")
async def get_current_metrics(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get current system metrics (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Current system metrics
    """
    try:
        metrics = metrics_collector.collect_system_metrics()
        return metrics.to_dict()
        
    except Exception as e:
        logger.error(f"Failed to collect metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to collect metrics: {str(e)}"
        )


@router.get("/metrics/summary")
async def get_metrics_summary(
    minutes: int = Query(60, ge=1, le=1440, description="Time period in minutes"),
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get metrics summary for a time period (admin only).
    
    Args:
        minutes: Time period in minutes (1-1440)
        current_user: Current admin user
        
    Returns:
        Metrics summary for the specified time period
    """
    try:
        summary = metrics_collector.get_metrics_summary(minutes)
        return summary
        
    except Exception as e:
        logger.error(f"Failed to get metrics summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics summary: {str(e)}"
        )


@router.get("/alerts")
async def get_current_alerts(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get current system alerts (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Current system alerts
    """
    try:
        # Collect current metrics
        metrics = metrics_collector.collect_system_metrics()
        
        # Check for alerts
        alerts = alert_manager.check_thresholds(metrics)
        
        return {
            "timestamp": metrics.timestamp.isoformat(),
            "alert_count": len(alerts),
            "alerts": alerts,
            "thresholds": alert_manager.alert_thresholds
        }
        
    except Exception as e:
        logger.error(f"Failed to get alerts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get alerts: {str(e)}"
        )


@router.get("/status")
async def get_system_status(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get overall system status summary.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        System status summary
    """
    try:
        # Get health status
        health_statuses = await health_checker.check_all()
        
        # Get current metrics
        metrics = metrics_collector.collect_system_metrics()
        
        # Get alerts
        alerts = alert_manager.check_thresholds(metrics)
        
        # Determine overall status
        overall_status = "healthy"
        if any(s.status == "unhealthy" for s in health_statuses.values()):
            overall_status = "unhealthy"
        elif any(s.status == "degraded" for s in health_statuses.values()):
            overall_status = "degraded"
        elif len(alerts) > 0:
            overall_status = "warning"
        
        return {
            "overall_status": overall_status,
            "timestamp": metrics.timestamp.isoformat(),
            "health": {
                "services_healthy": len([s for s in health_statuses.values() if s.status == "healthy"]),
                "services_total": len(health_statuses),
                "database_status": health_statuses.get("database", {}).status if "database" in health_statuses else "unknown",
                "cache_status": health_statuses.get("cache", {}).status if "cache" in health_statuses else "unknown"
            },
            "performance": {
                "cpu_percent": metrics.cpu_percent,
                "memory_percent": metrics.memory_percent,
                "disk_percent": metrics.disk_percent,
                "active_connections": metrics.active_connections
            },
            "alerts": {
                "active_alerts": len(alerts),
                "critical_alerts": len([a for a in alerts if a.get("severity") == "critical"]),
                "warning_alerts": len([a for a in alerts if a.get("severity") == "warning"])
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system status: {str(e)}"
        )


@router.get("/performance")
async def get_performance_metrics(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Get detailed performance metrics (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Detailed performance metrics
    """
    try:
        # Get current metrics
        current_metrics = metrics_collector.collect_system_metrics()
        
        # Get metrics summary for different time periods
        last_hour = metrics_collector.get_metrics_summary(60)
        last_day = metrics_collector.get_metrics_summary(1440)
        
        return {
            "current": current_metrics.to_dict(),
            "last_hour": last_hour,
            "last_24_hours": last_day,
            "recommendations": _generate_performance_recommendations(current_metrics, last_hour)
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get performance metrics: {str(e)}"
        )


@router.post("/alerts/test")
async def test_alerts(
    current_user: User = Depends(get_current_admin_user)
) -> Dict[str, Any]:
    """
    Test alert system (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Test alert results
    """
    try:
        # Create a test alert
        test_alert = {
            "type": "test_alert",
            "severity": "info",
            "message": "This is a test alert to verify the alert system is working",
            "timestamp": metrics_collector.collect_system_metrics().timestamp.isoformat(),
            "test": True
        }
        
        logger.warning("TEST ALERT: Alert system test", **test_alert)
        
        return {
            "success": True,
            "message": "Test alert generated successfully",
            "alert": test_alert
        }
        
    except Exception as e:
        logger.error(f"Failed to test alerts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test alerts: {str(e)}"
        )


def _generate_performance_recommendations(current_metrics, hour_summary) -> List[str]:
    """Generate performance recommendations based on metrics."""
    recommendations = []
    
    # CPU recommendations
    if current_metrics.cpu_percent > 80:
        recommendations.append("High CPU usage detected. Consider scaling up or optimizing CPU-intensive operations.")
    
    # Memory recommendations
    if current_metrics.memory_percent > 80:
        recommendations.append("High memory usage detected. Consider increasing memory or optimizing memory usage.")
    
    # Disk recommendations
    if current_metrics.disk_percent > 85:
        recommendations.append("High disk usage detected. Consider cleaning up old files or increasing disk space.")
    
    # Connection recommendations
    if current_metrics.active_connections > 1000:
        recommendations.append("High number of active connections. Consider implementing connection pooling or rate limiting.")
    
    # Trend-based recommendations
    if hour_summary.get("averages", {}).get("cpu_percent", 0) > 70:
        recommendations.append("Consistently high CPU usage over the last hour. Consider performance optimization.")
    
    if hour_summary.get("averages", {}).get("memory_percent", 0) > 70:
        recommendations.append("Consistently high memory usage over the last hour. Consider memory optimization.")
    
    if not recommendations:
        recommendations.append("System performance is within normal parameters.")
    
    return recommendations
