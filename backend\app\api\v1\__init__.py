"""
API v1 router configuration.
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db, check_db_connection

# Import routers
from .auth import router as auth_router
from .dealerships import router as dealerships_router
from .subscriptions import router as subscriptions_router
from .webhooks import router as webhooks_router
from .parts import router as parts_router
from .inventory import router as inventory_router
from .file_import import router as file_import_router
from .cache import router as cache_router
from .monitoring import router as monitoring_router
from .seo import router as seo_router
# from .admin import router as admin_router

api_router = APIRouter()

# Basic test endpoint
@api_router.get("/")
async def root():
    """Root endpoint for API v1."""
    return {
        "message": "AutoParts API v1",
        "status": "active",
        "endpoints": {
            "health": "/health",
            "docs": "/api/v1/docs",
            "redoc": "/api/v1/redoc",
            "database": "/api/v1/database/status",
            "auth": "/api/v1/auth",
            "dealerships": "/api/v1/dealerships",
            "subscriptions": "/api/v1/subscriptions",
            "webhooks": "/api/v1/webhooks",
            "parts": "/api/v1/parts",
            "inventory": "/api/v1/inventory",
            "imports": "/api/v1/imports",
            "cache": "/api/v1/cache",
            "monitoring": "/api/v1/monitoring",
            "seo": "/api/v1/seo",
        }
    }


@api_router.get("/database/status")
async def database_status():
    """Database connectivity and status endpoint."""
    try:
        # Test database connection
        db_status = check_db_connection()

        return {
            "status": "connected" if db_status else "disconnected",
            "message": "Database connection test completed",
            "statistics": "Available after migrations",
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Database connection failed: {str(e)}",
            "statistics": None,
        }

# Include routers
api_router.include_router(auth_router, prefix="/auth", tags=["authentication"])
api_router.include_router(dealerships_router, prefix="/dealerships", tags=["dealerships"])
api_router.include_router(subscriptions_router, prefix="/subscriptions", tags=["subscriptions"])
api_router.include_router(webhooks_router, prefix="/webhooks", tags=["webhooks"])
api_router.include_router(parts_router, prefix="/parts", tags=["parts"])
api_router.include_router(inventory_router, prefix="/inventory", tags=["inventory"])
api_router.include_router(file_import_router, prefix="/imports", tags=["file-imports"])
api_router.include_router(cache_router, prefix="/cache", tags=["cache"])
api_router.include_router(monitoring_router, prefix="/monitoring", tags=["monitoring"])
api_router.include_router(seo_router, prefix="/seo", tags=["seo"])
# api_router.include_router(admin_router, prefix="/admin", tags=["admin"])
