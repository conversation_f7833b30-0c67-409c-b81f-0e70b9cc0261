"""
Part model for AutoParts API.
"""
from sqlalchemy import Column, String, Integer, Text, Index
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Part(BaseModel):
    """
    Part model representing automotive parts.
    
    Attributes:
        code: Original part code/number
        name: Part name
        description: Detailed part description
        category: Part category (e.g., engine, transmission)
        brand: Vehicle brand (e.g., Toyota, Honda)
        model: Vehicle model
        year: Vehicle year
        oem_code: Original Equipment Manufacturer code
        alternative_codes: Alternative part codes (JSON array)
    """
    __tablename__ = "parts"

    # Part Identification
    code = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Original part code/number"
    )
    
    name = Column(
        String(255),
        nullable=False,
        doc="Part name"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Detailed part description"
    )

    # Categorization
    category = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Part category (e.g., engine, transmission, brake)"
    )
    
    subcategory = Column(
        String(100),
        nullable=True,
        doc="Part subcategory for more specific classification"
    )

    # Vehicle Information
    brand = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Vehicle brand (e.g., Toyota, Honda, Volkswagen)"
    )
    
    model = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Vehicle model"
    )
    
    year = Column(
        Integer,
        nullable=True,
        index=True,
        doc="Vehicle year"
    )
    
    year_start = Column(
        Integer,
        nullable=True,
        doc="Starting year for part compatibility"
    )
    
    year_end = Column(
        Integer,
        nullable=True,
        doc="Ending year for part compatibility"
    )

    # Additional Codes
    oem_code = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Original Equipment Manufacturer code"
    )
    
    alternative_codes = Column(
        Text,
        nullable=True,
        doc="Alternative part codes (comma-separated)"
    )

    # Technical Specifications
    weight = Column(
        String(20),
        nullable=True,
        doc="Part weight"
    )
    
    dimensions = Column(
        String(100),
        nullable=True,
        doc="Part dimensions (LxWxH)"
    )

    # Relationships
    inventories = relationship(
        "Inventory",
        back_populates="part",
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Part(id={self.id}, code='{self.code}', name='{self.name}')>"

    @property
    def full_name(self):
        """Return full part name with brand and model."""
        parts = [self.name]
        if self.brand:
            parts.append(self.brand)
        if self.model:
            parts.append(self.model)
        if self.year:
            parts.append(str(self.year))
        return " - ".join(parts)


# Create indexes for performance optimization
Index('idx_part_code', Part.code)
Index('idx_part_name', Part.name)
Index('idx_part_brand_model', Part.brand, Part.model)
Index('idx_part_category', Part.category)
Index('idx_part_year', Part.year)
Index('idx_part_oem_code', Part.oem_code)

# Full-text search index (PostgreSQL specific)
# This will be created via migration script
# CREATE INDEX idx_parts_search ON parts USING gin(to_tsvector('portuguese', name || ' ' || COALESCE(description, '')));
