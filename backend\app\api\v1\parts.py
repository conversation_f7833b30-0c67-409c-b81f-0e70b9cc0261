"""
Parts catalog endpoints for the AutoParts API.
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.part import PartService
from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
    optional_authentication
)
from app.schemas.part import (
    PartCreate,
    PartUpdate,
    Part,
    PartSummary,
    PartListResponse,
    PartSearchFilters,
    PartSearchResponse,
    PartStats,
    BulkPartCreate,
    BulkPartResponse,
    PartWithInventory
)
from app.models.user import User

router = APIRouter()


def get_part_service(db: Session = Depends(get_db)) -> PartService:
    """Get part service instance."""
    return PartService(db)


@router.post("/", response_model=Part)
async def create_part(
    part_data: PartCreate,
    current_user: User = Depends(get_current_admin_user),
    part_service: PartService = Depends(get_part_service)
):
    """
    Create a new part (admin only).
    
    Args:
        part_data: Part creation data
        current_user: Current admin user
        part_service: Part service instance
        
    Returns:
        Created part
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        part = part_service.create_part(part_data)
        return Part.from_orm(part)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/bulk", response_model=BulkPartResponse)
async def bulk_create_parts(
    bulk_data: BulkPartCreate,
    current_user: User = Depends(get_current_admin_user),
    part_service: PartService = Depends(get_part_service)
):
    """
    Create multiple parts in bulk (admin only).
    
    Args:
        bulk_data: Bulk creation data
        current_user: Current admin user
        part_service: Part service instance
        
    Returns:
        Bulk operation response
    """
    return part_service.bulk_create_parts(bulk_data)


@router.get("/search", response_model=PartSearchResponse)
async def search_parts(
    search: Optional[str] = Query(None, description="Search term for code, name, or description"),
    code: Optional[str] = Query(None, description="Exact part code"),
    brand: Optional[str] = Query(None, description="Filter by brand"),
    model: Optional[str] = Query(None, description="Filter by model"),
    year: Optional[int] = Query(None, ge=1900, le=2030, description="Filter by year"),
    year_start: Optional[int] = Query(None, ge=1900, le=2030, description="Year range start"),
    year_end: Optional[int] = Query(None, ge=1900, le=2030, description="Year range end"),
    category: Optional[str] = Query(None, description="Filter by category"),
    subcategory: Optional[str] = Query(None, description="Filter by subcategory"),
    has_inventory: bool = Query(False, description="Only show parts with available inventory"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("name", description="Sort field"),
    sort_order: str = Query("asc", description="Sort order (asc/desc)"),
    current_user: Optional[User] = Depends(optional_authentication),
    part_service: PartService = Depends(get_part_service)
):
    """
    Search parts with advanced filtering.
    
    This endpoint supports both authenticated and anonymous access.
    Authenticated users may get additional information.
    
    Args:
        search: General search term
        code: Exact part code
        brand: Vehicle brand filter
        model: Vehicle model filter
        year: Specific year filter
        year_start: Year range start
        year_end: Year range end
        category: Category filter
        subcategory: Subcategory filter
        has_inventory: Only parts with inventory
        page: Page number
        per_page: Items per page
        sort_by: Sort field
        sort_order: Sort order
        current_user: Optional authenticated user
        part_service: Part service instance
        
    Returns:
        Search results with parts and suggestions
    """
    filters = PartSearchFilters(
        search=search,
        code=code,
        brand=brand,
        model=model,
        year=year,
        year_start=year_start,
        year_end=year_end,
        category=category,
        subcategory=subcategory,
        has_inventory=has_inventory,
        page=page,
        per_page=per_page,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    return part_service.search_parts(filters)


@router.get("/stats", response_model=PartStats)
async def get_part_stats(
    current_user: User = Depends(get_current_admin_user),
    part_service: PartService = Depends(get_part_service)
):
    """
    Get part statistics (admin only).
    
    Args:
        current_user: Current admin user
        part_service: Part service instance
        
    Returns:
        Part statistics
    """
    return part_service.get_part_stats()


@router.get("/code/{code}", response_model=PartWithInventory)
async def get_part_by_code(
    code: str,
    current_user: Optional[User] = Depends(optional_authentication),
    part_service: PartService = Depends(get_part_service)
):
    """
    Get part by code.
    
    Args:
        code: Part code
        current_user: Optional authenticated user
        part_service: Part service instance
        
    Returns:
        Part details with inventory information
        
    Raises:
        HTTPException: If part not found
    """
    part = part_service.get_part_by_code(code)
    
    if not part:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Part not found"
        )
    
    # Get inventory stats for this part
    inventory_stats = part_service.part_repo.get_parts_with_inventory_stats(skip=0, limit=1)
    part_stats = next(
        (stats for stats in inventory_stats if stats['part'].id == part.id),
        {
            'inventory_count': 0,
            'min_price': None,
            'max_price': None,
            'total_quantity': 0,
            'dealership_count': 0
        }
    )
    
    return PartWithInventory(
        **part.__dict__,
        inventory_count=part_stats['inventory_count'],
        min_price=part_stats['min_price'],
        max_price=part_stats['max_price'],
        total_quantity=part_stats['total_quantity'],
        dealership_count=part_stats['dealership_count']
    )


@router.get("/{part_id}", response_model=Part)
async def get_part(
    part_id: UUID,
    current_user: Optional[User] = Depends(optional_authentication),
    part_service: PartService = Depends(get_part_service)
):
    """
    Get part by ID.
    
    Args:
        part_id: Part UUID
        current_user: Optional authenticated user
        part_service: Part service instance
        
    Returns:
        Part details
        
    Raises:
        HTTPException: If part not found
    """
    part = part_service.get_part_by_id(part_id)
    
    if not part:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Part not found"
        )
    
    return Part.from_orm(part)


@router.put("/{part_id}", response_model=Part)
async def update_part(
    part_id: UUID,
    update_data: PartUpdate,
    current_user: User = Depends(get_current_admin_user),
    part_service: PartService = Depends(get_part_service)
):
    """
    Update part information (admin only).
    
    Args:
        part_id: Part UUID
        update_data: Updated part data
        current_user: Current admin user
        part_service: Part service instance
        
    Returns:
        Updated part
        
    Raises:
        HTTPException: If part not found
    """
    part = part_service.update_part(part_id, update_data)
    
    if not part:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Part not found"
        )
    
    return Part.from_orm(part)


@router.delete("/{part_id}")
async def delete_part(
    part_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    part_service: PartService = Depends(get_part_service)
):
    """
    Delete a part (admin only).
    
    Args:
        part_id: Part UUID
        current_user: Current admin user
        part_service: Part service instance
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If part not found
    """
    success = part_service.delete_part(part_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Part not found"
        )
    
    return {"message": "Part deleted successfully"}


@router.get("/{part_id}/similar", response_model=List[PartSummary])
async def get_similar_parts(
    part_id: UUID,
    limit: int = Query(10, ge=1, le=50, description="Maximum number of similar parts"),
    current_user: Optional[User] = Depends(optional_authentication),
    part_service: PartService = Depends(get_part_service)
):
    """
    Get similar parts.
    
    Args:
        part_id: Reference part UUID
        limit: Maximum number of similar parts
        current_user: Optional authenticated user
        part_service: Part service instance
        
    Returns:
        List of similar parts
        
    Raises:
        HTTPException: If part not found
    """
    # Check if reference part exists
    reference_part = part_service.get_part_by_id(part_id)
    if not reference_part:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Reference part not found"
        )
    
    similar_parts = part_service.get_similar_parts(part_id, limit)
    
    return [PartSummary.from_orm(part) for part in similar_parts]


@router.get("/alternative/{alternative_code}", response_model=List[PartSummary])
async def get_parts_by_alternative_code(
    alternative_code: str,
    current_user: Optional[User] = Depends(optional_authentication),
    part_service: PartService = Depends(get_part_service)
):
    """
    Get parts by alternative code.
    
    Args:
        alternative_code: Alternative part code
        current_user: Optional authenticated user
        part_service: Part service instance
        
    Returns:
        List of parts with matching alternative code
    """
    parts = part_service.get_parts_by_alternative_code(alternative_code)
    
    return [PartSummary.from_orm(part) for part in parts]
