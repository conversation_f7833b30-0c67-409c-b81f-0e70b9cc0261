# JWT Authentication Service Implementation

This document describes the JWT authentication service implementation for the AutoParts frontend application.

## Overview

The JWT authentication service provides comprehensive token management, automatic refresh, secure storage, and session persistence across browser refreshes. It's designed to work seamlessly with the backend Python FastAPI authentication system.

## Components

### 1. JWT Service (`jwt-service.ts`)
- **Token Management**: Secure storage and retrieval of JWT tokens using httpOnly cookies
- **Automatic Refresh**: Proactive token refresh before expiration
- **Token Validation**: JWT payload validation and expiration checking
- **Role-based Access**: Built-in role checking utilities
- **Session Scheduling**: Automatic token refresh scheduling

### 2. Authentication Store (`../stores/auth-store.ts`)
- **Zustand Store**: Global state management for authentication
- **Persistent Storage**: User session persistence across browser refreshes
- **Action Methods**: Login, logout, register, and user refresh actions
- **Error Handling**: Comprehensive error state management
- **Loading States**: UI loading state management

### 3. Authentication Context (`context.tsx`)
- **React Context**: Provider for authentication state throughout the app
- **Integration**: Seamless integration with Zustand store
- **HOC Support**: Higher-order component for protected routes
- **Role Hooks**: Custom hooks for role-based access control

### 4. Session Management (`session.ts`)
- **Cross-tab Sync**: Session synchronization across browser tabs
- **Activity Tracking**: User activity monitoring and timeout handling
- **Security**: Session validation and security checks
- **Cleanup**: Automatic session cleanup on inactivity

### 5. Authentication Utilities (`utils.ts`)
- **Helper Functions**: Common authentication utility functions
- **Validation**: Email and password validation utilities
- **Password Generation**: Secure password generation
- **Route Access Control**: Role-based route access checking
- **User Display**: User information formatting utilities

## Key Features

### Secure Token Storage
- Uses httpOnly cookies for secure token storage
- Separate expiration times for access and refresh tokens
- Production-ready security settings (secure, sameSite)

### Automatic Token Refresh
- Proactive refresh 5 minutes before token expiration
- Prevents multiple simultaneous refresh requests
- Automatic retry on refresh failure
- Seamless user experience without interruption

### Session Persistence
- User session persists across browser refreshes
- Cross-tab session synchronization
- Activity-based session timeout
- Secure session validation

### Role-based Access Control
- Built-in role checking (admin, dealership, customer)
- Route-level access control
- Component-level protection
- Flexible permission system

### Comprehensive Error Handling
- Network error handling with user-friendly messages
- Token expiration handling
- Authentication failure management
- Automatic fallback to login page

## Usage Examples

### Basic Authentication
```typescript
import { useAuth } from '@/lib/auth';

function LoginComponent() {
  const { login, isLoading, error } = useAuth();

  const handleLogin = async (credentials) => {
    const result = await login(credentials);
    if (result.success) {
      // Redirect to dashboard
      window.location.href = result.redirectTo;
    }
  };

  return (
    // Login form JSX
  );
}
```

### Protected Routes
```typescript
import { withAuth } from '@/lib/auth';

const ProtectedComponent = withAuth(() => {
  return <div>Protected content</div>;
});
```

### Role-based Access
```typescript
import { useRole } from '@/lib/auth';

function AdminPanel() {
  const { isAdmin } = useRole();

  if (!isAdmin) {
    return <div>Access denied</div>;
  }

  return <div>Admin content</div>;
}
```

### Direct JWT Service Usage
```typescript
import { jwtService } from '@/lib/auth';

// Check authentication status
if (jwtService.isAuthenticated()) {
  // User is authenticated
}

// Get user information from token
const userId = jwtService.getUserId();
const userRole = jwtService.getUserRole();

// Manual token refresh
await jwtService.refreshAccessToken();
```

## Testing

The implementation includes comprehensive unit tests covering:
- Token storage and retrieval
- Authentication status checking
- Token refresh functionality
- Role-based access control
- Error handling scenarios
- Utility functions

Run tests with:
```bash
npm run test:run -- __tests__/auth
```

## Security Considerations

1. **Token Storage**: Uses secure cookies with appropriate flags
2. **Token Validation**: Validates token structure and expiration
3. **Session Security**: Cross-tab synchronization with security checks
4. **Activity Monitoring**: Automatic logout on inactivity
5. **Error Handling**: Secure error messages without sensitive information

## Integration with Backend

The service is designed to work with the Python FastAPI backend:
- Compatible with JWT token format
- Supports refresh token flow
- Handles backend error responses
- Automatic token refresh before expiration

## Environment Configuration

Required environment variables:
- `NEXT_PUBLIC_API_URL`: Backend API base URL
- `NODE_ENV`: Environment (development/production)

## Browser Compatibility

- Modern browsers with localStorage support
- Cookie support required for token storage
- JavaScript enabled for token management

## Performance

- Minimal memory footprint
- Efficient token refresh scheduling
- Optimized for single-page applications
- Lazy loading of authentication state