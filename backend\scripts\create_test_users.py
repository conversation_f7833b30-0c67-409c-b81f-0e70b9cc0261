#!/usr/bin/env python3
"""
Script to create test users for development.
This script creates admin, dealership, and customer test users with pre-activated accounts.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.repositories.user import UserRepository
from app.repositories.dealership import DealershipRepository
from app.models.user import UserRole, UserStatus
from app.schemas.dealership import DealershipCreate
from app.core.security import get_password_hash


def create_test_users():
    """Create test users for development."""
    db: Session = SessionLocal()
    
    try:
        user_repo = UserRepository(db)
        dealership_repo = DealershipRepository(db)
        
        print("Creating test users for development...")
        
        # Test credentials
        test_password = "TestPass123!"
        
        # 1. Create Admin User
        print("\n1. Creating Admin User...")
        admin_email = "<EMAIL>"
        existing_admin = user_repo.get_by_email(admin_email)
        
        if existing_admin:
            print(f"   Admin user already exists: {admin_email}")
        else:
            admin_user = user_repo.create_user(
                email=admin_email,
                password=test_password,
                full_name="Admin User",
                role=UserRole.ADMIN
            )
            # Activate the admin user immediately
            user_repo.verify_email(admin_user.id)
            print(f"   ✅ Admin user created: {admin_email}")
            print(f"   Password: {test_password}")
        
        # 2. Create Test Dealership and Dealership User
        print("\n2. Creating Test Dealership and Dealership User...")
        dealership_email = "<EMAIL>"
        existing_dealership_user = user_repo.get_by_email(dealership_email)
        
        if existing_dealership_user:
            print(f"   Dealership user already exists: {dealership_email}")
        else:
            # First create the dealership
            dealership_data = DealershipCreate(
                name="AutoParts Test Dealership",
                cnpj="12.345.678/0001-90",
                email=dealership_email,
                phone="(11) 99999-9999",
                address="Rua Teste, 123",
                city="São Paulo",
                state="SP",
                zip_code="01234-567",
                description="Test dealership for development"
            )
            
            # Check if dealership already exists
            existing_dealership = dealership_repo.get_by_cnpj(dealership_data.cnpj)
            if existing_dealership:
                dealership = existing_dealership
                print(f"   Dealership already exists: {dealership.name}")
            else:
                dealership = dealership_repo.create(dealership_data.dict())
                print(f"   ✅ Dealership created: {dealership.name}")
            
            # Create dealership admin user
            dealership_user = user_repo.create_user(
                email=dealership_email,
                password=test_password,
                full_name="Dealership Admin",
                role=UserRole.DEALERSHIP,
                dealership_id=dealership.id
            )
            # Activate the dealership user immediately
            user_repo.verify_email(dealership_user.id)
            print(f"   ✅ Dealership user created: {dealership_email}")
            print(f"   Password: {test_password}")
        
        # 3. Create Customer User
        print("\n3. Creating Customer User...")
        customer_email = "<EMAIL>"
        existing_customer = user_repo.get_by_email(customer_email)
        
        if existing_customer:
            print(f"   Customer user already exists: {customer_email}")
        else:
            customer_user = user_repo.create_user(
                email=customer_email,
                password=test_password,
                full_name="Test Customer",
                role=UserRole.CUSTOMER
            )
            # Activate the customer user immediately
            user_repo.verify_email(customer_user.id)
            print(f"   ✅ Customer user created: {customer_email}")
            print(f"   Password: {test_password}")
        
        print("\n" + "="*60)
        print("TEST USERS CREATED SUCCESSFULLY!")
        print("="*60)
        print("\nTest Credentials:")
        print(f"Admin:      {admin_email} / {test_password}")
        print(f"Dealership: {dealership_email} / {test_password}")
        print(f"Customer:   {customer_email} / {test_password}")
        print("\nAll users are pre-activated and ready for testing.")
        print("="*60)
        
    except Exception as e:
        print(f"Error creating test users: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    create_test_users()
