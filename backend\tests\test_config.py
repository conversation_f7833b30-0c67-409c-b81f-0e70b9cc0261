"""
Test configuration and utilities.
"""
import os
from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.database import Base


# SQLite configuration for tests
def configure_sqlite_for_tests():
    """Configure SQLite for testing with UUID support."""
    
    @event.listens_for(Engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        """Set SQLite pragmas for better performance and compatibility."""
        if 'sqlite' in str(dbapi_connection):
            cursor = dbapi_connection.cursor()
            # Enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys=ON")
            # Use WAL mode for better concurrency
            cursor.execute("PRAGMA journal_mode=WAL")
            # Increase cache size
            cursor.execute("PRAGMA cache_size=10000")
            cursor.close()


# Test database setup
def get_test_engine():
    """Get test database engine."""
    configure_sqlite_for_tests()
    
    engine = create_engine(
        "sqlite:///./test.db",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False  # Set to True for SQL debugging
    )
    return engine


def get_test_session():
    """Get test database session."""
    engine = get_test_engine()
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return TestingSessionLocal()


def create_test_tables(engine):
    """Create all tables for testing."""
    Base.metadata.create_all(bind=engine)


def drop_test_tables(engine):
    """Drop all tables after testing."""
    Base.metadata.drop_all(bind=engine)


def cleanup_test_db():
    """Clean up test database file."""
    test_db_path = "./test.db"
    if os.path.exists(test_db_path):
        try:
            os.remove(test_db_path)
        except OSError:
            pass  # File might be in use
    
    # Also clean up WAL files
    for suffix in ["-wal", "-shm"]:
        wal_file = test_db_path + suffix
        if os.path.exists(wal_file):
            try:
                os.remove(wal_file)
            except OSError:
                pass
