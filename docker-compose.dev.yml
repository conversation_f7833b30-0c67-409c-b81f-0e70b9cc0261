version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: autoparts_postgres_dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-autoparts_dev}
      POSTGRES_USER: ${POSTGRES_USER:-autoparts}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-autoparts123}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - autoparts_dev_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-autoparts} -d ${POSTGRES_DB:-autoparts_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache for Development
  redis:
    image: redis:7-alpine
    container_name: autoparts_redis_dev
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_dev_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - autoparts_dev_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # AutoParts API for Development
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: autoparts_api_dev
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-autoparts}:${POSTGRES_PASSWORD:-autoparts123}@postgres:5432/${POSTGRES_DB:-autoparts_dev}
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # Application
      ENVIRONMENT: development
      DEBUG: true
      SECRET_KEY: ${SECRET_KEY:-dev-secret-key-not-for-production}
      
      # Security
      ALLOWED_HOSTS: localhost,127.0.0.1,api.autoparts.local
      CORS_ORIGINS: http://localhost:3000,http://localhost:8080,http://localhost:5173
      
      # JWT
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-dev-jwt-secret-key}
      JWT_ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 60
      
      # Asaas Integration (Sandbox)
      ASAAS_API_KEY: ${ASAAS_API_KEY:-sandbox-api-key}
      ASAAS_BASE_URL: https://www.asaas.com/api/v3
      ASAAS_ENVIRONMENT: sandbox
      
      # File Upload
      MAX_FILE_SIZE: 10485760
      UPLOAD_PATH: /app/uploads
      
      # Logging
      LOG_LEVEL: debug
      
      # Development
      CREATE_SAMPLE_DATA: true
      SQL_ECHO: false
      ENABLE_DOCS: true
      
    volumes:
      - ./backend:/app
      - uploads_dev_data:/app/uploads
      - logs_dev_data:/app/logs
    ports:
      - "${API_PORT:-8000}:8000"
    networks:
      - autoparts_dev_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        while ! nc -z postgres 5432; do sleep 1; done &&
        echo 'Database is ready!' &&
        echo 'Running migrations...' &&
        alembic upgrade head &&
        echo 'Starting development server...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
      "

  # Redis Commander (Redis GUI for development)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: autoparts_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    ports:
      - "8081:8081"
    networks:
      - autoparts_dev_network
    depends_on:
      - redis

  # pgAdmin (PostgreSQL GUI for development)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: autoparts_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    ports:
      - "8082:80"
    networks:
      - autoparts_dev_network
    depends_on:
      - postgres

  # Mailhog (Email testing for development)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: autoparts_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - autoparts_dev_network

# Networks
networks:
  autoparts_dev_network:
    driver: bridge

# Volumes
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  uploads_dev_data:
    driver: local
  logs_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local
