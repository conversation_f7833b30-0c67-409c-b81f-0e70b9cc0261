"""
Dealership service for business logic operations.
"""
from typing import Op<PERSON>, <PERSON>, Tuple, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.dealership import Dealership
from app.models.user import User, UserRole, UserStatus
from app.repositories.dealership import DealershipRepository
from app.repositories.user import UserRepository
from app.schemas.dealership import (
    DealershipCreate,
    DealershipUpdate,
    DealershipRegistration,
    DealershipSearchFilters,
    DealershipStats
)
from app.core.security import get_password_hash


class DealershipService:
    """
    Service class for dealership management operations.
    """

    def __init__(self, db: Session):
        self.db = db
        self.dealership_repo = DealershipRepository(db)
        self.user_repo = UserRepository(db)

    def create_dealership(self, dealership_data: DealershipCreate) -> Dealership:
        """
        Create a new dealership.
        
        Args:
            dealership_data: Dealership creation data
            
        Returns:
            Created dealership instance
            
        Raises:
            ValueError: If CNPJ or email already exists
        """
        # Check if CNPJ already exists
        existing_cnpj = self.dealership_repo.get_by_cnpj(dealership_data.cnpj)
        if existing_cnpj:
            raise ValueError("CNPJ already registered")
        
        # Check if email already exists
        existing_email = self.dealership_repo.get_by_email(dealership_data.email)
        if existing_email:
            raise ValueError("Email already registered")
        
        # Create dealership
        dealership_dict = dealership_data.dict()
        dealership = self.dealership_repo.create(dealership_dict)
        
        return dealership

    def register_dealership_with_admin(
        self,
        registration_data: DealershipRegistration
    ) -> Tuple[Dealership, User]:
        """
        Register a new dealership with admin user.
        
        Args:
            registration_data: Dealership registration data with admin info
            
        Returns:
            Tuple of (dealership, admin_user)
            
        Raises:
            ValueError: If CNPJ, email already exists or user creation fails
        """
        # Check if CNPJ already exists
        existing_cnpj = self.dealership_repo.get_by_cnpj(registration_data.cnpj)
        if existing_cnpj:
            raise ValueError("CNPJ already registered")
        
        # Check if email already exists (both dealership and user)
        existing_dealership_email = self.dealership_repo.get_by_email(registration_data.email)
        if existing_dealership_email:
            raise ValueError("Email already registered for a dealership")
        
        existing_user_email = self.user_repo.get_by_email(registration_data.email)
        if existing_user_email:
            raise ValueError("Email already registered for a user")
        
        try:
            # Create dealership
            dealership_data = DealershipCreate(**registration_data.dict(exclude={
                'admin_full_name', 'admin_password'
            }))
            dealership = self.create_dealership(dealership_data)
            
            # Create admin user for the dealership
            admin_user = self.user_repo.create_user(
                email=registration_data.email,
                password=registration_data.admin_password,
                full_name=registration_data.admin_full_name,
                role=UserRole.DEALERSHIP,
                dealership_id=dealership.id
            )
            
            # Activate the admin user
            self.user_repo.verify_email(admin_user.id)
            
            return dealership, admin_user
            
        except Exception as e:
            # Rollback transaction if anything fails
            self.db.rollback()
            raise e

    def get_dealership_by_id(self, dealership_id: UUID) -> Optional[Dealership]:
        """
        Get dealership by ID.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            Dealership instance or None if not found
        """
        return self.dealership_repo.get_by_id(dealership_id)

    def get_dealership_by_cnpj(self, cnpj: str) -> Optional[Dealership]:
        """
        Get dealership by CNPJ.
        
        Args:
            cnpj: Brazilian company registration number
            
        Returns:
            Dealership instance or None if not found
        """
        return self.dealership_repo.get_by_cnpj(cnpj)

    def update_dealership(
        self,
        dealership_id: UUID,
        update_data: DealershipUpdate
    ) -> Optional[Dealership]:
        """
        Update dealership information.
        
        Args:
            dealership_id: Dealership UUID
            update_data: Updated dealership data
            
        Returns:
            Updated dealership instance or None if not found
            
        Raises:
            ValueError: If email already exists for another dealership
        """
        # Check if email is being updated and already exists
        if update_data.email:
            existing_email = self.dealership_repo.get_by_email(update_data.email)
            if existing_email and existing_email.id != dealership_id:
                raise ValueError("Email already registered for another dealership")
        
        # Update dealership
        update_dict = update_data.dict(exclude_unset=True)
        return self.dealership_repo.update(dealership_id, update_dict)

    def list_dealerships(
        self,
        filters: DealershipSearchFilters
    ) -> Tuple[List[Dealership], int]:
        """
        List dealerships with filtering and pagination.
        
        Args:
            filters: Search and filter criteria
            
        Returns:
            Tuple of (dealerships list, total count)
        """
        skip = (filters.page - 1) * filters.per_page
        
        if filters.search:
            # Use search functionality
            dealerships = self.dealership_repo.search_dealerships(
                search_term=filters.search,
                skip=skip,
                limit=filters.per_page,
                active_only=filters.active_only
            )
            # Get total count for search
            total = len(self.dealership_repo.search_dealerships(
                search_term=filters.search,
                active_only=filters.active_only
            ))
        else:
            # Use regular listing with filters
            filter_dict = {}
            if filters.active_only:
                filter_dict['is_active'] = True
            if filters.city:
                filter_dict['city'] = filters.city
            if filters.state:
                filter_dict['state'] = filters.state
            
            dealerships = self.dealership_repo.get_multi(
                skip=skip,
                limit=filters.per_page,
                filters=filter_dict
            )
            total = self.dealership_repo.count(filter_dict)
        
        return dealerships, total

    def activate_dealership(self, dealership_id: UUID) -> Optional[Dealership]:
        """
        Activate a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            Updated dealership instance or None if not found
        """
        return self.dealership_repo.activate_dealership(str(dealership_id))

    def deactivate_dealership(self, dealership_id: UUID) -> Optional[Dealership]:
        """
        Deactivate a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            Updated dealership instance or None if not found
        """
        return self.dealership_repo.deactivate_dealership(str(dealership_id))

    def delete_dealership(self, dealership_id: UUID) -> bool:
        """
        Delete a dealership (soft delete by deactivating).
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            True if deleted successfully, False if not found
        """
        dealership = self.deactivate_dealership(dealership_id)
        return dealership is not None

    def get_dealership_stats(self) -> DealershipStats:
        """
        Get dealership statistics.
        
        Returns:
            Dealership statistics
        """
        # Total dealerships
        total = self.dealership_repo.count()
        
        # Active/inactive counts
        active = self.dealership_repo.count({'is_active': True})
        inactive = total - active
        
        # Dealerships by state
        by_state = self.dealership_repo.count_by_state()
        dealerships_by_state = [
            {'state': state, 'count': count}
            for state, count in by_state
        ]
        
        # Recent registrations (last 30 days)
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent = self.db.query(func.count(Dealership.id)).filter(
            Dealership.created_at >= thirty_days_ago
        ).scalar()
        
        return DealershipStats(
            total_dealerships=total,
            active_dealerships=active,
            inactive_dealerships=inactive,
            dealerships_by_state=dealerships_by_state,
            recent_registrations=recent
        )

    def get_dealership_users(self, dealership_id: UUID) -> List[User]:
        """
        Get all users associated with a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            List of users associated with the dealership
        """
        return self.user_repo.get_dealership_users(dealership_id)
