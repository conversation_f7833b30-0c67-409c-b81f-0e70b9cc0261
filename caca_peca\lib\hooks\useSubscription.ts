import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { subscriptionApi } from '@/lib/api/subscription'
import { 
  Subscription, 
  SubscriptionCreate, 
  SubscriptionUpdate,
  BillingHistory,
  SubscriptionPlanInfo
} from '@/lib/types/api'
import { toast } from 'sonner'

// Query keys
export const subscriptionKeys = {
  all: ['subscription'] as const,
  current: () => [...subscriptionKeys.all, 'current'] as const,
  billingHistory: (filters?: any) => [...subscriptionKeys.all, 'billing-history', filters] as const,
  plans: () => [...subscriptionKeys.all, 'plans'] as const,
  usage: () => [...subscriptionKeys.all, 'usage'] as const,
  analytics: (period?: string) => [...subscriptionKeys.all, 'analytics', period] as const,
  notifications: () => [...subscriptionKeys.all, 'notifications'] as const,
}

// Get current subscription
export const useCurrentSubscription = () => {
  return useQuery({
    queryKey: subscriptionKeys.current(),
    queryFn: subscriptionApi.getCurrentSubscription,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// Get subscription plans
export const useSubscriptionPlans = () => {
  return useQuery({
    queryKey: subscriptionKeys.plans(),
    queryFn: subscriptionApi.getSubscriptionPlans,
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

// Get billing history
export const useBillingHistory = (
  page: number = 1,
  size: number = 20,
  status?: string,
  startDate?: string,
  endDate?: string
) => {
  return useQuery({
    queryKey: subscriptionKeys.billingHistory({ page, size, status, startDate, endDate }),
    queryFn: () => subscriptionApi.getBillingHistory(page, size, status, startDate, endDate),
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get subscription usage
export const useSubscriptionUsage = () => {
  return useQuery({
    queryKey: subscriptionKeys.usage(),
    queryFn: subscriptionApi.getSubscriptionUsage,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// Get subscription analytics
export const useSubscriptionAnalytics = (period: 'month' | 'quarter' | 'year' = 'month') => {
  return useQuery({
    queryKey: subscriptionKeys.analytics(period),
    queryFn: () => subscriptionApi.getSubscriptionAnalytics(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

// Get subscription notifications
export const useSubscriptionNotifications = () => {
  return useQuery({
    queryKey: subscriptionKeys.notifications(),
    queryFn: subscriptionApi.getSubscriptionNotifications,
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 2,
  })
}

// Create subscription mutation
export const useCreateSubscription = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: subscriptionApi.createSubscription,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.usage() })
      toast.success('Assinatura criada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao criar assinatura'
      toast.error(message)
    },
  })
}

// Update subscription mutation
export const useUpdateSubscription = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SubscriptionUpdate }) =>
      subscriptionApi.updateSubscription(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.usage() })
      toast.success('Assinatura atualizada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao atualizar assinatura'
      toast.error(message)
    },
  })
}

// Cancel subscription mutation
export const useCancelSubscription = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: subscriptionApi.cancelSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      toast.success('Assinatura cancelada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao cancelar assinatura'
      toast.error(message)
    },
  })
}

// Reactivate subscription mutation
export const useReactivateSubscription = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: subscriptionApi.reactivateSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      toast.success('Assinatura reativada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao reativar assinatura'
      toast.error(message)
    },
  })
}

// Update payment method mutation
export const useUpdatePaymentMethod = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ subscriptionId, billingType }: { subscriptionId: string; billingType: string }) =>
      subscriptionApi.updatePaymentMethod(subscriptionId, billingType),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      toast.success('Método de pagamento atualizado com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao atualizar método de pagamento'
      toast.error(message)
    },
  })
}

// Apply coupon mutation
export const useApplyCoupon = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ subscriptionId, couponCode }: { subscriptionId: string; couponCode: string }) =>
      subscriptionApi.applyCoupon(subscriptionId, couponCode),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      toast.success(`Cupom aplicado! Desconto de R$ ${data.data.data.discount_applied.toFixed(2)}`)
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao aplicar cupom'
      toast.error(message)
    },
  })
}

// Retry payment mutation
export const useRetryPayment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: subscriptionApi.retryPayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.billingHistory() })
      toast.success('Pagamento processado novamente!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao processar pagamento'
      toast.error(message)
    },
  })
}

// Sync subscription mutation
export const useSyncSubscription = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: subscriptionApi.syncSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.current() })
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.billingHistory() })
      toast.success('Assinatura sincronizada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao sincronizar assinatura'
      toast.error(message)
    },
  })
}

// Mark notification as read mutation
export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: subscriptionApi.markNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.notifications() })
    },
    onError: (error: any) => {
      console.error('Error marking notification as read:', error)
    },
  })
}

// Download invoice
export const useDownloadInvoice = () => {
  return useMutation({
    mutationFn: subscriptionApi.downloadInvoice,
    onSuccess: (blob, invoiceId) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `fatura-${invoiceId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      toast.success('Fatura baixada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao baixar fatura'
      toast.error(message)
    },
  })
}

// Preview plan change
export const usePreviewPlanChange = () => {
  return useMutation({
    mutationFn: ({ currentPlanType, newPlanType, cycle }: {
      currentPlanType: string;
      newPlanType: string;
      cycle: string;
    }) => subscriptionApi.previewPlanChange(currentPlanType, newPlanType, cycle),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao calcular mudança de plano'
      toast.error(message)
    },
  })
}
