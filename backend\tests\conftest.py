"""
Pytest configuration and fixtures for AutoParts API tests.
"""
import os
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from unittest.mock import Mock, patch
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Set test environment
os.environ["TESTING"] = "1"
os.environ["DATABASE_URL"] = "sqlite:///./test.db"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings
from app.core.cache import cache_manager
from app.models.user import User
from app.models.dealership import Dealership
from app.models.part import Part
from app.models.inventory import Inventory
from app.services.auth import AuthService


# Import test configuration
from tests.test_config import get_test_engine, create_test_tables, drop_test_tables, cleanup_test_db

# Test database setup
engine = get_test_engine()
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session() -> Generator[Session, None, None]:
    """
    Create a fresh database session for each test.
    """
    # Create all tables
    create_test_tables(engine)

    # Create session
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.close()
        # Drop all tables after test
        drop_test_tables(engine)


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """
    Create a test client with database dependency override.
    """
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def async_client(db_session: Session) -> AsyncGenerator[AsyncClient, None]:
    """
    Create an async test client with database dependency override.
    """
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def mock_cache():
    """Mock Redis cache for tests."""
    with patch.object(cache_manager, 'is_available', return_value=False):
        with patch.object(cache_manager, 'get', return_value=None):
            with patch.object(cache_manager, 'set', return_value=True):
                with patch.object(cache_manager, 'delete', return_value=True):
                    yield cache_manager


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "is_active": True,
        "is_admin": False
    }


@pytest.fixture
def sample_dealership_data():
    """Sample dealership data for testing."""
    return {
        "name": "Test Dealership",
        "cnpj": "12345678901234",
        "email": "<EMAIL>",
        "phone": "(11) 99999-9999",
        "address": "Test Address, 123",
        "city": "São Paulo",
        "state": "SP",
        "zip_code": "01234-567",
        "is_active": True
    }


@pytest.fixture
def sample_part_data():
    """Sample part data for testing."""
    return {
        "code": "TEST001",
        "name": "Test Part",
        "description": "A test automotive part",
        "category": "Engine",
        "brand": "Test Brand",
        "model": "Test Model",
        "year": 2023
    }


@pytest.fixture
def sample_inventory_data():
    """Sample inventory data for testing."""
    return {
        "quantity": 10,
        "price": 99.99,
        "cost": 50.00,
        "location": "A1-B2",
        "condition": "new",
        "is_available": True
    }


@pytest.fixture
def created_user(db_session: Session, sample_user_data: dict) -> User:
    """Create a test user in the database."""
    auth_service = AuthService(db_session)
    user = auth_service.create_user(
        email=sample_user_data["email"],
        password=sample_user_data["password"],
        full_name=sample_user_data["full_name"]
    )
    db_session.commit()
    return user


@pytest.fixture
def created_admin_user(db_session: Session) -> User:
    """Create a test admin user in the database."""
    auth_service = AuthService(db_session)
    user = auth_service.create_user(
        email="<EMAIL>",
        password="adminpassword123",
        full_name="Admin User"
    )
    user.is_admin = True
    db_session.commit()
    return user


@pytest.fixture
def created_dealership(db_session: Session, sample_dealership_data: dict) -> Dealership:
    """Create a test dealership in the database."""
    dealership = Dealership(**sample_dealership_data)
    db_session.add(dealership)
    db_session.commit()
    db_session.refresh(dealership)
    return dealership


@pytest.fixture
def created_part(db_session: Session, sample_part_data: dict) -> Part:
    """Create a test part in the database."""
    part = Part(**sample_part_data)
    db_session.add(part)
    db_session.commit()
    db_session.refresh(part)
    return part


@pytest.fixture
def created_inventory(
    db_session: Session,
    created_dealership: Dealership,
    created_part: Part,
    sample_inventory_data: dict
) -> Inventory:
    """Create a test inventory item in the database."""
    inventory_data = sample_inventory_data.copy()
    inventory_data["dealership_id"] = created_dealership.id
    inventory_data["part_id"] = created_part.id
    
    inventory = Inventory(**inventory_data)
    db_session.add(inventory)
    db_session.commit()
    db_session.refresh(inventory)
    return inventory


@pytest.fixture
def auth_headers(created_user: User) -> dict:
    """Create authentication headers for API requests."""
    auth_service = AuthService(None)  # No DB needed for token creation
    access_token = auth_service.create_access_token(data={"sub": created_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_auth_headers(created_admin_user: User) -> dict:
    """Create admin authentication headers for API requests."""
    auth_service = AuthService(None)  # No DB needed for token creation
    access_token = auth_service.create_access_token(data={"sub": created_admin_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_asaas_client():
    """Mock Asaas client for testing."""
    mock_client = Mock()
    mock_client.create_customer.return_value = {
        "id": "cus_test123",
        "name": "Test Customer",
        "email": "<EMAIL>"
    }
    mock_client.create_subscription.return_value = {
        "id": "sub_test123",
        "status": "ACTIVE",
        "customer": "cus_test123"
    }
    mock_client.get_subscription.return_value = {
        "id": "sub_test123",
        "status": "ACTIVE",
        "nextDueDate": "2024-02-01"
    }
    return mock_client


@pytest.fixture
def sample_excel_file_path(tmp_path):
    """Create a sample Excel file for testing."""
    import pandas as pd
    
    # Sample data
    data = {
        "EmpresaNome": ["Test Dealership"] * 5,
        "CodigoReferenciaProduto": ["TEST001", "TEST002", "TEST003", "TEST004", "TEST005"],
        "Produto_Descricao": ["Test Part 1", "Test Part 2", "Test Part 3", "Test Part 4", "Test Part 5"],
        "QtdeSaldo": [10, 5, 20, 15, 8],
        "ValorCustoMedio": [50.00, 75.00, 30.00, 100.00, 25.00],
        "ValorPublico": [99.99, 149.99, 59.99, 199.99, 49.99],
        "Marca_Descricao": ["Brand A", "Brand B", "Brand A", "Brand C", "Brand B"]
    }
    
    df = pd.DataFrame(data)
    file_path = tmp_path / "test_inventory.xlsx"
    df.to_excel(file_path, index=False)
    
    return str(file_path)


@pytest.fixture
def sample_csv_file_path(tmp_path):
    """Create a sample CSV file for testing."""
    import pandas as pd
    
    # Sample data
    data = {
        "EmpresaNome": ["Test Dealership"] * 3,
        "CodigoReferenciaProduto": ["CSV001", "CSV002", "CSV003"],
        "Produto_Descricao": ["CSV Part 1", "CSV Part 2", "CSV Part 3"],
        "QtdeSaldo": [5, 10, 15],
        "ValorCustoMedio": [40.00, 60.00, 80.00],
        "ValorPublico": [79.99, 119.99, 159.99]
    }
    
    df = pd.DataFrame(data)
    file_path = tmp_path / "test_inventory.csv"
    df.to_csv(file_path, index=False)
    
    return str(file_path)


# Test utilities
class TestUtils:
    """Utility functions for tests."""
    
    @staticmethod
    def assert_response_success(response, expected_status=200):
        """Assert that a response is successful."""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"
    
    @staticmethod
    def assert_response_error(response, expected_status=400):
        """Assert that a response is an error."""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"
    
    @staticmethod
    def assert_valid_uuid(uuid_string):
        """Assert that a string is a valid UUID."""
        import uuid
        try:
            uuid.UUID(uuid_string)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def assert_valid_datetime(datetime_string):
        """Assert that a string is a valid ISO datetime."""
        from datetime import datetime
        try:
            datetime.fromisoformat(datetime_string.replace('Z', '+00:00'))
            return True
        except ValueError:
            return False


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils
