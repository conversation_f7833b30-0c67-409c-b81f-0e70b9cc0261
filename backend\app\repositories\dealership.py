"""
Dealership repository for data access operations.
"""
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.dealership import Dealership
from app.repositories.base import BaseRepository


class DealershipRepository(BaseRepository[Dealership]):
    """
    Repository for dealership-specific database operations.
    """

    def __init__(self, db: Session):
        super().__init__(Dealership, db)

    def get_by_cnpj(self, cnpj: str) -> Optional[Dealership]:
        """
        Get dealership by CNPJ.
        
        Args:
            cnpj: Brazilian company registration number
            
        Returns:
            Dealership instance or None if not found
        """
        return self.db.query(Dealership).filter(Dealership.cnpj == cnpj).first()

    def get_by_email(self, email: str) -> Optional[Dealership]:
        """
        Get dealership by email.
        
        Args:
            email: Email address
            
        Returns:
            Dealership instance or None if not found
        """
        return self.db.query(Dealership).filter(Dealership.email == email).first()

    def get_active_dealerships(
        self,
        skip: int = 0,
        limit: int = 100,
        city: Optional[str] = None,
        state: Optional[str] = None
    ) -> List[Dealership]:
        """
        Get active dealerships with optional location filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            city: Filter by city
            state: Filter by state
            
        Returns:
            List of active dealership instances
        """
        query = self.db.query(Dealership).filter(Dealership.is_active == True)
        
        if city:
            query = query.filter(Dealership.city.ilike(f"%{city}%"))
        
        if state:
            query = query.filter(Dealership.state == state)
        
        return query.offset(skip).limit(limit).all()

    def search_dealerships(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[Dealership]:
        """
        Search dealerships by name, trading name, or city.
        
        Args:
            search_term: Search term to match against name, trading name, or city
            skip: Number of records to skip
            limit: Maximum number of records to return
            active_only: Whether to include only active dealerships
            
        Returns:
            List of matching dealership instances
        """
        search_pattern = f"%{search_term}%"
        query = self.db.query(Dealership).filter(
            or_(
                Dealership.name.ilike(search_pattern),
                Dealership.trading_name.ilike(search_pattern),
                Dealership.city.ilike(search_pattern)
            )
        )
        
        if active_only:
            query = query.filter(Dealership.is_active == True)
        
        return query.offset(skip).limit(limit).all()

    def get_dealerships_by_state(self, state: str) -> List[Dealership]:
        """
        Get all dealerships in a specific state.
        
        Args:
            state: State abbreviation (e.g., SP, RJ)
            
        Returns:
            List of dealership instances in the state
        """
        return self.db.query(Dealership).filter(
            and_(
                Dealership.state == state,
                Dealership.is_active == True
            )
        ).all()

    def get_dealerships_with_subscription(self) -> List[Dealership]:
        """
        Get dealerships that have an active subscription.
        
        Returns:
            List of dealership instances with subscriptions
        """
        return self.db.query(Dealership).filter(
            and_(
                Dealership.subscription_id.isnot(None),
                Dealership.is_active == True
            )
        ).all()

    def count_by_state(self) -> List[tuple]:
        """
        Count dealerships by state.
        
        Returns:
            List of tuples (state, count)
        """
        return (
            self.db.query(Dealership.state, func.count(Dealership.id))
            .filter(Dealership.is_active == True)
            .group_by(Dealership.state)
            .all()
        )

    def activate_dealership(self, dealership_id: str) -> Optional[Dealership]:
        """
        Activate a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            Updated dealership instance or None if not found
        """
        return self.update(dealership_id, {"is_active": True})

    def deactivate_dealership(self, dealership_id: str) -> Optional[Dealership]:
        """
        Deactivate a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            Updated dealership instance or None if not found
        """
        return self.update(dealership_id, {"is_active": False})

    def update_subscription_id(
        self,
        dealership_id: str,
        subscription_id: str
    ) -> Optional[Dealership]:
        """
        Update dealership's Asaas subscription ID.
        
        Args:
            dealership_id: Dealership UUID
            subscription_id: Asaas subscription ID
            
        Returns:
            Updated dealership instance or None if not found
        """
        return self.update(dealership_id, {"subscription_id": subscription_id})
