#!/bin/bash

# AutoParts API Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Default values
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Set compose file based on environment
case $ENVIRONMENT in
    "development"|"dev")
        COMPOSE_FILE="docker-compose.dev.yml"
        ENV_FILE=".env.dev"
        ;;
    "staging")
        COMPOSE_FILE="docker-compose.staging.yml"
        ENV_FILE=".env.staging"
        ;;
    "production"|"prod")
        COMPOSE_FILE="docker-compose.yml"
        ENV_FILE=".env"
        ;;
    *)
        error "Invalid environment: $ENVIRONMENT"
        echo "Usage: $0 [development|staging|production]"
        exit 1
        ;;
esac

log "Starting deployment for environment: $ENVIRONMENT"
log "Using compose file: $COMPOSE_FILE"
log "Using environment file: $ENV_FILE"

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    error "Environment file $ENV_FILE not found!"
    echo "Please copy .env.example to $ENV_FILE and configure it."
    exit 1
fi

# Load environment variables
set -a
source "$ENV_FILE"
set +a

# Validate required environment variables
validate_env() {
    local required_vars=(
        "SECRET_KEY"
        "JWT_SECRET_KEY"
        "DATABASE_URL"
        "REDIS_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            error "Required environment variable $var is not set!"
            exit 1
        fi
    done
    
    # Check for default/insecure values in production
    if [ "$ENVIRONMENT" = "production" ]; then
        if [[ "$SECRET_KEY" == *"change-in-production"* ]] || [[ "$SECRET_KEY" == *"dev-secret"* ]]; then
            error "SECRET_KEY contains default/development value in production!"
            exit 1
        fi
        
        if [[ "$JWT_SECRET_KEY" == *"dev-jwt"* ]] || [ ${#JWT_SECRET_KEY} -lt 32 ]; then
            error "JWT_SECRET_KEY is not secure enough for production!"
            exit 1
        fi
    fi
    
    success "Environment validation passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    local dirs=(
        "logs"
        "uploads"
        "database/backups"
        "nginx/ssl"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log "Created directory: $dir"
        fi
    done
    
    success "Directories created"
}

# Generate SSL certificates for development
generate_ssl_certs() {
    if [ "$ENVIRONMENT" != "production" ] && [ ! -f "nginx/ssl/autoparts.crt" ]; then
        log "Generating self-signed SSL certificates for development..."
        
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/autoparts.key \
            -out nginx/ssl/autoparts.crt \
            -subj "/C=BR/ST=SP/L=SaoPaulo/O=AutoParts/CN=localhost" \
            2>/dev/null || warning "Failed to generate SSL certificates"
        
        success "SSL certificates generated"
    fi
}

# Build and start services
deploy_services() {
    log "Building and starting services..."
    
    # Pull latest images
    docker-compose -f "$COMPOSE_FILE" pull
    
    # Build application image
    docker-compose -f "$COMPOSE_FILE" build --no-cache api
    
    # Start services
    docker-compose -f "$COMPOSE_FILE" up -d
    
    success "Services started"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to be healthy..."
    
    local max_wait=300  # 5 minutes
    local wait_time=0
    
    while [ $wait_time -lt $max_wait ]; do
        if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up (healthy)"; then
            success "Services are healthy"
            return 0
        fi
        
        sleep 5
        wait_time=$((wait_time + 5))
        log "Waiting... ($wait_time/${max_wait}s)"
    done
    
    error "Services failed to become healthy within ${max_wait}s"
    docker-compose -f "$COMPOSE_FILE" logs api
    exit 1
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    docker-compose -f "$COMPOSE_FILE" exec -T api alembic upgrade head
    
    success "Database migrations completed"
}

# Create initial data for development
create_initial_data() {
    if [ "$ENVIRONMENT" = "development" ] && [ "$CREATE_SAMPLE_DATA" = "true" ]; then
        log "Creating initial sample data..."
        
        docker-compose -f "$COMPOSE_FILE" exec -T api python -c "
from app.core.database import get_db
from app.core.init_db import init_db
init_db(next(get_db()))
print('Sample data created successfully')
" || warning "Failed to create sample data"
    fi
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo ""
    docker-compose -f "$COMPOSE_FILE" ps
    echo ""
    
    log "Service URLs:"
    echo "  API: http://localhost:${API_PORT:-8000}"
    echo "  API Docs: http://localhost:${API_PORT:-8000}/docs"
    echo "  Health Check: http://localhost:${API_PORT:-8000}/health"
    
    if [ "$ENVIRONMENT" = "development" ]; then
        echo "  pgAdmin: http://localhost:8082 (<EMAIL> / admin123)"
        echo "  Redis Commander: http://localhost:8081 (admin / admin)"
        echo "  Mailhog: http://localhost:8025"
    fi
    
    echo ""
    success "Deployment completed successfully!"
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        error "Deployment failed!"
        log "Checking service logs..."
        docker-compose -f "$COMPOSE_FILE" logs --tail=50
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment flow
main() {
    log "AutoParts API Deployment Script"
    log "================================"
    
    validate_env
    create_directories
    generate_ssl_certs
    deploy_services
    wait_for_services
    run_migrations
    create_initial_data
    show_status
}

# Run main function
main "$@"
