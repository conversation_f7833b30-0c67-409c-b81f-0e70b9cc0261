/**
 * Authentication Utilities and Helpers
 * Common authentication-related utility functions
 */

import { jwtService } from './jwt-service';
import { UserRole } from '../types/api';

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  return jwtService.isAuthenticated();
};

/**
 * Get current user's role
 */
export const getCurrentUserRole = (): string | null => {
  return jwtService.getUserRole();
};

/**
 * Get current user's ID
 */
export const getCurrentUserId = (): string | null => {
  return jwtService.getUserId();
};

/**
 * Get current user's email
 */
export const getCurrentUserEmail = (): string | null => {
  return jwtService.getUserEmail();
};

/**
 * Check if current user has specific role
 */
export const hasRole = (role: UserRole): boolean => {
  const userRole = getCurrentUserRole();
  return userRole === role;
};

/**
 * Check if current user is admin
 */
export const isAdmin = (): boolean => {
  return hasRole(UserRole.ADMIN);
};

/**
 * Check if current user is dealership
 */
export const isDealership = (): boolean => {
  return hasRole(UserRole.DEALERSHIP);
};

/**
 * Check if current user is customer
 */
export const isCustomer = (): boolean => {
  return hasRole(UserRole.CUSTOMER);
};

/**
 * Get redirect path based on user role
 */
export const getRedirectPath = (role: UserRole): string => {
  switch (role) {
    case UserRole.ADMIN:
      return '/admin';
    case UserRole.DEALERSHIP:
      return '/painel';
    case UserRole.CUSTOMER:
      return '/';
    default:
      return '/';
  }
};

/**
 * Get default redirect path for current user
 */
export const getCurrentUserRedirectPath = (): string => {
  const role = getCurrentUserRole() as UserRole;
  return role ? getRedirectPath(role) : '/';
};

/**
 * Check if user can access a specific route based on role
 */
export const canAccessRoute = (route: string, userRole?: string): boolean => {
  const role = userRole || getCurrentUserRole();
  
  if (!role) return false;

  // Admin routes
  if (route.startsWith('/admin')) {
    return role === UserRole.ADMIN;
  }

  // Dealership routes
  if (route.startsWith('/painel') || route.startsWith('/dashboard')) {
    return role === UserRole.DEALERSHIP || role === UserRole.ADMIN;
  }

  // Public routes accessible to all authenticated users
  const publicRoutes = ['/', '/busca', '/contato', '/sobre'];
  if (publicRoutes.includes(route)) {
    return true;
  }

  // Default: allow access if authenticated
  return isAuthenticated();
};

/**
 * Format user display name
 */
export const formatUserDisplayName = (user: { full_name?: string; email: string }): string => {
  return user.full_name || user.email.split('@')[0];
};

/**
 * Get user initials for avatar
 */
export const getUserInitials = (user: { full_name?: string; email: string }): string => {
  if (user.full_name) {
    return user.full_name
      .split(' ')
      .map(name => name.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }
  
  return user.email.charAt(0).toUpperCase();
};

/**
 * Check if token is about to expire
 */
export const isTokenExpiringSoon = (thresholdMinutes: number = 5): boolean => {
  const timeUntilExpiry = jwtService.getTimeUntilExpiry();
  if (!timeUntilExpiry) return false;
  
  return timeUntilExpiry < (thresholdMinutes * 60 * 1000);
};

/**
 * Get token expiration info
 */
export const getTokenExpirationInfo = () => {
  const expiration = jwtService.getTokenExpiration();
  const timeUntilExpiry = jwtService.getTimeUntilExpiry();
  
  if (!expiration || !timeUntilExpiry) {
    return null;
  }

  const minutes = Math.floor(timeUntilExpiry / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  return {
    expiration,
    timeUntilExpiry,
    minutes,
    hours,
    days,
    isExpiringSoon: isTokenExpiringSoon(),
    formattedTimeLeft: formatTimeLeft(timeUntilExpiry),
  };
};

/**
 * Format time left until expiration
 */
const formatTimeLeft = (milliseconds: number): string => {
  const minutes = Math.floor(milliseconds / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days} dia${days > 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hora${hours > 1 ? 's' : ''}`;
  } else if (minutes > 0) {
    return `${minutes} minuto${minutes > 1 ? 's' : ''}`;
  } else {
    return 'menos de 1 minuto';
  }
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} => {
  const errors: string[] = [];
  let score = 0;

  // Length check
  if (password.length < 8) {
    errors.push('A senha deve ter pelo menos 8 caracteres');
  } else {
    score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('A senha deve conter pelo menos uma letra maiúscula');
  } else {
    score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('A senha deve conter pelo menos uma letra minúscula');
  } else {
    score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('A senha deve conter pelo menos um número');
  } else {
    score += 1;
  }

  // Special character check
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('A senha deve conter pelo menos um caractere especial');
  } else {
    score += 1;
  }

  // Determine strength
  let strength: 'weak' | 'medium' | 'strong';
  if (score < 3) {
    strength = 'weak';
  } else if (score < 5) {
    strength = 'medium';
  } else {
    strength = 'strong';
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength,
  };
};

/**
 * Generate secure random password
 */
export const generateSecurePassword = (length: number = 12): string => {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*(),.?":{}|<>';
  
  const allChars = lowercase + uppercase + numbers + symbols;
  
  let password = '';
  
  // Ensure at least one character from each category
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

/**
 * Clear all authentication data
 */
export const clearAuthData = (): void => {
  jwtService.clearTokens();
  
  // Clear any other auth-related data from localStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem('autoparts-auth-storage');
  }
};

/**
 * Handle authentication error
 */
export const handleAuthError = (error: any): void => {
  console.error('Authentication error:', error);
  
  // Clear tokens and redirect to login
  clearAuthData();
  
  if (typeof window !== 'undefined') {
    window.location.href = '/auth/login';
  }
};

/**
 * Session persistence utilities
 */
export const sessionUtils = {
  /**
   * Save session data to localStorage
   */
  saveSessionData: (key: string, data: any): void => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(`autoparts_session_${key}`, JSON.stringify(data));
      } catch (error) {
        console.error('Failed to save session data:', error);
      }
    }
  },

  /**
   * Get session data from localStorage
   */
  getSessionData: (key: string): any => {
    if (typeof window !== 'undefined') {
      try {
        const data = localStorage.getItem(`autoparts_session_${key}`);
        return data ? JSON.parse(data) : null;
      } catch (error) {
        console.error('Failed to get session data:', error);
        return null;
      }
    }
    return null;
  },

  /**
   * Remove session data from localStorage
   */
  removeSessionData: (key: string): void => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`autoparts_session_${key}`);
    }
  },

  /**
   * Clear all session data
   */
  clearAllSessionData: (): void => {
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('autoparts_session_')) {
          localStorage.removeItem(key);
        }
      });
    }
  },
};