"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Download, 
  RefreshCw, 
  Calendar, 
  Filter,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  FileText,
  CreditCard,
  Smartphone
} from "lucide-react"
import { 
  useBillingHistory, 
  useDownloadInvoice, 
  useRetryPayment 
} from "@/lib/hooks/useSubscription"
import { PaymentStatus, BillingType } from "@/lib/types/api"
import { formatCurrency, formatDate } from "@/lib/utils"

interface BillingHistoryProps {
  className?: string
}

export function BillingHistory({ className }: BillingHistoryProps) {
  const [page, setPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState<string>("")
  const [startDate, setStartDate] = useState<string>("")
  const [endDate, setEndDate] = useState<string>("")
  const [showFilters, setShowFilters] = useState(false)

  const { 
    data: billingResponse, 
    isLoading, 
    refetch 
  } = useBillingHistory(page, 20, statusFilter, startDate, endDate)

  const downloadInvoiceMutation = useDownloadInvoice()
  const retryPaymentMutation = useRetryPayment()

  const billingData = billingResponse?.data?.success ? billingResponse.data.data : null
  const invoices = billingData?.items || []
  const totalPages = billingData?.pages || 1

  const getStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.RECEIVED:
      case PaymentStatus.CONFIRMED:
      case PaymentStatus.RECEIVED_IN_CASH:
        return "bg-green-100 text-green-800"
      case PaymentStatus.PENDING:
      case PaymentStatus.AWAITING_RISK_ANALYSIS:
        return "bg-yellow-100 text-yellow-800"
      case PaymentStatus.OVERDUE:
        return "bg-red-100 text-red-800"
      case PaymentStatus.REFUNDED:
      case PaymentStatus.REFUND_REQUESTED:
      case PaymentStatus.REFUND_IN_PROGRESS:
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.RECEIVED:
      case PaymentStatus.CONFIRMED:
      case PaymentStatus.RECEIVED_IN_CASH:
        return <CheckCircle className="h-3 w-3" />
      case PaymentStatus.PENDING:
      case PaymentStatus.AWAITING_RISK_ANALYSIS:
        return <Clock className="h-3 w-3" />
      case PaymentStatus.OVERDUE:
        return <AlertTriangle className="h-3 w-3" />
      case PaymentStatus.REFUNDED:
      case PaymentStatus.REFUND_REQUESTED:
      case PaymentStatus.REFUND_IN_PROGRESS:
        return <RefreshCw className="h-3 w-3" />
      default:
        return <XCircle className="h-3 w-3" />
    }
  }

  const getBillingTypeIcon = (billingType: BillingType) => {
    switch (billingType) {
      case BillingType.CREDIT_CARD:
        return <CreditCard className="h-4 w-4" />
      case BillingType.PIX:
        return <Smartphone className="h-4 w-4" />
      case BillingType.BOLETO:
        return <FileText className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getBillingTypeName = (billingType: BillingType) => {
    switch (billingType) {
      case BillingType.CREDIT_CARD:
        return "Cartão"
      case BillingType.PIX:
        return "PIX"
      case BillingType.BOLETO:
        return "Boleto"
      default:
        return billingType
    }
  }

  const handleDownloadInvoice = async (invoiceId: string) => {
    await downloadInvoiceMutation.mutateAsync(invoiceId)
  }

  const handleRetryPayment = async (paymentId: string) => {
    await retryPaymentMutation.mutateAsync(paymentId)
  }

  const handleFilterApply = () => {
    setPage(1)
    refetch()
  }

  const handleFilterClear = () => {
    setStatusFilter("")
    setStartDate("")
    setEndDate("")
    setPage(1)
    refetch()
  }

  const canRetryPayment = (status: PaymentStatus) => {
    return status === PaymentStatus.OVERDUE || 
           status === PaymentStatus.PENDING ||
           status === PaymentStatus.AWAITING_RISK_ANALYSIS
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Histórico de Faturas</h2>
          <p className="text-muted-foreground">
            Visualize e gerencie suas faturas e pagamentos
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filtros</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os status</SelectItem>
                    <SelectItem value={PaymentStatus.RECEIVED}>Pago</SelectItem>
                    <SelectItem value={PaymentStatus.PENDING}>Pendente</SelectItem>
                    <SelectItem value={PaymentStatus.OVERDUE}>Vencido</SelectItem>
                    <SelectItem value={PaymentStatus.REFUNDED}>Reembolsado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="start-date">Data Inicial</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="end-date">Data Final</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <div className="flex gap-2">
                  <Button onClick={handleFilterApply}>Aplicar</Button>
                  <Button variant="outline" onClick={handleFilterClear}>
                    Limpar
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Data</TableHead>
                <TableHead>Descrição</TableHead>
                <TableHead>Valor</TableHead>
                <TableHead>Método</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Vencimento</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Carregando faturas...
                  </TableCell>
                </TableRow>
              ) : invoices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Nenhuma fatura encontrada
                  </TableCell>
                </TableRow>
              ) : (
                invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      {formatDate(invoice.created_at)}
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">
                          {invoice.description || 'Assinatura AutoParts'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          ID: {invoice.id.slice(0, 8)}...
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-semibold">
                          {formatCurrency(invoice.value)}
                        </p>
                        {invoice.net_value !== invoice.value && (
                          <p className="text-sm text-muted-foreground">
                            Líquido: {formatCurrency(invoice.net_value)}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getBillingTypeIcon(invoice.billing_type)}
                        <span className="text-sm">
                          {getBillingTypeName(invoice.billing_type)}
                        </span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge className={getStatusColor(invoice.status)}>
                        {getStatusIcon(invoice.status)}
                        <span className="ml-1">{invoice.status}</span>
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">
                          {formatDate(invoice.due_date)}
                        </p>
                        {invoice.payment_date && (
                          <p className="text-sm text-muted-foreground">
                            Pago: {formatDate(invoice.payment_date)}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        {invoice.invoice_url && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownloadInvoice(invoice.id)}
                            disabled={downloadInvoiceMutation.isPending}
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                        )}
                        
                        {canRetryPayment(invoice.status) && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRetryPayment(invoice.asaas_payment_id)}
                            disabled={retryPaymentMutation.isPending}
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Página {page} de {totalPages}
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Anterior
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= totalPages}
            >
              Próxima
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
