"""
Redis cache configuration and utilities for AutoParts API.
"""
import json
import pickle
import hashlib
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import redis
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Redis cache manager with connection pooling and utilities.
    """
    
    def __init__(self):
        """Initialize Redis connection with connection pooling."""
        try:
            # Parse Redis URL
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            self.redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def is_available(self) -> bool:
        """Check if Redis is available."""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except Exception:
            return False
    
    def generate_cache_key(self, prefix: str, **kwargs) -> str:
        """
        Generate a cache key from prefix and parameters.
        
        Args:
            prefix: Cache key prefix
            **kwargs: Parameters to include in the key
            
        Returns:
            Generated cache key
        """
        # Sort kwargs for consistent key generation
        sorted_params = sorted(kwargs.items())
        params_str = "&".join([f"{k}={v}" for k, v in sorted_params if v is not None])
        
        # Create hash for long parameter strings
        if len(params_str) > 100:
            params_hash = hashlib.md5(params_str.encode()).hexdigest()
            return f"{prefix}:{params_hash}"
        
        return f"{prefix}:{params_str}" if params_str else prefix
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize: bool = True
    ) -> bool:
        """
        Set a value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            serialize: Whether to serialize the value
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_available():
            return False
        
        try:
            # Serialize value if needed
            if serialize:
                if isinstance(value, (dict, list)):
                    cached_value = json.dumps(value, default=str)
                else:
                    cached_value = pickle.dumps(value)
            else:
                cached_value = value
            
            # Set with TTL if provided
            if ttl:
                return self.redis_client.setex(key, ttl, cached_value)
            else:
                return self.redis_client.set(key, cached_value)
                
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    def get(
        self,
        key: str,
        deserialize: bool = True
    ) -> Optional[Any]:
        """
        Get a value from cache.
        
        Args:
            key: Cache key
            deserialize: Whether to deserialize the value
            
        Returns:
            Cached value or None if not found
        """
        if not self.is_available():
            return None
        
        try:
            cached_value = self.redis_client.get(key)
            
            if cached_value is None:
                return None
            
            # Deserialize value if needed
            if deserialize:
                try:
                    # Try JSON first
                    return json.loads(cached_value)
                except (json.JSONDecodeError, TypeError):
                    try:
                        # Try pickle
                        return pickle.loads(cached_value)
                    except (pickle.PickleError, TypeError):
                        # Return as string
                        return cached_value
            
            return cached_value
            
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """
        Delete a key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_available():
            return False
        
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching a pattern.
        
        Args:
            pattern: Pattern to match (e.g., "parts:*")
            
        Returns:
            Number of keys deleted
        """
        if not self.is_available():
            return 0
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Error deleting cache pattern {pattern}: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """
        Check if a key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists, False otherwise
        """
        if not self.is_available():
            return False
        
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
    
    def get_ttl(self, key: str) -> int:
        """
        Get time to live for a key.
        
        Args:
            key: Cache key
            
        Returns:
            TTL in seconds, -1 if no expiry, -2 if key doesn't exist
        """
        if not self.is_available():
            return -2
        
        try:
            return self.redis_client.ttl(key)
        except Exception as e:
            logger.error(f"Error getting TTL for cache key {key}: {e}")
            return -2
    
    def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """
        Increment a numeric value in cache.
        
        Args:
            key: Cache key
            amount: Amount to increment by
            
        Returns:
            New value after increment, None if error
        """
        if not self.is_available():
            return None
        
        try:
            return self.redis_client.incrby(key, amount)
        except Exception as e:
            logger.error(f"Error incrementing cache key {key}: {e}")
            return None
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get cache information and statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        if not self.is_available():
            return {"status": "unavailable"}
        
        try:
            info = self.redis_client.info()
            return {
                "status": "available",
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get("keyspace_hits", 0),
                    info.get("keyspace_misses", 0)
                )
            }
        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate percentage."""
        total = hits + misses
        if total == 0:
            return 0.0
        return round((hits / total) * 100, 2)
    
    def flush_all(self) -> bool:
        """
        Flush all cache data (use with caution).
        
        Returns:
            True if successful, False otherwise
        """
        if not self.is_available():
            return False
        
        try:
            self.redis_client.flushdb()
            logger.warning("Cache flushed - all data cleared")
            return True
        except Exception as e:
            logger.error(f"Error flushing cache: {e}")
            return False


# Global cache manager instance
cache_manager = CacheManager()


# Cache TTL constants (in seconds)
class CacheTTL:
    """Cache TTL constants for different data types."""
    PARTS_SEARCH = 15 * 60  # 15 minutes
    PART_DETAILS = 30 * 60  # 30 minutes
    DEALERSHIP_INFO = 60 * 60  # 1 hour
    INVENTORY_STATS = 10 * 60  # 10 minutes
    USER_SESSION = 30 * 60  # 30 minutes
    SEARCH_SUGGESTIONS = 60 * 60  # 1 hour
    POPULAR_SEARCHES = 24 * 60 * 60  # 24 hours


# Cache key prefixes
class CacheKeys:
    """Cache key prefixes for different data types."""
    PARTS_SEARCH = "parts:search"
    PART_DETAILS = "part:details"
    DEALERSHIP_INFO = "dealership:info"
    INVENTORY_STATS = "inventory:stats"
    USER_SESSION = "user:session"
    SEARCH_SUGGESTIONS = "search:suggestions"
    POPULAR_SEARCHES = "search:popular"
    INVENTORY_AVAILABILITY = "inventory:availability"
