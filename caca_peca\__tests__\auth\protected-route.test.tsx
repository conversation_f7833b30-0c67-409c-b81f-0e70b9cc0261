/**
 * Protected Route Component Tests
 * Tests for route protection and role-based access control
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ProtectedRoute, useRouteProtection } from '@/components/auth/protected-route';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/lib/types/api';

// Mock the auth context
vi.mock('@/lib/auth/context');
const mockUseAuth = vi.mocked(useAuth);

// Mock Next.js router
const mockPush = vi.fn();
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/protected',
    search: '',
  },
  writable: true,
});

describe('ProtectedRoute', () => {
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    full_name: 'Test User',
    role: UserRole.CUSTOMER,
    status: 'active' as const,
    is_active: true,
    email_verified: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: mockUser,
      hasRole: vi.fn((role) => mockUser.role === role),
      error: null,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      refreshUser: vi.fn(),
      clearError: vi.fn(),
      getRedirectPath: vi.fn(),
      isAdmin: vi.fn(() => mockUser.role === UserRole.ADMIN),
      isDealership: vi.fn(() => mockUser.role === UserRole.DEALERSHIP),
      isCustomer: vi.fn(() => mockUser.role === UserRole.CUSTOMER),
    });
  });

  it('should render children when user is authenticated', () => {
    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('should show loading state when authentication is loading', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isLoading: true,
      isAuthenticated: false,
      user: null,
    });

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText(/verificando autenticação/i)).toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('should redirect to login when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isAuthenticated: false,
      user: null,
    });

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    );

    expect(mockPush).toHaveBeenCalledWith('/auth?redirect=%2Fprotected');
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('should render children when user has required role', () => {
    render(
      <ProtectedRoute requiredRole={UserRole.CUSTOMER}>
        <div>Customer Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText('Customer Content')).toBeInTheDocument();
  });

  it('should show unauthorized when user lacks required role', () => {
    render(
      <ProtectedRoute requiredRole={UserRole.ADMIN}>
        <div>Admin Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText(/acesso negado/i)).toBeInTheDocument();
    expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
  });

  it('should handle multiple required roles', () => {
    render(
      <ProtectedRoute requiredRole={[UserRole.ADMIN, UserRole.DEALERSHIP]}>
        <div>Admin or Dealership Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText(/acesso negado/i)).toBeInTheDocument();
    expect(screen.queryByText('Admin or Dealership Content')).not.toBeInTheDocument();
  });

  it('should render custom loading component', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isLoading: true,
      isAuthenticated: false,
      user: null,
    });

    render(
      <ProtectedRoute loadingComponent={<div>Custom Loading</div>}>
        <div>Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText('Custom Loading')).toBeInTheDocument();
  });

  it('should render custom unauthorized component', () => {
    render(
      <ProtectedRoute 
        requiredRole={UserRole.ADMIN}
        unauthorizedComponent={<div>Custom Unauthorized</div>}
      >
        <div>Admin Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText('Custom Unauthorized')).toBeInTheDocument();
  });

  it('should redirect to unauthorized page when showUnauthorized is false', () => {
    render(
      <ProtectedRoute 
        requiredRole={UserRole.ADMIN}
        showUnauthorized={false}
      >
        <div>Admin Content</div>
      </ProtectedRoute>
    );

    expect(mockPush).toHaveBeenCalledWith('/unauthorized');
  });

  it('should use custom fallback path', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isAuthenticated: false,
      user: null,
    });

    render(
      <ProtectedRoute fallbackPath="/custom-login">
        <div>Protected Content</div>
      </ProtectedRoute>
    );

    expect(mockPush).toHaveBeenCalledWith('/custom-login?redirect=%2Fprotected');
  });
});

describe('useRouteProtection', () => {
  const TestComponent = () => {
    const { canAccess, getAccessLevel } = useRouteProtection();
    
    return (
      <div>
        <div>Can access admin: {canAccess(UserRole.ADMIN) ? 'yes' : 'no'}</div>
        <div>Access level: {getAccessLevel()}</div>
      </div>
    );
  };

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: {
        id: '1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: UserRole.DEALERSHIP,
        status: 'active' as const,
        is_active: true,
        email_verified: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
      hasRole: vi.fn((role) => role === UserRole.DEALERSHIP),
      error: null,
      isLoading: false,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      refreshUser: vi.fn(),
      clearError: vi.fn(),
      getRedirectPath: vi.fn(),
      isAdmin: vi.fn(() => false),
      isDealership: vi.fn(() => true),
      isCustomer: vi.fn(() => false),
    });
  });

  it('should return correct access information', () => {
    render(<TestComponent />);

    expect(screen.getByText('Can access admin: no')).toBeInTheDocument();
    expect(screen.getByText('Access level: dealership')).toBeInTheDocument();
  });

  it('should return none access level when not authenticated', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isAuthenticated: false,
      user: null,
    });

    const TestComponent = () => {
      const { getAccessLevel } = useRouteProtection();
      return <div>Access level: {getAccessLevel()}</div>;
    };

    render(<TestComponent />);

    expect(screen.getByText('Access level: none')).toBeInTheDocument();
  });
});