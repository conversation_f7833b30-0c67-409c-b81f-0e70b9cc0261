"""
Dealership model for AutoParts API.
"""
from sqlalchemy import Column, String, Boolean, Text, Index
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Dealership(BaseModel):
    """
    Dealership model representing automotive dealerships.
    
    Attributes:
        name: Dealership business name
        trading_name: Legal trading name
        cnpj: Brazilian company registration number
        email: Contact email address
        phone: Contact phone number
        whatsapp: WhatsApp number for contact
        address: Street address
        neighborhood: Neighborhood/district
        city: City name
        state: State abbreviation (e.g., SP, RJ)
        zip_code: Postal code
        description: Dealership description
        responsible: Name of responsible person
        subscription_id: Asaas subscription ID
        is_active: Whether the dealership is active
    """
    __tablename__ = "dealerships"

    # Basic Information
    name = Column(
        String(255),
        nullable=False,
        doc="Dealership business name"
    )
    
    trading_name = Column(
        String(255),
        nullable=True,
        doc="Legal trading name"
    )
    
    cnpj = Column(
        String(18),
        unique=True,
        nullable=False,
        index=True,
        doc="Brazilian company registration number (CNPJ)"
    )

    # Contact Information
    email = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Contact email address"
    )
    
    phone = Column(
        String(20),
        nullable=True,
        doc="Contact phone number"
    )
    
    whatsapp = Column(
        String(20),
        nullable=True,
        doc="WhatsApp number for contact"
    )

    # Address Information
    address = Column(
        String(500),
        nullable=True,
        doc="Street address"
    )
    
    neighborhood = Column(
        String(100),
        nullable=True,
        doc="Neighborhood or district"
    )
    
    city = Column(
        String(100),
        nullable=True,
        doc="City name"
    )
    
    state = Column(
        String(2),
        nullable=True,
        doc="State abbreviation (e.g., SP, RJ)"
    )
    
    zip_code = Column(
        String(10),
        nullable=True,
        doc="Postal code"
    )

    # Additional Information
    description = Column(
        Text,
        nullable=True,
        doc="Dealership description"
    )
    
    responsible = Column(
        String(255),
        nullable=True,
        doc="Name of responsible person"
    )

    # Business Information
    subscription_id = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Asaas subscription ID"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether the dealership is active"
    )

    # Relationships
    inventories = relationship(
        "Inventory",
        back_populates="dealership",
        cascade="all, delete-orphan"
    )

    subscriptions = relationship(
        "Subscription",
        back_populates="dealership",
        cascade="all, delete-orphan"
    )

    users = relationship(
        "User",
        back_populates="dealership",
        foreign_keys="User.dealership_id"
    )

    def __repr__(self):
        return f"<Dealership(id={self.id}, name='{self.name}', cnpj='{self.cnpj}')>"


# Create indexes for performance optimization
Index('idx_dealership_name', Dealership.name)
Index('idx_dealership_city_state', Dealership.city, Dealership.state)
Index('idx_dealership_active', Dealership.is_active)
