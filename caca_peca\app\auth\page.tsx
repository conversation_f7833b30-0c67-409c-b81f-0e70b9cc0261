"use client"

/**
 * Auth Page - Combined Login/Register with Tabs
 * Enhanced version using new auth components
 */

'use client';

import React, { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoginForm } from '@/components/auth/login-form';
import { RegisterForm } from '@/components/auth/register-form';
import { UserRole } from '@/lib/types/api';

export default function AuthPage() {
  const searchParams = useSearchParams();
  const defaultTab = searchParams.get('register') ? 'register' : 'login';
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleRegistrationSuccess = () => {
    setActiveTab('login');
  };

  return (
    <div className="container flex min-h-screen flex-col items-center justify-center py-8">
      <Link href="/" className="mb-8 flex items-center space-x-2">
        <ShoppingCart className="h-6 w-6 text-primary" />
        <span className="text-xl font-bold">AutoParts</span>
      </Link>

      <Tabs 
        defaultValue={activeTab} 
        className="w-full max-w-md" 
        onValueChange={setActiveTab}
        value={activeTab}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="login">Entrar</TabsTrigger>
          <TabsTrigger value="register">Cadastrar</TabsTrigger>
        </TabsList>

        <TabsContent value="login">
          <LoginForm showTestCredentials={true} />
          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>
              Não tem uma conta?{' '}
              <button 
                onClick={() => setActiveTab('register')} 
                className="text-primary hover:underline"
              >
                Cadastre-se
              </button>
            </p>
          </div>
        </TabsContent>

        <TabsContent value="register">
          <RegisterForm 
            onSuccess={handleRegistrationSuccess}
            defaultRole={UserRole.CUSTOMER}
            showRoleSelection={true}
          />
          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>
              Já tem uma conta?{' '}
              <button 
                onClick={() => setActiveTab('login')} 
                className="text-primary hover:underline"
              >
                Faça login
              </button>
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

