# AutoParts API Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-in-production-make-it-long-and-random
API_PORT=8000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database
POSTGRES_DB=autoparts
POSTGRES_USER=autoparts
POSTGRES_PASSWORD=autoparts123
POSTGRES_PORT=5432
DATABASE_URL=postgresql://autoparts:autoparts123@localhost:5432/autoparts

# For Docker Compose
DATABASE_HOST=postgres
DATABASE_PORT=5432

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-make-it-different-from-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings
ALLOWED_HOSTS=localhost,127.0.0.1,api.autoparts.com
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,https://autoparts.com

# =============================================================================
# ASAAS PAYMENT INTEGRATION
# =============================================================================
ASAAS_API_KEY=your-asaas-api-key-here
ASAAS_BASE_URL=https://www.asaas.com/api/v3
ASAAS_ENVIRONMENT=sandbox
ASAAS_WEBHOOK_SECRET=your-webhook-secret

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=xlsx,xls,csv

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
EMAIL_FROM=<EMAIL>

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FILE=logs/autoparts.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
# Gunicorn Workers (for production)
WORKERS=4
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
KEEP_ALIVE=5

# =============================================================================
# CELERY CONFIGURATION (Background Tasks)
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=America/Sao_Paulo

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# =============================================================================
# NGINX CONFIGURATION
# =============================================================================
HTTP_PORT=80
HTTPS_PORT=443

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Create sample data on startup
CREATE_SAMPLE_DATA=false

# Enable SQL query logging
SQL_ECHO=false

# Enable API documentation
ENABLE_DOCS=true

# =============================================================================
# PRODUCTION SETTINGS (Override in production)
# =============================================================================
# ENVIRONMENT=production
# DEBUG=false
# SECRET_KEY=generate-a-very-long-random-secret-key-for-production
# JWT_SECRET_KEY=generate-a-different-very-long-random-jwt-secret
# ASAAS_ENVIRONMENT=production
# ASAAS_API_KEY=your-production-asaas-api-key
# LOG_LEVEL=warning
# WORKERS=8
# CREATE_SAMPLE_DATA=false
# SQL_ECHO=false
# ENABLE_DOCS=false

# =============================================================================
# SSL CERTIFICATES (for HTTPS)
# =============================================================================
SSL_CERT_PATH=./nginx/ssl/autoparts.crt
SSL_KEY_PATH=./nginx/ssl/autoparts.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=autoparts-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
