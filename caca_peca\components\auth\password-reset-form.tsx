/**
 * Password Reset Form Component
 * Handles password reset request and confirmation
 */

'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { PasswordStrength } from '@/components/password-strength';
import { authService } from '@/lib/api/auth';
import { isValidEmail, validatePassword } from '@/lib/auth/utils';

type ResetStep = 'request' | 'confirm' | 'success';

export function PasswordResetForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [step, setStep] = useState<ResetStep>(token ? 'confirm' : 'request');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Request form state
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');

  // Confirm form state
  const [confirmForm, setConfirmForm] = useState({
    password: '',
    confirmPassword: '',
  });
  const [confirmErrors, setConfirmErrors] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setEmailError('');

    // Validate email
    if (!email) {
      setEmailError('E-mail é obrigatório');
      return;
    }
    if (!isValidEmail(email)) {
      setEmailError('E-mail inválido');
      return;
    }

    setIsLoading(true);

    try {
      const response = await authService.requestPasswordReset(email);
      
      if (response.success) {
        setSuccess('Instruções de recuperação foram enviadas para seu e-mail');
        setStep('success');
      } else {
        setError(response.error?.message || 'Erro ao solicitar recuperação de senha');
      }
    } catch (err) {
      setError('Erro interno. Tente novamente mais tarde.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setConfirmErrors({ password: '', confirmPassword: '' });

    // Validate form
    const errors = { password: '', confirmPassword: '' };
    
    const passwordValidation = validatePassword(confirmForm.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0] || 'Senha inválida';
    }

    if (!confirmForm.confirmPassword) {
      errors.confirmPassword = 'Confirmação de senha é obrigatória';
    } else if (confirmForm.password !== confirmForm.confirmPassword) {
      errors.confirmPassword = 'Senhas não coincidem';
    }

    if (errors.password || errors.confirmPassword) {
      setConfirmErrors(errors);
      return;
    }

    if (!token) {
      setError('Token de recuperação inválido');
      return;
    }

    setIsLoading(true);

    try {
      const response = await authService.confirmPasswordReset(token, confirmForm.password);
      
      if (response.success) {
        setSuccess('Senha alterada com sucesso!');
        setStep('success');
      } else {
        setError(response.error?.message || 'Erro ao alterar senha');
      }
    } catch (err) {
      setError('Erro interno. Tente novamente mais tarde.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof confirmForm) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    setConfirmForm(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (confirmErrors[field]) {
      setConfirmErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // Clear global error
    if (error) {
      setError('');
    }
  };

  // Success step
  if (step === 'success') {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
            <h3 className="text-lg font-semibold">
              {token ? 'Senha alterada com sucesso!' : 'E-mail enviado!'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {token 
                ? 'Sua senha foi alterada. Você pode fazer login com a nova senha.'
                : success || 'Verifique sua caixa de entrada e siga as instruções para recuperar sua senha.'
              }
            </p>
            <Button 
              onClick={() => router.push('/auth')}
              className="w-full"
            >
              Ir para Login
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Request reset step
  if (step === 'request') {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Recuperar Senha</CardTitle>
          <CardDescription>
            Digite seu e-mail para receber instruções de recuperação
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleRequestReset}>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">E-mail</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (emailError) setEmailError('');
                  if (error) setError('');
                }}
                className={emailError ? 'border-red-500' : ''}
                disabled={isLoading}
                autoComplete="email"
              />
              {emailError && (
                <p className="text-sm text-red-500">{emailError}</p>
              )}
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                'Enviar Instruções'
              )}
            </Button>

            <Link 
              href="/auth" 
              className="flex items-center justify-center text-sm text-muted-foreground hover:text-primary"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar para Login
            </Link>
          </CardFooter>
        </form>
      </Card>
    );
  }

  // Confirm reset step
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Nova Senha</CardTitle>
        <CardDescription>
          Digite sua nova senha
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleConfirmReset}>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* New Password */}
          <div className="space-y-2">
            <Label htmlFor="password">Nova Senha</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Digite sua nova senha"
                value={confirmForm.password}
                onChange={handleInputChange('password')}
                className={confirmErrors.password ? 'border-red-500 pr-10' : 'pr-10'}
                disabled={isLoading}
                autoComplete="new-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {confirmErrors.password && (
              <p className="text-sm text-red-500">{confirmErrors.password}</p>
            )}
            <PasswordStrength password={confirmForm.password} />
          </div>

          {/* Confirm New Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirme sua nova senha"
                value={confirmForm.confirmPassword}
                onChange={handleInputChange('confirmPassword')}
                className={confirmErrors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
                disabled={isLoading}
                autoComplete="new-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {confirmErrors.confirmPassword && (
              <p className="text-sm text-red-500">{confirmErrors.confirmPassword}</p>
            )}
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Alterando...
              </>
            ) : (
              'Alterar Senha'
            )}
          </Button>

          <Link 
            href="/auth" 
            className="flex items-center justify-center text-sm text-muted-foreground hover:text-primary"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar para Login
          </Link>
        </CardFooter>
      </form>
    </Card>
  );
}