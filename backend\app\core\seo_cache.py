"""
SEO Cache Management
Handles caching of SEO pages, meta tags, and structured data for better performance.
"""

import json
import hashlib
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.core.cache import CacheManager
from app.core.logging import get_logger
from app.models.part import Part
from app.models.inventory import Inventory

logger = get_logger("app.core.seo_cache")


class SEOCache:
    """Manages caching for SEO-related data and pages."""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.cache_prefix = "seo:"
        self.default_ttl = 3600  # 1 hour
        self.sitemap_ttl = 86400  # 24 hours
        self.meta_ttl = 1800  # 30 minutes
    
    def _get_cache_key(self, key_type: str, identifier: str) -> str:
        """Generate cache key for SEO data."""
        return f"{self.cache_prefix}{key_type}:{identifier}"
    
    def _hash_content(self, content: str) -> str:
        """Generate hash for content to detect changes."""
        return hashlib.md5(content.encode()).hexdigest()
    
    def cache_part_page(self, part_number: str, html_content: str, ttl: Optional[int] = None) -> bool:
        """Cache HTML page for a part."""
        try:
            cache_key = self._get_cache_key("part_page", part_number)
            content_hash = self._hash_content(html_content)

            cache_data = {
                "html": html_content,
                "hash": content_hash,
                "cached_at": datetime.utcnow().isoformat(),
                "ttl": ttl or self.default_ttl
            }

            success = self.cache_manager.set(
                cache_key,
                json.dumps(cache_data),
                ttl or self.default_ttl
            )

            if success:
                logger.info(f"Cached SEO page for part {part_number}")
            return success

        except Exception as e:
            logger.error(f"Error caching part page {part_number}: {e}")
            return False
    
    def get_cached_part_page(self, part_number: str) -> Optional[str]:
        """Get cached HTML page for a part."""
        try:
            cache_key = self._get_cache_key("part_page", part_number)
            cached_data = self.cache_manager.get(cache_key)

            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"Retrieved cached SEO page for part {part_number}")
                return data.get("html")

            return None

        except Exception as e:
            logger.error(f"Error retrieving cached part page {part_number}: {e}")
            return None
    
    def cache_meta_tags(self, identifier: str, meta_tags: Dict[str, str], ttl: Optional[int] = None) -> bool:
        """Cache meta tags for a resource."""
        try:
            cache_key = self._get_cache_key("meta", identifier)

            cache_data = {
                "meta_tags": meta_tags,
                "cached_at": datetime.utcnow().isoformat()
            }

            success = self.cache_manager.set(
                cache_key,
                json.dumps(cache_data),
                ttl or self.meta_ttl
            )

            return success

        except Exception as e:
            logger.error(f"Error caching meta tags for {identifier}: {e}")
            return False

    def get_cached_meta_tags(self, identifier: str) -> Optional[Dict[str, str]]:
        """Get cached meta tags for a resource."""
        try:
            cache_key = self._get_cache_key("meta", identifier)
            cached_data = self.cache_manager.get(cache_key)

            if cached_data:
                data = json.loads(cached_data)
                return data.get("meta_tags")

            return None

        except Exception as e:
            logger.error(f"Error retrieving cached meta tags for {identifier}: {e}")
            return None
    
    def cache_structured_data(self, identifier: str, structured_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Cache structured data for a resource."""
        try:
            cache_key = self._get_cache_key("structured", identifier)

            cache_data = {
                "structured_data": structured_data,
                "cached_at": datetime.utcnow().isoformat()
            }

            success = self.cache_manager.set(
                cache_key,
                json.dumps(cache_data),
                ttl or self.default_ttl
            )

            return success

        except Exception as e:
            logger.error(f"Error caching structured data for {identifier}: {e}")
            return False

    def get_cached_structured_data(self, identifier: str) -> Optional[Dict[str, Any]]:
        """Get cached structured data for a resource."""
        try:
            cache_key = self._get_cache_key("structured", identifier)
            cached_data = self.cache_manager.get(cache_key)

            if cached_data:
                data = json.loads(cached_data)
                return data.get("structured_data")

            return None

        except Exception as e:
            logger.error(f"Error retrieving cached structured data for {identifier}: {e}")
            return None

    def cache_sitemap(self, sitemap_content: str, ttl: Optional[int] = None) -> bool:
        """Cache sitemap XML content."""
        try:
            cache_key = self._get_cache_key("sitemap", "main")

            cache_data = {
                "content": sitemap_content,
                "generated_at": datetime.utcnow().isoformat()
            }

            success = self.cache_manager.set(
                cache_key,
                json.dumps(cache_data),
                ttl or self.sitemap_ttl
            )

            if success:
                logger.info("Cached sitemap content")
            return success

        except Exception as e:
            logger.error(f"Error caching sitemap: {e}")
            return False

    def get_cached_sitemap(self) -> Optional[str]:
        """Get cached sitemap content."""
        try:
            cache_key = self._get_cache_key("sitemap", "main")
            cached_data = self.cache_manager.get(cache_key)

            if cached_data:
                data = json.loads(cached_data)
                logger.info("Retrieved cached sitemap")
                return data.get("content")

            return None

        except Exception as e:
            logger.error(f"Error retrieving cached sitemap: {e}")
            return None
    
    def invalidate_part_cache(self, part_number: str) -> bool:
        """Invalidate cache for a specific part."""
        try:
            keys_to_delete = [
                self._get_cache_key("part_page", part_number),
                self._get_cache_key("meta", part_number),
                self._get_cache_key("structured", part_number)
            ]

            success = True
            for key in keys_to_delete:
                if not self.cache_manager.delete(key):
                    success = False

            # Also invalidate sitemap since part data changed
            self.cache_manager.delete(self._get_cache_key("sitemap", "main"))

            if success:
                logger.info(f"Invalidated cache for part {part_number}")
            return success

        except Exception as e:
            logger.error(f"Error invalidating cache for part {part_number}: {e}")
            return False

    def invalidate_all_seo_cache(self) -> bool:
        """Invalidate all SEO cache."""
        try:
            # For simplicity, just delete common cache keys
            # In a real implementation, you'd use pattern matching if available
            success = self.cache_manager.clear()

            if success:
                logger.info("Invalidated all SEO cache")
            return success

        except Exception as e:
            logger.error(f"Error invalidating all SEO cache: {e}")
            return False

    def warm_popular_parts_cache(self, db: Session, limit: int = 100) -> int:
        """Pre-warm cache for popular parts."""
        try:
            # Get parts with most inventory items (popular parts)
            popular_parts = db.query(Part).join(Inventory).group_by(Part.id).order_by(
                db.func.count(Inventory.id).desc()
            ).limit(limit).all()

            warmed_count = 0

            for part in popular_parts:
                # Pre-generate meta tags and structured data
                inventory_count = db.query(Inventory).filter(
                    Inventory.part_id == part.id,
                    Inventory.is_available == True
                ).count()

                # Generate and cache meta tags
                meta_tags = self._generate_part_meta_tags(part, inventory_count)
                self.cache_meta_tags(part.part_number, meta_tags)

                # Generate and cache structured data
                inventory_items = db.query(Inventory).filter(
                    Inventory.part_id == part.id,
                    Inventory.is_available == True
                ).all()

                structured_data = self._generate_part_structured_data(part, inventory_items)
                self.cache_structured_data(part.part_number, structured_data)

                warmed_count += 1

            logger.info(f"Warmed cache for {warmed_count} popular parts")
            return warmed_count

        except Exception as e:
            logger.error(f"Error warming popular parts cache: {e}")
            return 0

    def _generate_part_meta_tags(self, part: Part, inventory_count: int = 0) -> Dict[str, str]:
        """Generate meta tags for a specific part."""
        title = f"{part.name} - {part.brand} | Peça Original {part.part_number}"
        description = f"Encontre {part.name} da {part.brand} (código {part.part_number}). {inventory_count} ofertas disponíveis. Peças originais com garantia."

        return {
            "title": title,
            "description": description,
            "keywords": f"{part.name}, {part.brand}, {part.part_number}, peças automotivas, peças originais",
            "og:title": title,
            "og:description": description,
            "og:type": "product",
            "og:url": f"/parts/{part.part_number}",
            "twitter:card": "summary_large_image",
            "twitter:title": title,
            "twitter:description": description,
        }

    def _generate_part_structured_data(self, part: Part, inventory_items: List[Inventory]) -> Dict[str, Any]:
        """Generate structured data for a part with inventory."""
        offers = []
        for item in inventory_items:
            offers.append({
                "@type": "Offer",
                "price": str(item.price),
                "priceCurrency": "BRL",
                "availability": "https://schema.org/InStock" if item.is_available else "https://schema.org/OutOfStock",
                "condition": f"https://schema.org/{item.condition.value.title()}Condition",
                "seller": {
                    "@type": "Organization",
                    "name": item.dealership.name if item.dealership else "Concessionária"
                }
            })

        return {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": part.name,
            "description": part.description or f"Peça automotiva {part.name} da marca {part.brand}",
            "brand": {
                "@type": "Brand",
                "name": part.brand
            },
            "mpn": part.part_number,  # Manufacturer Part Number
            "category": part.category,
            "offers": offers,
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.5",
                "reviewCount": "10"
            } if offers else None
        }

    def warm_popular_searches_cache(self, db: Session, limit: int = 50) -> int:
        """Pre-warm cache for popular search terms."""
        try:
            # Get most common search terms (categories, brands, etc.)
            popular_categories = db.query(Part.category).filter(
                Part.category.isnot(None)
            ).group_by(Part.category).order_by(
                db.func.count(Part.category).desc()
            ).limit(limit).all()

            popular_brands = db.query(Part.brand).filter(
                Part.brand.isnot(None)
            ).group_by(Part.brand).order_by(
                db.func.count(Part.brand).desc()
            ).limit(limit).all()

            warmed_count = 0

            # Cache popular categories
            for (category,) in popular_categories:
                if category:
                    # In a real implementation, you'd generate the search page HTML here
                    logger.info(f"Would warm search cache for category: {category}")
                    warmed_count += 1

            # Cache popular brands
            for (brand,) in popular_brands:
                if brand:
                    # In a real implementation, you'd generate the search page HTML here
                    logger.info(f"Would warm search cache for brand: {brand}")
                    warmed_count += 1

            logger.info(f"Warmed search cache for {warmed_count} popular terms")
            return warmed_count

        except Exception as e:
            logger.error(f"Error warming popular searches cache: {e}")
            return 0

    def invalidate_category_cache(self, category: str) -> bool:
        """Invalidate cache for a specific category."""
        try:
            cache_key = self._get_cache_key("category", category.lower())
            success = self.cache_manager.delete(cache_key)

            if success:
                logger.info(f"Invalidated cache for category {category}")
            return success

        except Exception as e:
            logger.error(f"Error invalidating cache for category {category}: {e}")
            return False


# Global instance
seo_cache = SEOCache()
