version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: autoparts_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-autoparts}
      POSTGRES_USER: ${POSTGRES_USER:-autoparts}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-autoparts123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - autoparts_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-autoparts} -d ${POSTGRES_DB:-autoparts}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: autoparts_redis
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - autoparts_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # AutoParts API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: autoparts_api
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-autoparts}:${POSTGRES_PASSWORD:-autoparts123}@postgres:5432/${POSTGRES_DB:-autoparts}
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # Application
      ENVIRONMENT: ${ENVIRONMENT:-production}
      DEBUG: ${DEBUG:-false}
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      
      # Security
      ALLOWED_HOSTS: ${ALLOWED_HOSTS:-localhost,127.0.0.1,api.autoparts.com}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,https://autoparts.com}
      
      # JWT
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-your-jwt-secret-key}
      JWT_ALGORITHM: ${JWT_ALGORITHM:-HS256}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      
      # Asaas Integration
      ASAAS_API_KEY: ${ASAAS_API_KEY}
      ASAAS_BASE_URL: ${ASAAS_BASE_URL:-https://www.asaas.com/api/v3}
      ASAAS_ENVIRONMENT: ${ASAAS_ENVIRONMENT:-sandbox}
      
      # File Upload
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-10485760}
      UPLOAD_PATH: ${UPLOAD_PATH:-/app/uploads}
      
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-info}
      
      # Performance
      WORKERS: ${WORKERS:-4}
      WORKER_CONNECTIONS: ${WORKER_CONNECTIONS:-1000}
      MAX_REQUESTS: ${MAX_REQUESTS:-1000}
      
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
      - ./backend/alembic:/app/alembic
    ports:
      - "${API_PORT:-8000}:8000"
    networks:
      - autoparts_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["/app/start.sh"]

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: autoparts_nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
      - logs_data:/var/log/autoparts
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    networks:
      - autoparts_network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker (for background tasks)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: autoparts_celery_worker
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-autoparts}:${POSTGRES_PASSWORD:-autoparts123}@postgres:5432/${POSTGRES_DB:-autoparts}
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      
      # Application
      ENVIRONMENT: ${ENVIRONMENT:-production}
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      
      # Celery
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
      
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    networks:
      - autoparts_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["celery", "-A", "app.core.celery_app", "worker", "--loglevel=info", "--concurrency=2"]

  # Celery Beat (for scheduled tasks)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: autoparts_celery_beat
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-autoparts}:${POSTGRES_PASSWORD:-autoparts123}@postgres:5432/${POSTGRES_DB:-autoparts}
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      
      # Application
      ENVIRONMENT: ${ENVIRONMENT:-production}
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      
      # Celery
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
      
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    networks:
      - autoparts_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["celery", "-A", "app.core.celery_app", "beat", "--loglevel=info"]

  # Next.js Frontend
  frontend:
    build:
      context: ./caca_peca
      dockerfile: Dockerfile
    container_name: autoparts_frontend
    restart: unless-stopped
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_API_VERSION=v1
      - NEXT_PUBLIC_APP_NAME=AutoParts
      - NEXT_PUBLIC_APP_VERSION=1.0.0
      - NEXT_PUBLIC_ENVIRONMENT=development
    ports:
      - "3000:3000"
    networks:
      - autoparts_network
    depends_on:
      - api

# Networks
networks:
  autoparts_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local
