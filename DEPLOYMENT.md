# AutoParts API - Deployment Guide

This guide covers deployment configurations and procedures for the AutoParts API in different environments.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Git
- Make (optional, for convenience commands)

### Development Environment
```bash
# Clone the repository
git clone <repository-url>
cd autoparts-api

# Setup environment
make install
# or manually:
cp .env.example .env
# Edit .env with your configuration

# Start development environment
make dev
# or manually:
./scripts/deploy.sh development
```

### Production Environment
```bash
# Setup production environment file
cp .env.example .env
# Configure production values in .env

# Deploy to production
make prod
# or manually:
./scripts/deploy.sh production
```

## 📁 Project Structure

```
autoparts-api/
├── backend/                 # Python FastAPI application
│   ├── app/                # Application code
│   ├── tests/              # Test suite
│   ├── alembic/            # Database migrations
│   ├── Dockerfile          # Multi-stage Docker build
│   ├── requirements.txt    # Python dependencies
│   └── start.sh           # Container startup script
├── nginx/                  # Nginx configuration
│   ├── nginx.conf         # Main Nginx config
│   ├── conf.d/            # Server configurations
│   └── ssl/               # SSL certificates
├── scripts/               # Deployment and maintenance scripts
│   ├── deploy.sh          # Main deployment script
│   ├── backup.sh          # Backup script
│   └── monitor.sh         # Health monitoring
├── docker-compose.yml     # Production compose file
├── docker-compose.dev.yml # Development compose file
├── .env.example           # Environment template
└── Makefile              # Convenience commands
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure the following:

#### Application Settings
```bash
ENVIRONMENT=production          # development, staging, production
DEBUG=false                    # Enable debug mode
SECRET_KEY=your-secret-key     # Application secret key
API_PORT=8000                  # API port
```

#### Database Configuration
```bash
POSTGRES_DB=autoparts
POSTGRES_USER=autoparts
POSTGRES_PASSWORD=secure-password
DATABASE_URL=********************************/db
```

#### Redis Configuration
```bash
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
```

#### Security Settings
```bash
JWT_SECRET_KEY=jwt-secret-key
ALLOWED_HOSTS=localhost,api.autoparts.com
CORS_ORIGINS=https://autoparts.com
```

#### External Services
```bash
ASAAS_API_KEY=your-asaas-api-key
ASAAS_ENVIRONMENT=production    # sandbox or production
```

### SSL/TLS Configuration

For production, place your SSL certificates in `nginx/ssl/`:
- `autoparts.crt` - SSL certificate
- `autoparts.key` - Private key

For development, self-signed certificates are generated automatically.

## 🐳 Docker Deployment

### Services Overview

The application consists of the following services:

1. **API** - FastAPI application (Python)
2. **PostgreSQL** - Primary database
3. **Redis** - Cache and session storage
4. **Nginx** - Reverse proxy and load balancer
5. **Celery Worker** - Background task processing
6. **Celery Beat** - Scheduled task management

### Production Deployment

```bash
# 1. Configure environment
cp .env.example .env
# Edit .env with production values

# 2. Deploy services
docker-compose up -d

# 3. Run migrations
docker-compose exec api alembic upgrade head

# 4. Check status
docker-compose ps
```

### Development Deployment

```bash
# Start development environment with additional tools
docker-compose -f docker-compose.dev.yml up -d

# Available development tools:
# - pgAdmin: http://localhost:8082
# - Redis Commander: http://localhost:8081
# - Mailhog: http://localhost:8025
```

## 🔄 Database Management

### Migrations

```bash
# Run migrations
make db-migrate
# or
docker-compose exec api alembic upgrade head

# Create new migration
docker-compose exec api alembic revision --autogenerate -m "Description"

# Rollback migration
make db-rollback
# or
docker-compose exec api alembic downgrade -1
```

### Backup and Restore

```bash
# Create backup
make backup
# or
./scripts/backup.sh

# Verify backup
./scripts/backup.sh verify

# Restore from backup
gunzip -c backup.sql.gz | docker exec -i autoparts_postgres psql -U autoparts -d autoparts
```

## 📊 Monitoring and Health Checks

### Health Monitoring

```bash
# Run health check
make monitor
# or
./scripts/monitor.sh

# Generate detailed report
make monitor-report
# or
./scripts/monitor.sh report
```

### Available Endpoints

- **Health Check**: `GET /health`
- **API Documentation**: `GET /docs`
- **Metrics**: `GET /metrics` (if enabled)

### Log Management

```bash
# View application logs
make logs
# or
docker-compose logs -f api

# View all service logs
make logs-all
# or
docker-compose logs -f

# View specific service logs
docker-compose logs -f postgres
docker-compose logs -f redis
docker-compose logs -f nginx
```

## 🔒 Security Considerations

### Production Security Checklist

- [ ] Use strong, unique passwords for all services
- [ ] Configure SSL/TLS certificates
- [ ] Set secure JWT secret keys
- [ ] Configure proper CORS origins
- [ ] Enable rate limiting in Nginx
- [ ] Use non-root user in containers
- [ ] Keep dependencies updated
- [ ] Configure firewall rules
- [ ] Enable audit logging
- [ ] Regular security scans

### Environment-Specific Settings

#### Development
- Debug mode enabled
- Relaxed CORS settings
- Self-signed SSL certificates
- Sample data creation
- Extended token expiration

#### Production
- Debug mode disabled
- Strict CORS settings
- Valid SSL certificates
- No sample data
- Short token expiration
- Enhanced logging

## 🚀 Scaling and Performance

### Horizontal Scaling

```bash
# Scale API instances
docker-compose up -d --scale api=3

# Scale Celery workers
docker-compose up -d --scale celery_worker=4
```

### Performance Tuning

#### Database Optimization
- Connection pooling configured
- Query optimization enabled
- Proper indexing
- Regular VACUUM and ANALYZE

#### Redis Configuration
- Memory optimization
- Persistence settings
- Connection limits

#### Nginx Configuration
- Gzip compression
- Static file caching
- Rate limiting
- Load balancing

## 🔧 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check service status
docker-compose ps

# Check logs
docker-compose logs service-name

# Restart service
docker-compose restart service-name
```

#### Database Connection Issues
```bash
# Check database connectivity
docker-compose exec api python -c "from app.core.database import engine; print(engine.execute('SELECT 1').scalar())"

# Check database logs
docker-compose logs postgres
```

#### Performance Issues
```bash
# Check resource usage
docker stats

# Monitor system resources
./scripts/monitor.sh performance

# Check slow queries
docker-compose exec postgres psql -U autoparts -d autoparts -c "SELECT query, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
```

### Recovery Procedures

#### Database Recovery
```bash
# Stop services
docker-compose down

# Restore from backup
gunzip -c backup.sql.gz | docker exec -i autoparts_postgres psql -U autoparts -d autoparts

# Start services
docker-compose up -d
```

#### Full System Recovery
```bash
# Stop all services
docker-compose down -v

# Restore from full backup
tar -xzf full_backup.tar.gz
# Follow restore instructions in backup

# Restart services
docker-compose up -d
```

## 📋 Maintenance

### Regular Maintenance Tasks

#### Daily
- Monitor system health
- Check error logs
- Verify backups

#### Weekly
- Update dependencies
- Clean old logs
- Performance review

#### Monthly
- Security updates
- Backup verification
- Capacity planning

### Automated Maintenance

```bash
# Setup cron jobs for automated tasks
# Add to crontab:

# Daily backup at 2 AM
0 2 * * * /path/to/autoparts-api/scripts/backup.sh

# Health check every 30 minutes
*/30 * * * * /path/to/autoparts-api/scripts/monitor.sh

# Log cleanup weekly
0 3 * * 0 find /path/to/logs -name "*.log" -mtime +7 -delete
```

## 🆘 Support

### Getting Help

1. Check this documentation
2. Review application logs
3. Run health monitoring
4. Check GitHub issues
5. Contact development team

### Useful Commands Reference

```bash
# Quick commands with Make
make help          # Show all available commands
make dev           # Start development environment
make prod          # Deploy to production
make test          # Run tests
make backup        # Create backup
make monitor       # Health check
make logs          # View logs
make clean         # Cleanup

# Direct Docker commands
docker-compose ps                    # Service status
docker-compose logs -f api          # API logs
docker-compose exec api bash        # API shell
docker-compose restart api          # Restart API
docker-compose down -v              # Stop and remove volumes
```

This deployment guide provides comprehensive instructions for deploying and maintaining the AutoParts API in various environments. Follow the appropriate sections based on your deployment needs.
