version: '3.8'

services:
  # PostgreSQL Database for Staging
  postgres:
    image: postgres:15-alpine
    container_name: autoparts_postgres_staging
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-autoparts_staging}
      POSTGRES_USER: ${POSTGRES_USER:-autoparts}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-autoparts123}
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - autoparts_staging_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-autoparts} -d ${POSTGRES_DB:-autoparts_staging}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=100
      -c shared_buffers=128MB
      -c effective_cache_size=512MB

  # Redis Cache for Staging
  redis:
    image: redis:7-alpine
    container_name: autoparts_redis_staging
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_staging_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - autoparts_staging_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # AutoParts API for Staging
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: autoparts_api_staging
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-autoparts}:${POSTGRES_PASSWORD:-autoparts123}@postgres:5432/${POSTGRES_DB:-autoparts_staging}
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # Application
      ENVIRONMENT: staging
      DEBUG: false
      SECRET_KEY: ${SECRET_KEY:-staging-secret-key-change-me}
      
      # Security
      ALLOWED_HOSTS: ${ALLOWED_HOSTS:-localhost,127.0.0.1,staging-api.autoparts.com}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,https://staging.autoparts.com}
      
      # JWT
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-staging-jwt-secret-key}
      JWT_ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      
      # Asaas Integration (Sandbox for staging)
      ASAAS_API_KEY: ${ASAAS_API_KEY}
      ASAAS_BASE_URL: https://www.asaas.com/api/v3
      ASAAS_ENVIRONMENT: sandbox
      
      # File Upload
      MAX_FILE_SIZE: 10485760
      UPLOAD_PATH: /app/uploads
      
      # Logging
      LOG_LEVEL: info
      
      # Performance (reduced for staging)
      WORKERS: 2
      WORKER_CONNECTIONS: 500
      MAX_REQUESTS: 500
      
    volumes:
      - uploads_staging_data:/app/uploads
      - logs_staging_data:/app/logs
    ports:
      - "${API_PORT:-8000}:8000"
    networks:
      - autoparts_staging_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx for Staging
  nginx:
    image: nginx:alpine
    container_name: autoparts_nginx_staging
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_staging_data:/var/www/uploads:ro
      - logs_staging_data:/var/log/autoparts
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    networks:
      - autoparts_staging_network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Staging
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: autoparts_celery_worker_staging
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-autoparts}:${POSTGRES_PASSWORD:-autoparts123}@postgres:5432/${POSTGRES_DB:-autoparts_staging}
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      
      # Application
      ENVIRONMENT: staging
      SECRET_KEY: ${SECRET_KEY:-staging-secret-key-change-me}
      
      # Celery
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
      
    volumes:
      - uploads_staging_data:/app/uploads
      - logs_staging_data:/app/logs
    networks:
      - autoparts_staging_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["celery", "-A", "app.core.celery_app", "worker", "--loglevel=info", "--concurrency=1"]

  # Monitoring (Staging specific)
  prometheus:
    image: prom/prometheus:latest
    container_name: autoparts_prometheus_staging
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_staging_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - autoparts_staging_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: autoparts_grafana_staging
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_staging_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    networks:
      - autoparts_staging_network
    depends_on:
      - prometheus

# Networks
networks:
  autoparts_staging_network:
    driver: bridge

# Volumes
volumes:
  postgres_staging_data:
    driver: local
  redis_staging_data:
    driver: local
  uploads_staging_data:
    driver: local
  logs_staging_data:
    driver: local
  prometheus_staging_data:
    driver: local
  grafana_staging_data:
    driver: local
