"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star, 
  Package, 
  Truck,
  Shield,
  Info,
  FileText,
  Users,
  BarChart3,
  Loader2,
  AlertCircle
} from "lucide-react"
import { 
  usePart, 
  usePartSpecifications, 
  useRelatedParts, 
  useAlternativeParts,
  usePartAvailability,
  usePartPriceHistory,
  usePartReviews,
  useTrackPartView
} from "@/lib/hooks/useParts"
import { CompatibilityChecker } from "@/components/parts/compatibility-checker"
import { formatCurrency } from "@/lib/utils"

export default function PartDetailPage() {
  const params = useParams()
  const partId = params.id as string
  const [selectedTab, setSelectedTab] = useState("overview")
  
  // Track part view
  const trackViewMutation = useTrackPartView()
  
  // API hooks
  const { data: partResponse, isLoading: partLoading, error: partError } = usePart(partId)
  const { data: specificationsResponse } = usePartSpecifications(partId)
  const { data: relatedPartsResponse } = useRelatedParts(partId, 6)
  const { data: alternativePartsResponse } = useAlternativeParts(partId)
  const { data: availabilityResponse } = usePartAvailability(partId)
  const { data: priceHistoryResponse } = usePartPriceHistory(partId, 30)
  const { data: reviewsResponse } = usePartReviews(partId, 1, 5)

  // Extract data from API responses
  const part = partResponse?.data?.success ? partResponse.data.data : null
  const specifications = specificationsResponse?.data?.success ? specificationsResponse.data.data : null
  const relatedParts = relatedPartsResponse?.data?.success ? relatedPartsResponse.data.data : []
  const alternativeParts = alternativePartsResponse?.data?.success ? alternativePartsResponse.data.data : []
  const availability = availabilityResponse?.data?.success ? availabilityResponse.data.data : []
  const priceHistory = priceHistoryResponse?.data?.success ? priceHistoryResponse.data.data : []
  const reviews = reviewsResponse?.data?.success ? reviewsResponse.data.data : null

  // Track view when component mounts
  useEffect(() => {
    if (partId) {
      trackViewMutation.mutate(partId)
    }
  }, [partId])

  if (partLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto" />
            <p>Carregando detalhes da peça...</p>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (partError || !part) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4">
            <AlertCircle className="h-12 w-12 mx-auto text-destructive" />
            <h1 className="text-2xl font-bold">Peça não encontrada</h1>
            <p className="text-muted-foreground">
              A peça solicitada não foi encontrada ou não está disponível.
            </p>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1">
        <div className="container py-8">
          {/* Breadcrumb */}
          <nav className="mb-6 text-sm text-muted-foreground">
            <span>Início</span> / <span>Buscar</span> / <span className="text-foreground">{part.name}</span>
          </nav>

          {/* Part Header */}
          <div className="grid gap-8 lg:grid-cols-2 mb-8">
            {/* Part Image */}
            <div className="space-y-4">
              <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                <Package className="h-24 w-24 text-muted-foreground" />
              </div>
              <div className="grid grid-cols-4 gap-2">
                {Array.from({ length: 4 }, (_, i) => (
                  <div key={i} className="aspect-square bg-muted rounded border cursor-pointer hover:border-primary">
                    <div className="w-full h-full flex items-center justify-center">
                      <Package className="h-6 w-6 text-muted-foreground" />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Part Info */}
            <div className="space-y-6">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">{part.part_number}</Badge>
                  <Badge variant="secondary">{part.brand}</Badge>
                </div>
                <h1 className="text-3xl font-bold mb-2">{part.name}</h1>
                <p className="text-muted-foreground">{part.description}</p>
              </div>

              <div className="space-y-4">
                <div className="text-3xl font-bold text-primary">
                  {formatCurrency(part.price || 0)}
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    {Array.from({ length: 5 }, (_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < 4 ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="text-sm text-muted-foreground ml-1">(4.2)</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {reviews?.total || 0} avaliações
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  {part.is_available ? (
                    <Badge className="bg-green-100 text-green-800">Em estoque</Badge>
                  ) : (
                    <Badge variant="destructive">Indisponível</Badge>
                  )}
                  <span className="text-sm text-muted-foreground">
                    Categoria: {part.category}
                  </span>
                </div>
              </div>

              <div className="flex gap-3">
                <Button size="lg" className="flex-1">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Adicionar ao Carrinho
                </Button>
                <Button variant="outline" size="lg">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="lg">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>

              {/* Quick Info */}
              <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                <div className="text-center">
                  <Truck className="h-5 w-5 mx-auto mb-1 text-muted-foreground" />
                  <p className="text-xs text-muted-foreground">Entrega</p>
                  <p className="text-sm font-medium">5-7 dias</p>
                </div>
                <div className="text-center">
                  <Shield className="h-5 w-5 mx-auto mb-1 text-muted-foreground" />
                  <p className="text-xs text-muted-foreground">Garantia</p>
                  <p className="text-sm font-medium">12 meses</p>
                </div>
                <div className="text-center">
                  <Package className="h-5 w-5 mx-auto mb-1 text-muted-foreground" />
                  <p className="text-xs text-muted-foreground">Estoque</p>
                  <p className="text-sm font-medium">Disponível</p>
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Information Tabs */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                Visão Geral
              </TabsTrigger>
              <TabsTrigger value="specifications" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Especificações
              </TabsTrigger>
              <TabsTrigger value="compatibility" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Compatibilidade
              </TabsTrigger>
              <TabsTrigger value="availability" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Disponibilidade
              </TabsTrigger>
              <TabsTrigger value="reviews" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Avaliações
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Histórico
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Descrição Detalhada</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {part.description || "Descrição não disponível."}
                  </p>
                </CardContent>
              </Card>

              {/* Related Parts */}
              {relatedParts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Peças Relacionadas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {relatedParts.map((relatedPart) => (
                        <Card key={relatedPart.id} className="border-dashed">
                          <CardContent className="p-4">
                            <h4 className="font-medium mb-1">{relatedPart.name}</h4>
                            <p className="text-sm text-muted-foreground mb-2">
                              {relatedPart.part_number}
                            </p>
                            <p className="font-semibold text-primary">
                              {formatCurrency(relatedPart.price || 0)}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Alternative Parts */}
              {alternativeParts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Peças Alternativas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {alternativeParts.map((altPart) => (
                        <Card key={altPart.id} className="border-dashed">
                          <CardContent className="p-4">
                            <h4 className="font-medium mb-1">{altPart.name}</h4>
                            <p className="text-sm text-muted-foreground mb-2">
                              {altPart.part_number} • {altPart.brand}
                            </p>
                            <p className="font-semibold text-primary">
                              {formatCurrency(altPart.price || 0)}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="specifications">
              <Card>
                <CardHeader>
                  <CardTitle>Especificações Técnicas</CardTitle>
                </CardHeader>
                <CardContent>
                  {specifications ? (
                    <div className="space-y-6">
                      {specifications.technical_specs && (
                        <div>
                          <h4 className="font-semibold mb-3">Especificações</h4>
                          <div className="grid gap-2">
                            {Object.entries(specifications.technical_specs).map(([key, value]) => (
                              <div key={key} className="flex justify-between py-2 border-b">
                                <span className="font-medium">{key}</span>
                                <span className="text-muted-foreground">{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {specifications.dimensions && (
                        <div>
                          <h4 className="font-semibold mb-3">Dimensões</h4>
                          <div className="grid gap-2">
                            {Object.entries(specifications.dimensions).map(([key, value]) => (
                              <div key={key} className="flex justify-between py-2 border-b">
                                <span className="font-medium">{key}</span>
                                <span className="text-muted-foreground">{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {specifications.warranty_info && (
                        <div>
                          <h4 className="font-semibold mb-3">Informações de Garantia</h4>
                          <div className="p-4 bg-muted/50 rounded-lg">
                            <p><strong>Duração:</strong> {specifications.warranty_info.duration_months} meses</p>
                            <p><strong>Cobertura:</strong> {specifications.warranty_info.coverage}</p>
                            {specifications.warranty_info.terms && (
                              <p><strong>Termos:</strong> {specifications.warranty_info.terms}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">Especificações não disponíveis.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compatibility">
              <CompatibilityChecker part={part} />
            </TabsContent>

            <TabsContent value="availability">
              <Card>
                <CardHeader>
                  <CardTitle>Disponibilidade por Região</CardTitle>
                </CardHeader>
                <CardContent>
                  {availability.length > 0 ? (
                    <div className="space-y-4">
                      {availability.map((item, index) => (
                        <div key={index} className="flex justify-between items-center p-4 border rounded-lg">
                          <div>
                            <h4 className="font-medium">{item.dealership_name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {item.city}, {item.state}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{formatCurrency(item.price)}</p>
                            <p className="text-sm text-muted-foreground">
                              {item.quantity} em estoque
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">Informações de disponibilidade não encontradas.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews">
              <Card>
                <CardHeader>
                  <CardTitle>Avaliações dos Clientes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Sistema de avaliações em desenvolvimento.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics">
              <Card>
                <CardHeader>
                  <CardTitle>Histórico de Preços</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Gráfico de histórico de preços em desenvolvimento.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <Footer />
    </div>
  )
}
