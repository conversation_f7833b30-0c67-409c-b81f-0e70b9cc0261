'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  DollarSign,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { inventoryService, InventorySearchParams } from '@/lib/api/inventory';
import { Inventory } from '@/lib/types/api';

export default function InventoryPage() {
  const { user } = useAuth();
  const [inventory, setInventory] = useState<Inventory[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<Inventory | null>(null);

  // Search and filter state
  const [searchParams, setSearchParams] = useState<InventorySearchParams>({
    page: 1,
    size: 20,
    available_only: true
  });

  // Load inventory data
  const loadInventory = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await inventoryService.searchInventory(searchParams);

      if (response.success && response.data) {
        setInventory(response.data.items);
      } else {
        setError(response.error?.message || 'Erro ao carregar estoque');
      }
    } catch (error: any) {
      setError('Erro inesperado ao carregar estoque');
    } finally {
      setIsLoading(false);
    }
  };

  // Load inventory statistics
  const loadStats = async () => {
    try {
      const response = await inventoryService.getInventoryStats(
        user?.role === 'dealership' ? user.dealership_id : undefined
      );

      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  useEffect(() => {
    loadInventory();
    loadStats();
  }, [searchParams]);

  const handleSearch = (value: string) => {
    setSearchParams(prev => ({ ...prev, part_number: value, page: 1 }));
  };

  const handleFilterChange = (key: keyof InventorySearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      const response = await inventoryService.deleteInventory(itemToDelete.id);

      if (response.success) {
        await loadInventory();
        await loadStats();
        setDeleteDialogOpen(false);
        setItemToDelete(null);
      } else {
        setError(response.error?.message || 'Erro ao excluir item');
      }
    } catch (error: any) {
      setError('Erro inesperado ao excluir item');
    }
  };

  const getConditionBadge = (condition: string) => {
    const conditionConfig = {
      new: { label: 'Novo', variant: 'default' as const },
      used: { label: 'Usado', variant: 'secondary' as const },
      refurbished: { label: 'Recondicionado', variant: 'outline' as const }
    };

    const config = conditionConfig[condition as keyof typeof conditionConfig] || conditionConfig.new;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const getStockBadge = (item: Inventory) => {
    if (item.quantity === 0) {
      return <Badge variant="destructive">Sem Estoque</Badge>;
    }

    if (item.quantity <= (item.minimum_stock || 5)) {
      return <Badge variant="secondary">Estoque Baixo</Badge>;
    }

    return <Badge variant="default">Em Estoque</Badge>;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Gerenciar Estoque</h1>
          <p className="text-muted-foreground">
            Gerencie seu inventário de peças automotivas
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/painel/importar-estoque">
              <Package className="w-4 h-4 mr-2" />
              Importar
            </Link>
          </Button>
          <Button asChild>
            <Link href="/painel/estoque/novo">
              <Plus className="w-4 h-4 mr-2" />
              Adicionar Item
            </Link>
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Itens</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_items}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.total_value)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estoque Baixo</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.low_stock_items}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sem Estoque</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.out_of_stock_items}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Buscar por Número da Peça</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Digite o número da peça..."
                  className="pl-8"
                  value={searchParams.part_number || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Marca</Label>
              <Input
                placeholder="Filtrar por marca..."
                value={searchParams.brand || ''}
                onChange={(e) => handleFilterChange('brand', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>Categoria</Label>
              <Input
                placeholder="Filtrar por categoria..."
                value={searchParams.category || ''}
                onChange={(e) => handleFilterChange('category', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>Condição</Label>
              <Select
                value={searchParams.condition || ''}
                onValueChange={(value) => handleFilterChange('condition', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todas as condições" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todas as condições</SelectItem>
                  <SelectItem value="new">Novo</SelectItem>
                  <SelectItem value="used">Usado</SelectItem>
                  <SelectItem value="refurbished">Recondicionado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>Itens do Estoque</CardTitle>
          <CardDescription>
            {inventory.length} itens encontrados
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <p className="text-muted-foreground">Carregando estoque...</p>
            </div>
          ) : inventory.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <p className="text-muted-foreground">Nenhum item encontrado</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Peça</TableHead>
                  <TableHead>Quantidade</TableHead>
                  <TableHead>Preço</TableHead>
                  <TableHead>Condição</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inventory.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.part?.part_number}</p>
                        <p className="text-sm text-muted-foreground">{item.part?.name}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.quantity}</p>
                        {item.minimum_stock && (
                          <p className="text-sm text-muted-foreground">
                            Min: {item.minimum_stock}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(item.price)}</TableCell>
                    <TableCell>{getConditionBadge(item.condition)}</TableCell>
                    <TableCell>{getStockBadge(item)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button asChild variant="outline" size="sm">
                          <Link href={`/painel/estoque/${item.id}/editar`}>
                            <Edit className="w-4 h-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setItemToDelete(item);
                            setDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir este item do estoque? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          {itemToDelete && (
            <div className="py-4">
              <p><strong>Peça:</strong> {itemToDelete.part?.part_number}</p>
              <p><strong>Nome:</strong> {itemToDelete.part?.name}</p>
              <p><strong>Quantidade:</strong> {itemToDelete.quantity}</p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteItem}>
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
