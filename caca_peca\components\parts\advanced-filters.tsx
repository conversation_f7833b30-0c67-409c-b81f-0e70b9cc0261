"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  ChevronDown, 
  ChevronUp, 
  X, 
  Filter,
  Search,
  Car,
  Calendar,
  DollarSign,
  Package,
  MapPin
} from "lucide-react"
import { usePartCategories, usePartBrands, useVehicleMakes, useVehicleModels } from "@/lib/hooks/useParts"
import { PartSearchFilters } from "@/lib/types/api"

interface AdvancedFiltersProps {
  filters: PartSearchFilters
  onFiltersChange: (filters: PartSearchFilters) => void
  onApply: () => void
  onClear: () => void
  isOpen: boolean
  onToggle: () => void
}

export function AdvancedFilters({
  filters,
  onFiltersChange,
  onApply,
  onClear,
  isOpen,
  onToggle
}: AdvancedFiltersProps) {
  // Local state for form inputs
  const [localFilters, setLocalFilters] = useState<PartSearchFilters>(filters)
  const [expandedSections, setExpandedSections] = useState({
    search: true,
    vehicle: false,
    price: false,
    location: false,
    advanced: false
  })

  // API hooks
  const { data: categoriesResponse } = usePartCategories()
  const { data: brandsResponse } = usePartBrands()
  const { data: makesResponse } = useVehicleMakes()
  const { data: modelsResponse } = useVehicleModels(
    localFilters.vehicle_makes?.[0] || "", 
    !!localFilters.vehicle_makes?.[0]
  )

  // Extract data from API responses
  const categories = categoriesResponse?.data?.success ? categoriesResponse.data.data : []
  const brands = brandsResponse?.data?.success ? brandsResponse.data.data : []
  const makes = makesResponse?.data?.success ? makesResponse.data.data : []
  const models = modelsResponse?.data?.success ? modelsResponse.data.data : []

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const updateLocalFilters = (updates: Partial<PartSearchFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...updates }))
  }

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }))
  }

  const handleApply = () => {
    onFiltersChange(localFilters)
    onApply()
  }

  const handleClear = () => {
    const clearedFilters: PartSearchFilters = {
      page: 1,
      size: 20
    }
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
    onClear()
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (localFilters.search_terms?.length) count++
    if (localFilters.brands?.length) count++
    if (localFilters.categories?.length) count++
    if (localFilters.vehicle_makes?.length) count++
    if (localFilters.year_range) count++
    if (localFilters.price_range) count++
    if (localFilters.location?.state || localFilters.location?.city) count++
    if (localFilters.has_inventory !== undefined) count++
    return count
  }

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        onClick={onToggle}
        className="flex items-center gap-2"
      >
        <Filter className="h-4 w-4" />
        Filtros Avançados
        {getActiveFiltersCount() > 0 && (
          <Badge variant="secondary" className="ml-1">
            {getActiveFiltersCount()}
          </Badge>
        )}
        <ChevronDown className="h-4 w-4" />
      </Button>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros Avançados
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleClear}>
              <X className="h-4 w-4 mr-1" />
              Limpar
            </Button>
            <Button size="sm" onClick={handleApply}>
              Aplicar Filtros
            </Button>
            <Button variant="ghost" size="sm" onClick={onToggle}>
              <ChevronUp className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Search Terms Section */}
        <Collapsible 
          open={expandedSections.search} 
          onOpenChange={() => toggleSection('search')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <span className="font-medium">Termos de Busca</span>
            </div>
            {expandedSections.search ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </CollapsibleTrigger>
          <CollapsibleContent className="pt-4 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label>Marcas</Label>
                <Select
                  value={localFilters.brands?.[0] || ""}
                  onValueChange={(value) => 
                    updateLocalFilters({ brands: value ? [value] : undefined })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma marca" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas as marcas</SelectItem>
                    {brands.map((brand) => (
                      <SelectItem key={brand} value={brand}>
                        {brand}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Categorias</Label>
                <Select
                  value={localFilters.categories?.[0] || ""}
                  onValueChange={(value) => 
                    updateLocalFilters({ categories: value ? [value] : undefined })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas as categorias</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="has-inventory"
                checked={localFilters.has_inventory || false}
                onCheckedChange={(checked) => 
                  updateLocalFilters({ has_inventory: checked as boolean })
                }
              />
              <Label htmlFor="has-inventory">Apenas peças em estoque</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="in-stock-only"
                checked={localFilters.in_stock_only || false}
                onCheckedChange={(checked) => 
                  updateLocalFilters({ in_stock_only: checked as boolean })
                }
              />
              <Label htmlFor="in-stock-only">Apenas peças disponíveis</Label>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Vehicle Section */}
        <Collapsible 
          open={expandedSections.vehicle} 
          onOpenChange={() => toggleSection('vehicle')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded">
            <div className="flex items-center gap-2">
              <Car className="h-4 w-4" />
              <span className="font-medium">Veículo</span>
            </div>
            {expandedSections.vehicle ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </CollapsibleTrigger>
          <CollapsibleContent className="pt-4 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label>Marca do Veículo</Label>
                <Select
                  value={localFilters.vehicle_makes?.[0] || ""}
                  onValueChange={(value) => 
                    updateLocalFilters({ 
                      vehicle_makes: value ? [value] : undefined,
                      vehicle_models: undefined // Reset models when make changes
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma marca" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas as marcas</SelectItem>
                    {makes.map((make) => (
                      <SelectItem key={make} value={make}>
                        {make}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Modelo do Veículo</Label>
                <Select
                  value={localFilters.vehicle_models?.[0] || ""}
                  onValueChange={(value) => 
                    updateLocalFilters({ vehicle_models: value ? [value] : undefined })
                  }
                  disabled={!localFilters.vehicle_makes?.[0]}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um modelo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os modelos</SelectItem>
                    {models.map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Faixa de Anos</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Ano inicial</Label>
                  <Input
                    type="number"
                    placeholder="Ex: 2010"
                    value={localFilters.year_range?.start || ""}
                    onChange={(e) => 
                      updateLocalFilters({
                        year_range: {
                          start: parseInt(e.target.value) || 1990,
                          end: localFilters.year_range?.end || new Date().getFullYear()
                        }
                      })
                    }
                  />
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Ano final</Label>
                  <Input
                    type="number"
                    placeholder="Ex: 2024"
                    value={localFilters.year_range?.end || ""}
                    onChange={(e) => 
                      updateLocalFilters({
                        year_range: {
                          start: localFilters.year_range?.start || 1990,
                          end: parseInt(e.target.value) || new Date().getFullYear()
                        }
                      })
                    }
                  />
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Price Section */}
        <Collapsible 
          open={expandedSections.price} 
          onOpenChange={() => toggleSection('price')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              <span className="font-medium">Preço</span>
            </div>
            {expandedSections.price ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </CollapsibleTrigger>
          <CollapsibleContent className="pt-4 space-y-4">
            <div className="space-y-4">
              <div className="px-2">
                <Slider
                  value={[
                    localFilters.price_range?.min || 0,
                    localFilters.price_range?.max || 5000
                  ]}
                  min={0}
                  max={5000}
                  step={50}
                  onValueChange={([min, max]) => 
                    updateLocalFilters({ price_range: { min, max } })
                  }
                  className="py-4"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Preço mínimo</Label>
                  <Input
                    type="number"
                    placeholder="R$ 0"
                    value={localFilters.price_range?.min || ""}
                    onChange={(e) => 
                      updateLocalFilters({
                        price_range: {
                          min: parseFloat(e.target.value) || 0,
                          max: localFilters.price_range?.max || 5000
                        }
                      })
                    }
                  />
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Preço máximo</Label>
                  <Input
                    type="number"
                    placeholder="R$ 5000"
                    value={localFilters.price_range?.max || ""}
                    onChange={(e) => 
                      updateLocalFilters({
                        price_range: {
                          min: localFilters.price_range?.min || 0,
                          max: parseFloat(e.target.value) || 5000
                        }
                      })
                    }
                  />
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Location Section */}
        <Collapsible 
          open={expandedSections.location} 
          onOpenChange={() => toggleSection('location')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <span className="font-medium">Localização</span>
            </div>
            {expandedSections.location ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </CollapsibleTrigger>
          <CollapsibleContent className="pt-4 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label>Estado</Label>
                <Input
                  placeholder="Ex: SP"
                  value={localFilters.location?.state || ""}
                  onChange={(e) => 
                    updateLocalFilters({
                      location: {
                        ...localFilters.location,
                        state: e.target.value
                      }
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Cidade</Label>
                <Input
                  placeholder="Ex: São Paulo"
                  value={localFilters.location?.city || ""}
                  onChange={(e) => 
                    updateLocalFilters({
                      location: {
                        ...localFilters.location,
                        city: e.target.value
                      }
                    })
                  }
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  )
}
