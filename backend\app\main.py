"""
Main FastAPI application for AutoParts API.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi import Request
import time
import logging

from app.core.config import settings
from app.core.database import check_db_connection
from app.core.logging import setup_logging, get_logger
from app.middleware.logging_middleware import LoggingMiddleware
from app.middleware.validation_middleware import ValidationMiddleware
from app.api.v1 import api_router

# Setup structured logging
setup_logging()
logger = get_logger("app.main")

# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="""
    ## AutoParts B2B Marketplace API

    A comprehensive REST API for automotive parts inventory management, connecting dealerships with customers through a modern B2B marketplace platform.

    ### Key Features
    - **Authentication & Authorization**: JWT-based authentication with role-based access control
    - **Parts Catalog Management**: Comprehensive parts search, categorization, and compatibility matching
    - **Inventory Management**: Real-time stock tracking, pricing, and availability
    - **Dealership Management**: Registration, profile management, and subscription handling
    - **File Processing**: Excel/CSV import for bulk inventory updates
    - **Payment Integration**: Asaas payment gateway integration for subscriptions
    - **Caching & Performance**: Redis-based caching for optimized search performance
    - **Monitoring & Logging**: Comprehensive monitoring, health checks, and structured logging

    ### Authentication
    The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```

    ### Rate Limiting
    API endpoints are rate-limited to ensure fair usage and system stability.

    ### Error Handling
    All endpoints return standardized error responses with appropriate HTTP status codes and detailed error messages.
    """,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    contact={
        "name": "AutoParts API Support",
        "email": "<EMAIL>",
        "url": "https://autoparts.com/support",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "Development server"
        },
        {
            "url": "https://api.autoparts.com",
            "description": "Production server"
        }
    ],
    openapi_tags=[
        {
            "name": "authentication",
            "description": "User authentication and authorization endpoints"
        },
        {
            "name": "dealerships",
            "description": "Dealership management and registration"
        },
        {
            "name": "parts",
            "description": "Parts catalog, search, and compatibility"
        },
        {
            "name": "inventory",
            "description": "Inventory management and stock tracking"
        },
        {
            "name": "subscriptions",
            "description": "Subscription management and Asaas integration"
        },
        {
            "name": "file-imports",
            "description": "File upload and processing for bulk operations"
        },
        {
            "name": "webhooks",
            "description": "Webhook endpoints for external integrations"
        },
        {
            "name": "cache",
            "description": "Cache management and optimization"
        },
        {
            "name": "monitoring",
            "description": "Health checks, metrics, and system monitoring"
        }
    ]
)

# Add middleware (order matters - last added is executed first)
# app.add_middleware(ValidationMiddleware)  # Temporarily disabled - causing Content-Length issues
# app.add_middleware(LoggingMiddleware)  # Temporarily disabled for testing

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add process time header to responses."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "timestamp": time.time(),
        }
    )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AutoParts API",
        "version": settings.APP_VERSION,
        "status": "running",
        "docs": "/api/v1/docs",
        "health": "/health",
        "timestamp": time.time(),
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint with database connectivity check."""
    try:
        db_status = "connected" if check_db_connection() else "disconnected"
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        db_status = "error"

    return {
        "status": "healthy" if db_status == "connected" else "degraded",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "database": db_status,
        "timestamp": time.time(),
    }


# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Include SEO routes at root level for better SEO (temporarily disabled)
# from app.api.v1.seo import router as seo_router
# app.include_router(seo_router, tags=["seo-root"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
    )
