"""
Subscription model for AutoParts API.
"""
from sqlalchemy import Column, String, Date, ForeignKey, Index, DECIMAL, Enum as SQLEnum
from sqlalchemy.orm import relationship
from enum import Enum

from app.models.base import BaseModel
from app.core.database_types import UUID


class SubscriptionStatus(str, Enum):
    """Subscription status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"
    SUSPENDED = "suspended"


class PlanType(str, Enum):
    """Subscription plan type enumeration."""
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"
    TRIAL = "trial"


class Subscription(BaseModel):
    """
    Subscription model representing dealership subscriptions.
    
    Attributes:
        dealership_id: Reference to the dealership
        asaas_subscription_id: Asaas subscription ID
        asaas_customer_id: Asaas customer ID
        plan_type: Type of subscription plan
        status: Current subscription status
        value: Monthly subscription value
        next_due_date: Next payment due date
        billing_type: Payment method (BOLETO, CREDIT_CARD, PIX)
        cycle: Billing cycle (MONTHLY, YEARLY)
        description: Subscription description
        external_reference: External reference for tracking
    """
    __tablename__ = "subscriptions"

    # Foreign Keys
    dealership_id = Column(
        UUID(),
        ForeignKey("dealerships.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the dealership"
    )

    # Asaas Integration
    asaas_subscription_id = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        doc="Asaas subscription ID"
    )
    
    asaas_customer_id = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Asaas customer ID"
    )

    # Subscription Details
    plan_type = Column(
        SQLEnum(PlanType),
        nullable=False,
        default=PlanType.BASIC,
        index=True,
        doc="Type of subscription plan"
    )
    
    status = Column(
        SQLEnum(SubscriptionStatus),
        nullable=False,
        default=SubscriptionStatus.PENDING,
        index=True,
        doc="Current subscription status"
    )
    
    value = Column(
        DECIMAL(10, 2),
        nullable=False,
        doc="Monthly subscription value"
    )

    # Billing Information
    next_due_date = Column(
        Date,
        nullable=True,
        index=True,
        doc="Next payment due date"
    )
    
    billing_type = Column(
        String(20),
        nullable=False,
        default="BOLETO",
        doc="Payment method: BOLETO, CREDIT_CARD, PIX"
    )
    
    cycle = Column(
        String(20),
        nullable=False,
        default="MONTHLY",
        doc="Billing cycle: MONTHLY, YEARLY"
    )

    # Additional Information
    description = Column(
        String(500),
        nullable=True,
        doc="Subscription description"
    )
    
    external_reference = Column(
        String(100),
        nullable=True,
        doc="External reference for tracking"
    )
    
    discount_value = Column(
        DECIMAL(10, 2),
        nullable=True,
        default=0,
        doc="Discount value applied"
    )
    
    interest_value = Column(
        DECIMAL(10, 2),
        nullable=True,
        default=0,
        doc="Interest value for overdue payments"
    )

    # Relationships
    dealership = relationship(
        "Dealership",
        back_populates="subscriptions"
    )

    def __repr__(self):
        return f"<Subscription(id={self.id}, dealership_id={self.dealership_id}, plan={self.plan_type}, status={self.status})>"

    @property
    def is_active(self):
        """Check if subscription is active."""
        return self.status == SubscriptionStatus.ACTIVE

    @property
    def is_overdue(self):
        """Check if subscription is overdue."""
        return self.status == SubscriptionStatus.OVERDUE

    @property
    def final_value(self):
        """Calculate final value with discount and interest."""
        base_value = float(self.value or 0)
        discount = float(self.discount_value or 0)
        interest = float(self.interest_value or 0)
        return base_value - discount + interest

    def activate(self):
        """Activate the subscription."""
        self.status = SubscriptionStatus.ACTIVE

    def cancel(self):
        """Cancel the subscription."""
        self.status = SubscriptionStatus.CANCELLED

    def suspend(self):
        """Suspend the subscription."""
        self.status = SubscriptionStatus.SUSPENDED


# Create indexes for performance optimization
Index('idx_subscription_dealership', Subscription.dealership_id)
Index('idx_subscription_asaas_id', Subscription.asaas_subscription_id)
Index('idx_subscription_status', Subscription.status)
Index('idx_subscription_plan', Subscription.plan_type)
Index('idx_subscription_due_date', Subscription.next_due_date)
Index('idx_subscription_active', Subscription.dealership_id, Subscription.status)
