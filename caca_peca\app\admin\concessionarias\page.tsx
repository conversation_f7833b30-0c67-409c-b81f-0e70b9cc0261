"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  Package,
  FileText,
  User,
  Filter,
  RefreshCw,
  Eye,
  Ban,
} from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"

// Dados simulados de concessionárias
const dealerships = [
  {
    id: "1",
    name: "AutoPeças Premium",
    tradingName: "AutoPeças Premium LTDA",
    cnpj: "12.345.678/0001-90",
    city: "São Paulo",
    state: "SP",
    phone: "(11) 3333-4444",
    email: "<EMAIL>",
    status: "active",
    stockItems: 1248,
    lastStockUpdate: "2023-10-15T14:30:00",
    lastLogin: "2023-10-18T09:15:00",
    lastInvoice: {
      id: "INV-001",
      date: "2023-10-01",
      status: "paid",
      amount: 1250.9,
    },
    plan: "premium",
    createdAt: "2022-05-10T10:00:00",
  },
  {
    id: "2",
    name: "Center Parts",
    tradingName: "Center Parts Comércio LTDA",
    cnpj: "23.456.789/0001-01",
    city: "Campinas",
    state: "SP",
    phone: "(19) 3333-4444",
    email: "<EMAIL>",
    status: "active",
    stockItems: 856,
    lastStockUpdate: "2023-10-10T11:20:00",
    lastLogin: "2023-10-17T16:45:00",
    lastInvoice: {
      id: "INV-002",
      date: "2023-10-01",
      status: "pending",
      amount: 1250.9,
    },
    plan: "standard",
    createdAt: "2022-06-15T14:30:00",
  },
  {
    id: "3",
    name: "Freios & Cia",
    tradingName: "Freios & Cia Autopeças LTDA",
    cnpj: "34.567.890/0001-12",
    city: "Rio de Janeiro",
    state: "RJ",
    phone: "(21) 3333-4444",
    email: "<EMAIL>",
    status: "inactive",
    stockItems: 532,
    lastStockUpdate: "2023-09-28T09:45:00",
    lastLogin: "2023-10-05T10:30:00",
    lastInvoice: {
      id: "INV-003",
      date: "2023-10-01",
      status: "overdue",
      amount: 1250.9,
    },
    plan: "basic",
    createdAt: "2022-07-20T09:15:00",
  },
  {
    id: "4",
    name: "Amortex",
    tradingName: "Amortex Comércio de Autopeças LTDA",
    cnpj: "45.678.901/0001-23",
    city: "Belo Horizonte",
    state: "MG",
    phone: "(31) 3333-4444",
    email: "<EMAIL>",
    status: "active",
    stockItems: 1024,
    lastStockUpdate: "2023-10-16T15:10:00",
    lastLogin: "2023-10-18T11:20:00",
    lastInvoice: {
      id: "INV-004",
      date: "2023-10-01",
      status: "paid",
      amount: 1250.9,
    },
    plan: "premium",
    createdAt: "2022-08-05T13:45:00",
  },
  {
    id: "5",
    name: "Sul Peças",
    tradingName: "Sul Peças Automotivas LTDA",
    cnpj: "56.789.012/0001-34",
    city: "Porto Alegre",
    state: "RS",
    phone: "(51) 3333-4444",
    email: "<EMAIL>",
    status: "active",
    stockItems: 768,
    lastStockUpdate: "2023-10-12T10:30:00",
    lastLogin: "2023-10-16T14:50:00",
    lastInvoice: {
      id: "INV-005",
      date: "2023-10-01",
      status: "paid",
      amount: 1250.9,
    },
    plan: "standard",
    createdAt: "2022-09-10T11:30:00",
  },
]

// Planos disponíveis
const plans = [
  { value: "basic", label: "Básico" },
  { value: "standard", label: "Padrão" },
  { value: "premium", label: "Premium" },
]

// Estados brasileiros
const states = [
  { value: "AC", label: "Acre" },
  { value: "AL", label: "Alagoas" },
  { value: "AP", label: "Amapá" },
  { value: "AM", label: "Amazonas" },
  { value: "BA", label: "Bahia" },
  { value: "CE", label: "Ceará" },
  { value: "DF", label: "Distrito Federal" },
  { value: "ES", label: "Espírito Santo" },
  { value: "GO", label: "Goiás" },
  { value: "MA", label: "Maranhão" },
  { value: "MT", label: "Mato Grosso" },
  { value: "MS", label: "Mato Grosso do Sul" },
  { value: "MG", label: "Minas Gerais" },
  { value: "PA", label: "Pará" },
  { value: "PB", label: "Paraíba" },
  { value: "PR", label: "Paraná" },
  { value: "PE", label: "Pernambuco" },
  { value: "PI", label: "Piauí" },
  { value: "RJ", label: "Rio de Janeiro" },
  { value: "RN", label: "Rio Grande do Norte" },
  { value: "RS", label: "Rio Grande do Sul" },
  { value: "RO", label: "Rondônia" },
  { value: "RR", label: "Roraima" },
  { value: "SC", label: "Santa Catarina" },
  { value: "SP", label: "São Paulo" },
  { value: "SE", label: "Sergipe" },
  { value: "TO", label: "Tocantins" },
]

export default function DealershipsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [planFilter, setPlanFilter] = useState("all")
  const [stateFilter, setStateFilter] = useState("all")
  const [selectedDealership, setSelectedDealership] = useState<any>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedTab, setSelectedTab] = useState("all")

  // Adicionar novos estados para os modais de detalhes e ativação/desativação
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false)

  // Filtrar concessionárias
  const filteredDealerships = dealerships.filter((dealership) => {
    // Filtrar por termo de busca
    const matchesSearch =
      searchTerm === "" ||
      dealership.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dealership.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dealership.cnpj.includes(searchTerm)

    // Filtrar por status
    const matchesStatus = statusFilter === "all" || dealership.status === statusFilter

    // Filtrar por plano
    const matchesPlan = planFilter === "all" || dealership.plan === planFilter

    // Filtrar por estado
    const matchesState = stateFilter === "all" || dealership.state === stateFilter

    // Filtrar por tab
    if (selectedTab === "active") return dealership.status === "active" && matchesSearch && matchesPlan && matchesState
    if (selectedTab === "inactive")
      return dealership.status === "inactive" && matchesSearch && matchesPlan && matchesState
    if (selectedTab === "overdue")
      return (
        dealership.lastInvoice.status === "overdue" && matchesSearch && matchesStatus && matchesPlan && matchesState
      )

    return matchesSearch && matchesStatus && matchesPlan && matchesState
  })

  // Formatar status da concessionária
  const formatStatus = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Ativa</Badge>
      case "inactive":
        return <Badge variant="outline">Inativa</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Formatar status da fatura
  const formatInvoiceStatus = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-green-500">Paga</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pendente</Badge>
      case "overdue":
        return <Badge className="bg-red-500">Vencida</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Formatar plano
  const formatPlan = (plan: string) => {
    switch (plan) {
      case "basic":
        return <Badge variant="outline">Básico</Badge>
      case "standard":
        return <Badge className="bg-blue-500">Padrão</Badge>
      case "premium":
        return <Badge className="bg-purple-500">Premium</Badge>
      default:
        return <Badge variant="outline">{plan}</Badge>
    }
  }

  // Formatar data relativa
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return "agora mesmo"
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `há ${minutes} ${minutes === 1 ? "minuto" : "minutos"}`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `há ${hours} ${hours === 1 ? "hora" : "horas"}`
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return `há ${days} ${days === 1 ? "dia" : "dias"}`
    } else if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000)
      return `há ${months} ${months === 1 ? "mês" : "meses"}`
    } else {
      const years = Math.floor(diffInSeconds / 31536000)
      return `há ${years} ${years === 1 ? "ano" : "anos"}`
    }
  }

  // Verificar se a última atualização de estoque é recente (menos de 7 dias)
  const isStockUpdateRecent = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
    return diffInDays < 7
  }

  // Verificar se o último login é recente (menos de 3 dias)
  const isLoginRecent = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
    return diffInDays < 3
  }

  // Abrir modal de edição
  const handleEditDealership = (dealership: any) => {
    setSelectedDealership(dealership)
    setIsEditDialogOpen(true)
  }

  // Abrir modal de exclusão
  const handleDeleteDealership = (dealership: any) => {
    setSelectedDealership(dealership)
    setIsDeleteDialogOpen(true)
  }

  // Confirmar exclusão
  const confirmDelete = () => {
    // Em uma aplicação real, você enviaria uma requisição para excluir a concessionária
    console.log(`Excluindo concessionária: ${selectedDealership?.name}`)
    setIsDeleteDialogOpen(false)
    setSelectedDealership(null)
  }

  // Limpar filtros
  const clearFilters = () => {
    setSearchTerm("")
    setStatusFilter("all")
    setPlanFilter("all")
    setStateFilter("all")
  }

  // Adicionar função para alternar o status da concessionária
  const toggleDealershipStatus = () => {
    // Em uma aplicação real, você enviaria uma requisição para alterar o status
    console.log(`Alterando status da concessionária: ${selectedDealership?.name}`)
    console.log(`Novo status: ${selectedDealership?.status === "active" ? "inactive" : "active"}`)
    setIsStatusDialogOpen(false)
    setSelectedDealership(null)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Concessionárias</h1>
          <p className="text-muted-foreground">Gerencie as concessionárias cadastradas na plataforma</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Nova Concessionária
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Adicionar Nova Concessionária</DialogTitle>
                <DialogDescription>
                  Preencha os dados abaixo para cadastrar uma nova concessionária na plataforma.
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <Tabs defaultValue="basic">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="basic">Dados Básicos</TabsTrigger>
                    <TabsTrigger value="address">Endereço</TabsTrigger>
                    <TabsTrigger value="plan">Plano</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Nome Fantasia</Label>
                        <Input id="name" placeholder="Nome da concessionária" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="tradingName">Razão Social</Label>
                        <Input id="tradingName" placeholder="Razão social completa" />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="cnpj">CNPJ</Label>
                        <Input id="cnpj" placeholder="XX.XXX.XXX/XXXX-XX" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Telefone</Label>
                        <Input id="phone" placeholder="(XX) XXXX-XXXX" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" placeholder="<EMAIL>" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select defaultValue="active">
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Selecione o status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Ativa</SelectItem>
                          <SelectItem value="inactive">Inativa</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="address" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="address">Endereço</Label>
                        <Input id="address" placeholder="Rua, número" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="complement">Complemento</Label>
                        <Input id="complement" placeholder="Sala, andar, etc." />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="city">Cidade</Label>
                        <Input id="city" placeholder="Nome da cidade" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="state">Estado</Label>
                        <Select>
                          <SelectTrigger id="state">
                            <SelectValue placeholder="Selecione o estado" />
                          </SelectTrigger>
                          <SelectContent>
                            {states.map((state) => (
                              <SelectItem key={state.value} value={state.value}>
                                {state.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="zipCode">CEP</Label>
                      <Input id="zipCode" placeholder="XXXXX-XXX" />
                    </div>
                  </TabsContent>

                  <TabsContent value="plan" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="plan">Plano</Label>
                      <Select defaultValue="standard">
                        <SelectTrigger id="plan">
                          <SelectValue placeholder="Selecione o plano" />
                        </SelectTrigger>
                        <SelectContent>
                          {plans.map((plan) => (
                            <SelectItem key={plan.value} value={plan.value}>
                              {plan.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notes">Observações</Label>
                      <Textarea id="notes" placeholder="Informações adicionais sobre a concessionária" />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch id="sendCredentials" />
                      <Label htmlFor="sendCredentials">Enviar credenciais por email</Label>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={() => setIsAddDialogOpen(false)}>Adicionar Concessionária</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="all" onValueChange={setSelectedTab}>
        <div className="flex flex-col justify-between gap-4 sm:flex-row">
          <TabsList>
            <TabsTrigger value="all">Todas</TabsTrigger>
            <TabsTrigger value="active">Ativas</TabsTrigger>
            <TabsTrigger value="inactive">Inativas</TabsTrigger>
            <TabsTrigger value="overdue">Faturas Vencidas</TabsTrigger>
          </TabsList>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="mr-2 h-4 w-4" />
              Filtros
              {(statusFilter !== "all" || planFilter !== "all" || stateFilter !== "all") && (
                <Badge className="ml-1 h-5 w-5 rounded-full p-0 text-[10px]">
                  {(statusFilter !== "all" ? 1 : 0) + (planFilter !== "all" ? 1 : 0) + (stateFilter !== "all" ? 1 : 0)}
                </Badge>
              )}
            </Button>
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Limpar
            </Button>
          </div>
        </div>

        <div className="mt-4 flex items-center">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Buscar por nome, email ou CNPJ..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {showFilters && (
          <Card className="mt-4">
            <CardContent className="grid grid-cols-1 gap-4 p-4 sm:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger id="status-filter">
                    <SelectValue placeholder="Filtrar por status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os status</SelectItem>
                    <SelectItem value="active">Ativas</SelectItem>
                    <SelectItem value="inactive">Inativas</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="plan-filter">Plano</Label>
                <Select value={planFilter} onValueChange={setPlanFilter}>
                  <SelectTrigger id="plan-filter">
                    <SelectValue placeholder="Filtrar por plano" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os planos</SelectItem>
                    <SelectItem value="basic">Básico</SelectItem>
                    <SelectItem value="standard">Padrão</SelectItem>
                    <SelectItem value="premium">Premium</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="state-filter">Estado</Label>
                <Select value={stateFilter} onValueChange={setStateFilter}>
                  <SelectTrigger id="state-filter">
                    <SelectValue placeholder="Filtrar por estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os estados</SelectItem>
                    {states.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        )}

        <TabsContent value="all" className="mt-6">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Concessionária</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Plano</TableHead>
                    <TableHead>Estoque</TableHead>
                    <TableHead>Última Fatura</TableHead>
                    <TableHead>Último Login</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDealerships.length > 0 ? (
                    filteredDealerships.map((dealership) => (
                      <TableRow key={dealership.id}>
                        <TableCell>
                          <div className="font-medium">{dealership.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {dealership.city}/{dealership.state}
                          </div>
                        </TableCell>
                        <TableCell>{formatStatus(dealership.status)}</TableCell>
                        <TableCell>{formatPlan(dealership.plan)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-muted-foreground" />
                            <span>{dealership.stockItems} itens</span>
                          </div>
                          <div
                            className={`flex items-center gap-1 text-xs ${
                              isStockUpdateRecent(dealership.lastStockUpdate)
                                ? "text-green-600"
                                : "text-muted-foreground"
                            }`}
                          >
                            <Clock className="h-3 w-3" />
                            <span>Atualizado {formatRelativeTime(dealership.lastStockUpdate)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <span>{dealership.lastInvoice.id}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs">
                            {formatInvoiceStatus(dealership.lastInvoice.status)}
                            <span className="text-muted-foreground">
                              {new Date(dealership.lastInvoice.date).toLocaleDateString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div
                            className={`flex items-center gap-1 ${
                              isLoginRecent(dealership.lastLogin) ? "text-green-600" : "text-muted-foreground"
                            }`}
                          >
                            <User className="h-4 w-4" />
                            <span>{formatRelativeTime(dealership.lastLogin)}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(dealership.lastLogin).toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end">
                            <div className="relative inline-block text-left">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const menuId = `dropdown-menu-${dealership.id}`
                                  const menu = document.getElementById(menuId)

                                  // Fechar todos os outros menus primeiro
                                  document.querySelectorAll('[id^="dropdown-menu-"]').forEach((el) => {
                                    if (el.id !== menuId) {
                                      el.classList.add("hidden")
                                    }
                                  })

                                  // Alternar a visibilidade deste menu
                                  if (menu) {
                                    menu.classList.toggle("hidden")

                                    // Adicionar um event listener para fechar o menu quando clicar fora dele
                                    const closeMenu = (event: MouseEvent) => {
                                      const target = event.target as Node
                                      if (menu && !menu.contains(target) && target !== e.currentTarget) {
                                        menu.classList.add("hidden")
                                        document.removeEventListener("click", closeMenu)
                                      }
                                    }

                                    // Adicionar o event listener com um pequeno atraso para evitar que ele seja acionado imediatamente
                                    setTimeout(() => {
                                      document.addEventListener("click", closeMenu)
                                    }, 100)
                                  }
                                }}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Abrir menu</span>
                              </Button>

                              <div
                                id={`dropdown-menu-${dealership.id}`}
                                className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-popover border border-border hidden z-50"
                              >
                                <div className="py-1 rounded-md bg-popover text-popover-foreground">
                                  <div className="px-3 py-2 text-sm font-medium border-b">Ações</div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => {
                                      setSelectedDealership(dealership)
                                      setIsViewDialogOpen(true)
                                    }}
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    Ver detalhes
                                  </button>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => handleEditDealership(dealership)}
                                  >
                                    <Edit className="mr-2 h-4 w-4" />
                                    Editar
                                  </button>

                                  <div className="border-t my-1"></div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm text-red-600 hover:bg-accent hover:text-red-700"
                                    onClick={() => handleDeleteDealership(dealership)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Excluir
                                  </button>

                                  <div className="border-t my-1"></div>

                                  {dealership.status === "active" ? (
                                    <button
                                      className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                      onClick={() => {
                                        setSelectedDealership(dealership)
                                        setIsStatusDialogOpen(true)
                                      }}
                                    >
                                      <Ban className="mr-2 h-4 w-4" />
                                      Desativar
                                    </button>
                                  ) : (
                                    <button
                                      className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                      onClick={() => {
                                        setSelectedDealership(dealership)
                                        setIsStatusDialogOpen(true)
                                      }}
                                    >
                                      <CheckCircle className="mr-2 h-4 w-4" />
                                      Ativar
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        Nenhuma concessionária encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active" className="mt-6">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Concessionária</TableHead>
                    <TableHead>Plano</TableHead>
                    <TableHead>Estoque</TableHead>
                    <TableHead>Última Fatura</TableHead>
                    <TableHead>Último Login</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDealerships.length > 0 ? (
                    filteredDealerships.map((dealership) => (
                      <TableRow key={dealership.id}>
                        <TableCell>
                          <div className="font-medium">{dealership.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {dealership.city}/{dealership.state}
                          </div>
                        </TableCell>
                        <TableCell>{formatPlan(dealership.plan)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-muted-foreground" />
                            <span>{dealership.stockItems} itens</span>
                          </div>
                          <div
                            className={`flex items-center gap-1 text-xs ${
                              isStockUpdateRecent(dealership.lastStockUpdate)
                                ? "text-green-600"
                                : "text-muted-foreground"
                            }`}
                          >
                            <Clock className="h-3 w-3" />
                            <span>Atualizado {formatRelativeTime(dealership.lastStockUpdate)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <span>{dealership.lastInvoice.id}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs">
                            {formatInvoiceStatus(dealership.lastInvoice.status)}
                            <span className="text-muted-foreground">
                              {new Date(dealership.lastInvoice.date).toLocaleDateString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div
                            className={`flex items-center gap-1 ${
                              isLoginRecent(dealership.lastLogin) ? "text-green-600" : "text-muted-foreground"
                            }`}
                          >
                            <User className="h-4 w-4" />
                            <span>{formatRelativeTime(dealership.lastLogin)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end">
                            <div className="relative inline-block text-left">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const menuId = `dropdown-menu-${dealership.id}`
                                  const menu = document.getElementById(menuId)

                                  // Fechar todos os outros menus primeiro
                                  document.querySelectorAll('[id^="dropdown-menu-"]').forEach((el) => {
                                    if (el.id !== menuId) {
                                      el.classList.add("hidden")
                                    }
                                  })

                                  // Alternar a visibilidade deste menu
                                  if (menu) {
                                    menu.classList.toggle("hidden")

                                    // Adicionar um event listener para fechar o menu quando clicar fora dele
                                    const closeMenu = (event: MouseEvent) => {
                                      const target = event.target as Node
                                      if (menu && !menu.contains(target) && target !== e.currentTarget) {
                                        menu.classList.add("hidden")
                                        document.removeEventListener("click", closeMenu)
                                      }
                                    }

                                    // Adicionar o event listener com um pequeno atraso para evitar que ele seja acionado imediatamente
                                    setTimeout(() => {
                                      document.addEventListener("click", closeMenu)
                                    }, 100)
                                  }
                                }}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Abrir menu</span>
                              </Button>

                              <div
                                id={`dropdown-menu-${dealership.id}`}
                                className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-popover border border-border hidden z-50"
                              >
                                <div className="py-1 rounded-md bg-popover text-popover-foreground">
                                  <div className="px-3 py-2 text-sm font-medium border-b">Ações</div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => {
                                      setSelectedDealership(dealership)
                                      setIsViewDialogOpen(true)
                                    }}
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    Ver detalhes
                                  </button>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => handleEditDealership(dealership)}
                                  >
                                    <Edit className="mr-2 h-4 w-4" />
                                    Editar
                                  </button>

                                  <div className="border-t my-1"></div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm text-red-600 hover:bg-accent hover:text-red-700"
                                    onClick={() => handleDeleteDealership(dealership)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Excluir
                                  </button>

                                  <div className="border-t my-1"></div>

                                  {dealership.status === "active" ? (
                                    <button
                                      className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                      onClick={() => {
                                        setSelectedDealership(dealership)
                                        setIsStatusDialogOpen(true)
                                      }}
                                    >
                                      <Ban className="mr-2 h-4 w-4" />
                                      Desativar
                                    </button>
                                  ) : (
                                    <button
                                      className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                      onClick={() => {
                                        setSelectedDealership(dealership)
                                        setIsStatusDialogOpen(true)
                                      }}
                                    >
                                      <CheckCircle className="mr-2 h-4 w-4" />
                                      Ativar
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        Nenhuma concessionária ativa encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inactive" className="mt-6">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Concessionária</TableHead>
                    <TableHead>Plano</TableHead>
                    <TableHead>Estoque</TableHead>
                    <TableHead>Última Fatura</TableHead>
                    <TableHead>Último Login</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDealerships.length > 0 ? (
                    filteredDealerships.map((dealership) => (
                      <TableRow key={dealership.id}>
                        <TableCell>
                          <div className="font-medium">{dealership.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {dealership.city}/{dealership.state}
                          </div>
                        </TableCell>
                        <TableCell>{formatPlan(dealership.plan)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-muted-foreground" />
                            <span>{dealership.stockItems} itens</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <span>{dealership.lastInvoice.id}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs">
                            {formatInvoiceStatus(dealership.lastInvoice.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-muted-foreground">
                            <User className="h-4 w-4" />
                            <span>{formatRelativeTime(dealership.lastLogin)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end">
                            <div className="relative inline-block text-left">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const menuId = `dropdown-menu-${dealership.id}`
                                  const menu = document.getElementById(menuId)

                                  // Fechar todos os outros menus primeiro
                                  document.querySelectorAll('[id^="dropdown-menu-"]').forEach((el) => {
                                    if (el.id !== menuId) {
                                      el.classList.add("hidden")
                                    }
                                  })

                                  // Alternar a visibilidade deste menu
                                  if (menu) {
                                    menu.classList.toggle("hidden")

                                    // Adicionar um event listener para fechar o menu quando clicar fora dele
                                    const closeMenu = (event: MouseEvent) => {
                                      const target = event.target as Node
                                      if (menu && !menu.contains(target) && target !== e.currentTarget) {
                                        menu.classList.add("hidden")
                                        document.removeEventListener("click", closeMenu)
                                      }
                                    }

                                    // Adicionar o event listener com um pequeno atraso para evitar que ele seja acionado imediatamente
                                    setTimeout(() => {
                                      document.addEventListener("click", closeMenu)
                                    }, 100)
                                  }
                                }}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Abrir menu</span>
                              </Button>

                              <div
                                id={`dropdown-menu-${dealership.id}`}
                                className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-popover border border-border hidden z-50"
                              >
                                <div className="py-1 rounded-md bg-popover text-popover-foreground">
                                  <div className="px-3 py-2 text-sm font-medium border-b">Ações</div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => {
                                      setSelectedDealership(dealership)
                                      setIsViewDialogOpen(true)
                                    }}
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    Ver detalhes
                                  </button>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => handleEditDealership(dealership)}
                                  >
                                    <Edit className="mr-2 h-4 w-4" />
                                    Editar
                                  </button>

                                  <div className="border-t my-1"></div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm text-red-600 hover:bg-accent hover:text-red-700"
                                    onClick={() => handleDeleteDealership(dealership)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Excluir
                                  </button>

                                  <div className="border-t my-1"></div>

                                  <button className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Ativar
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        Nenhuma concessionária inativa encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="overdue" className="mt-6">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Concessionária</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Plano</TableHead>
                    <TableHead>Fatura Vencida</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDealerships.length > 0 ? (
                    filteredDealerships.map((dealership) => (
                      <TableRow key={dealership.id}>
                        <TableCell>
                          <div className="font-medium">{dealership.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {dealership.city}/{dealership.state}
                          </div>
                        </TableCell>
                        <TableCell>{formatStatus(dealership.status)}</TableCell>
                        <TableCell>{formatPlan(dealership.plan)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-red-500" />
                            <span>{dealership.lastInvoice.id}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Vencida em {new Date(dealership.lastInvoice.date).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-red-500">
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(dealership.lastInvoice.amount)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end">
                            <div className="relative inline-block text-left">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const menuId = `dropdown-menu-${dealership.id}`
                                  const menu = document.getElementById(menuId)

                                  // Fechar todos os outros menus primeiro
                                  document.querySelectorAll('[id^="dropdown-menu-"]').forEach((el) => {
                                    if (el.id !== menuId) {
                                      el.classList.add("hidden")
                                    }
                                  })

                                  // Alternar a visibilidade deste menu
                                  if (menu) {
                                    menu.classList.toggle("hidden")

                                    // Adicionar um event listener para fechar o menu quando clicar fora dele
                                    const closeMenu = (event: MouseEvent) => {
                                      const target = event.target as Node
                                      if (menu && !menu.contains(target) && target !== e.currentTarget) {
                                        menu.classList.add("hidden")
                                        document.removeEventListener("click", closeMenu)
                                      }
                                    }

                                    // Adicionar o event listener com um pequeno atraso para evitar que ele seja acionado immediately
                                    setTimeout(() => {
                                      document.addEventListener("click", closeMenu)
                                    }, 100)
                                  }
                                }}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Abrir menu</span>
                              </Button>

                              <div
                                id={`dropdown-menu-${dealership.id}`}
                                className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-popover border border-border hidden z-50"
                              >
                                <div className="py-1 rounded-md bg-popover text-popover-foreground">
                                  <div className="px-3 py-2 text-sm font-medium border-b">Ações</div>

                                  <button
                                    className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                                    onClick={() => {
                                      setSelectedDealership(dealership)
                                      setIsViewDialogOpen(true)
                                    }}
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    Ver detalhes
                                  </button>

                                  <button className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                                    <FileText className="mr-2 h-4 w-4" />
                                    Ver fatura
                                  </button>

                                  <div className="border-t my-1"></div>

                                  <button className="flex w-full items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Marcar como paga
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        Nenhuma concessionária com faturas vencidas.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal de edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Concessionária</DialogTitle>
            <DialogDescription>Edite as informações da concessionária {selectedDealership?.name}.</DialogDescription>
          </DialogHeader>

          {selectedDealership && (
            <div className="grid gap-4 py-4">
              <Tabs defaultValue="basic">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Dados Básicos</TabsTrigger>
                  <TabsTrigger value="address">Endereço</TabsTrigger>
                  <TabsTrigger value="plan">Plano</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-name">Nome Fantasia</Label>
                      <Input id="edit-name" defaultValue={selectedDealership.name} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-tradingName">Razão Social</Label>
                      <Input id="edit-tradingName" defaultValue={selectedDealership.tradingName} />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-cnpj">CNPJ</Label>
                      <Input id="edit-cnpj" defaultValue={selectedDealership.cnpj} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-phone">Telefone</Label>
                      <Input id="edit-phone" defaultValue={selectedDealership.phone} />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="edit-email">Email</Label>
                    <Input id="edit-email" type="email" defaultValue={selectedDealership.email} />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="edit-status">Status</Label>
                    <Select defaultValue={selectedDealership.status}>
                      <SelectTrigger id="edit-status">
                        <SelectValue placeholder="Selecione o status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Ativa</SelectItem>
                        <SelectItem value="inactive">Inativa</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>

                <TabsContent value="address" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-city">Cidade</Label>
                      <Input id="edit-city" defaultValue={selectedDealership.city} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-state">Estado</Label>
                      <Select defaultValue={selectedDealership.state}>
                        <SelectTrigger id="edit-state">
                          <SelectValue placeholder="Selecione o estado" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state.value} value={state.value}>
                              {state.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="plan" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-plan">Plano</Label>
                    <Select defaultValue={selectedDealership.plan}>
                      <SelectTrigger id="edit-plan">
                        <SelectValue placeholder="Selecione o plano" />
                      </SelectTrigger>
                      <SelectContent>
                        {plans.map((plan) => (
                          <SelectItem key={plan.value} value={plan.value}>
                            {plan.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="edit-notes">Observações</Label>
                    <Textarea id="edit-notes" placeholder="Informações adicionais sobre a concessionária" />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={() => setIsEditDialogOpen(false)}>Salvar Alterações</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir a concessionária {selectedDealership?.name}? Esta ação não pode ser
              desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de visualização de detalhes */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Detalhes da Concessionária</DialogTitle>
            <DialogDescription>Informações detalhadas sobre {selectedDealership?.name}</DialogDescription>
          </DialogHeader>

          {selectedDealership && (
            <div className="grid gap-6 py-4">
              <Tabs defaultValue="info">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="info">Informações</TabsTrigger>
                  <TabsTrigger value="invoices">Faturas</TabsTrigger>
                  <TabsTrigger value="stock">Estoque</TabsTrigger>
                  <TabsTrigger value="users">Usuários</TabsTrigger>
                </TabsList>

                <TabsContent value="info" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-lg font-medium">Dados Cadastrais</h3>
                      <div className="mt-2 space-y-2 rounded-md border p-3">
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Nome Fantasia:</span>
                          <p>{selectedDealership.name}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Razão Social:</span>
                          <p>{selectedDealership.tradingName}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">CNPJ:</span>
                          <p>{selectedDealership.cnpj}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Email:</span>
                          <p>{selectedDealership.email}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Telefone:</span>
                          <p>{selectedDealership.phone}</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium">Informações Adicionais</h3>
                      <div className="mt-2 space-y-2 rounded-md border p-3">
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Status:</span>
                          <p className="flex items-center gap-2">{formatStatus(selectedDealership.status)}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Plano:</span>
                          <p className="flex items-center gap-2">{formatPlan(selectedDealership.plan)}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Localização:</span>
                          <p>
                            {selectedDealership.city}/{selectedDealership.state}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Data de Cadastro:</span>
                          <p>{new Date(selectedDealership.createdAt).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-muted-foreground">Último Login:</span>
                          <p className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            {formatRelativeTime(selectedDealership.lastLogin)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="invoices" className="space-y-4">
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Fatura</TableHead>
                          <TableHead>Data</TableHead>
                          <TableHead>Valor</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell>{selectedDealership.lastInvoice.id}</TableCell>
                          <TableCell>{new Date(selectedDealership.lastInvoice.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(selectedDealership.lastInvoice.amount)}
                          </TableCell>
                          <TableCell>{formatInvoiceStatus(selectedDealership.lastInvoice.status)}</TableCell>
                        </TableRow>
                        {/* Faturas simuladas adicionais */}
                        <TableRow>
                          <TableCell>INV-{selectedDealership.id}-002</TableCell>
                          <TableCell>
                            {new Date(new Date().setMonth(new Date().getMonth() - 1)).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(1250.9)}
                          </TableCell>
                          <TableCell>{formatInvoiceStatus("paid")}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>INV-{selectedDealership.id}-003</TableCell>
                          <TableCell>
                            {new Date(new Date().setMonth(new Date().getMonth() - 2)).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(1250.9)}
                          </TableCell>
                          <TableCell>{formatInvoiceStatus("paid")}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>

                <TabsContent value="stock" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">Estoque</h3>
                      <p className="text-sm text-muted-foreground">
                        Total de {selectedDealership.stockItems} itens cadastrados
                      </p>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>Última atualização: {formatRelativeTime(selectedDealership.lastStockUpdate)}</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <h4 className="text-sm font-medium text-muted-foreground">Valor Total do Estoque</h4>
                          <p className="text-2xl font-bold mt-2">
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(selectedDealership.stockItems * 150)}{" "}
                            {/* Valor médio por item */}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <h4 className="text-sm font-medium text-muted-foreground">Itens em Promoção</h4>
                          <p className="text-2xl font-bold mt-2">
                            {Math.floor(selectedDealership.stockItems * 0.15)} {/* 15% do estoque em promoção */}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(selectedDealership.stockItems * 0.15 * 120)}{" "}
                            {/* Valor com desconto */}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <h4 className="text-sm font-medium text-muted-foreground">Disponibilidade</h4>
                          <p className="text-2xl font-bold mt-2">
                            {Math.floor(selectedDealership.stockItems * 0.92)} {/* 92% do estoque disponível */}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {Math.floor(selectedDealership.stockItems * 0.08)} itens em falta
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-medium">Resumo do Estoque</h4>
                        <Badge variant="outline">{formatRelativeTime(selectedDealership.lastStockUpdate)}</Badge>
                      </div>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Tipo</TableHead>
                            <TableHead>Quantidade</TableHead>
                            <TableHead>Valor</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>Itens a preço normal</TableCell>
                            <TableCell>
                              {selectedDealership.stockItems - Math.floor(selectedDealership.stockItems * 0.15)}
                            </TableCell>
                            <TableCell>
                              {new Intl.NumberFormat("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              }).format(
                                (selectedDealership.stockItems - Math.floor(selectedDealership.stockItems * 0.15)) *
                                  150,
                              )}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Itens em promoção</TableCell>
                            <TableCell>{Math.floor(selectedDealership.stockItems * 0.15)}</TableCell>
                            <TableCell>
                              {new Intl.NumberFormat("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              }).format(Math.floor(selectedDealership.stockItems * 0.15) * 120)}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="users" className="space-y-4">
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Usuário</TableHead>
                          <TableHead>Cargo</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Último Acesso</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell>
                            <div className="font-medium">Administrador</div>
                          </TableCell>
                          <TableCell>Gerente</TableCell>
                          <TableCell>admin@{selectedDealership.email.split("@")[1]}</TableCell>
                          <TableCell>{formatRelativeTime(selectedDealership.lastLogin)}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>
                            <div className="font-medium">Operador 1</div>
                          </TableCell>
                          <TableCell>Vendedor</TableCell>
                          <TableCell>vendas@{selectedDealership.email.split("@")[1]}</TableCell>
                          <TableCell>
                            {formatRelativeTime(
                              new Date(new Date().setHours(new Date().getHours() - 48)).toISOString(),
                            )}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>
                            <div className="font-medium">Operador 2</div>
                          </TableCell>
                          <TableCell>Estoque</TableCell>
                          <TableCell>estoque@{selectedDealership.email.split("@")[1]}</TableCell>
                          <TableCell>
                            {formatRelativeTime(
                              new Date(new Date().setHours(new Date().getHours() - 72)).toISOString(),
                            )}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Fechar
            </Button>
            <Button
              onClick={() => {
                setIsViewDialogOpen(false)
                setIsEditDialogOpen(true)
              }}
            >
              Editar Concessionária
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de confirmação de ativação/desativação */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedDealership?.status === "active" ? "Desativar" : "Ativar"} Concessionária</DialogTitle>
            <DialogDescription>
              {selectedDealership?.status === "active"
                ? `Tem certeza que deseja desativar a concessionária ${selectedDealership?.name}? Ela não poderá mais acessar a plataforma.`
                : `Tem certeza que deseja ativar a concessionária ${selectedDealership?.name}? Ela poderá voltar a acessar a plataforma.`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              variant={selectedDealership?.status === "active" ? "destructive" : "default"}
              onClick={toggleDealershipStatus}
            >
              {selectedDealership?.status === "active" ? "Desativar" : "Ativar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

