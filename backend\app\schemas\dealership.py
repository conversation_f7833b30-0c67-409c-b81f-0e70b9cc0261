"""
Pydantic schemas for dealership management.
"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator
from datetime import datetime
from uuid import UUID
import re


class DealershipBase(BaseModel):
    """Base dealership schema."""
    name: str = Field(..., min_length=2, max_length=255, description="Dealership business name")
    trading_name: Optional[str] = Field(None, max_length=255, description="Legal trading name")
    cnpj: str = Field(..., min_length=14, max_length=18, description="Brazilian company registration number")
    email: EmailStr = Field(..., description="Contact email address")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone number")
    whatsapp: Optional[str] = Field(None, max_length=20, description="WhatsApp number")
    address: Optional[str] = Field(None, max_length=500, description="Street address")
    neighborhood: Optional[str] = Field(None, max_length=100, description="Neighborhood")
    city: Optional[str] = Field(None, max_length=100, description="City name")
    state: Optional[str] = Field(None, min_length=2, max_length=2, description="State abbreviation")
    zip_code: Optional[str] = Field(None, max_length=10, description="Postal code")
    description: Optional[str] = Field(None, description="Dealership description")
    responsible: Optional[str] = Field(None, max_length=255, description="Responsible person name")

    @validator('cnpj')
    def validate_cnpj(cls, v):
        """Validate CNPJ format."""
        if not v:
            raise ValueError('CNPJ is required')
        
        # Remove non-numeric characters
        cnpj_numbers = re.sub(r'[^0-9]', '', v)
        
        if len(cnpj_numbers) != 14:
            raise ValueError('CNPJ must have 14 digits')
        
        # Basic CNPJ validation (simplified)
        if cnpj_numbers == cnpj_numbers[0] * 14:
            raise ValueError('Invalid CNPJ format')
        
        return v

    @validator('state')
    def validate_state(cls, v):
        """Validate Brazilian state abbreviation."""
        if v is None:
            return v
        
        valid_states = [
            'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
            'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
            'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
        ]
        
        if v.upper() not in valid_states:
            raise ValueError('Invalid Brazilian state abbreviation')
        
        return v.upper()

    @validator('zip_code')
    def validate_zip_code(cls, v):
        """Validate Brazilian ZIP code format."""
        if v is None:
            return v
        
        # Remove non-numeric characters
        zip_numbers = re.sub(r'[^0-9]', '', v)
        
        if len(zip_numbers) != 8:
            raise ValueError('ZIP code must have 8 digits')
        
        return v

    @validator('phone', 'whatsapp')
    def validate_phone(cls, v):
        """Validate phone number format."""
        if v is None:
            return v
        
        # Remove non-numeric characters
        phone_numbers = re.sub(r'[^0-9]', '', v)
        
        if len(phone_numbers) < 10 or len(phone_numbers) > 11:
            raise ValueError('Phone number must have 10 or 11 digits')
        
        return v


class DealershipCreate(DealershipBase):
    """Schema for creating a new dealership."""
    # All fields from base are required for creation
    pass


class DealershipUpdate(BaseModel):
    """Schema for updating dealership information."""
    name: Optional[str] = Field(None, min_length=2, max_length=255)
    trading_name: Optional[str] = Field(None, max_length=255)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, max_length=20)
    whatsapp: Optional[str] = Field(None, max_length=20)
    address: Optional[str] = Field(None, max_length=500)
    neighborhood: Optional[str] = Field(None, max_length=100)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, min_length=2, max_length=2)
    zip_code: Optional[str] = Field(None, max_length=10)
    description: Optional[str] = None
    responsible: Optional[str] = Field(None, max_length=255)

    @validator('state')
    def validate_state(cls, v):
        """Validate Brazilian state abbreviation."""
        if v is None:
            return v
        
        valid_states = [
            'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
            'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
            'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
        ]
        
        if v.upper() not in valid_states:
            raise ValueError('Invalid Brazilian state abbreviation')
        
        return v.upper()


class DealershipInDB(DealershipBase):
    """Schema for dealership in database."""
    id: UUID
    subscription_id: Optional[str] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Dealership(DealershipInDB):
    """Public dealership schema."""
    pass


class DealershipSummary(BaseModel):
    """Summary schema for dealership listings."""
    id: UUID
    name: str
    city: Optional[str] = None
    state: Optional[str] = None
    is_active: bool
    inventory_count: Optional[int] = 0
    
    class Config:
        from_attributes = True


class DealershipRegistration(DealershipCreate):
    """Schema for dealership registration with user creation."""
    # User information for the dealership admin
    admin_full_name: str = Field(..., min_length=2, max_length=255, description="Admin user full name")
    admin_password: str = Field(..., min_length=8, max_length=128, description="Admin user password")
    
    @validator('admin_password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class DealershipRegistrationResponse(BaseModel):
    """Response schema for dealership registration."""
    dealership: Dealership
    admin_user_id: UUID
    message: str


class DealershipListResponse(BaseModel):
    """Response schema for dealership listing."""
    dealerships: List[DealershipSummary]
    total: int
    page: int
    per_page: int
    pages: int


class DealershipSearchFilters(BaseModel):
    """Schema for dealership search filters."""
    search: Optional[str] = Field(None, description="Search term for name, trading name, or city")
    city: Optional[str] = Field(None, description="Filter by city")
    state: Optional[str] = Field(None, description="Filter by state")
    active_only: bool = Field(True, description="Include only active dealerships")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(20, ge=1, le=100, description="Items per page")


class DealershipStats(BaseModel):
    """Schema for dealership statistics."""
    total_dealerships: int
    active_dealerships: int
    inactive_dealerships: int
    dealerships_by_state: List[dict]
    recent_registrations: int
