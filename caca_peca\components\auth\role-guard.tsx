/**
 * Role Guard Component
 * Conditionally renders content based on user roles
 */

'use client';

import React from 'react';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/lib/types/api';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: UserRole | UserRole[];
  fallback?: React.ReactNode;
  requireAll?: boolean; // If true, user must have ALL roles, otherwise ANY role
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback = null,
  requireAll = false 
}: RoleGuardProps) {
  const { user, hasRole } = useAuth();

  if (!user) {
    return <>{fallback}</>;
  }

  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  
  const hasAccess = requireAll
    ? roles.every(role => hasRole(role))
    : roles.some(role => hasRole(role));

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// Specific role components for convenience
export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={UserRole.ADMIN} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function DealershipOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={UserRole.DEALERSHIP} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function CustomerOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={UserRole.CUSTOMER} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function DealershipOrAdmin({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.DEALERSHIP, UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function AuthenticatedOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  const { isAuthenticated } = useAuth();
  
  return isAuthenticated ? <>{children}</> : <>{fallback}</>;
}

// Hook for role-based conditional logic
export function useRoleAccess() {
  const { user, hasRole, isAuthenticated } = useAuth();

  const checkAccess = (
    allowedRoles: UserRole | UserRole[], 
    requireAll: boolean = false
  ): boolean => {
    if (!isAuthenticated || !user) {
      return false;
    }

    const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
    
    return requireAll
      ? roles.every(role => hasRole(role))
      : roles.some(role => hasRole(role));
  };

  return {
    checkAccess,
    isAdmin: hasRole(UserRole.ADMIN),
    isDealership: hasRole(UserRole.DEALERSHIP),
    isCustomer: hasRole(UserRole.CUSTOMER),
    isAuthenticated,
    user,
  };
}