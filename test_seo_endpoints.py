#!/usr/bin/env python3
"""
Test script for SEO endpoints functionality.
Tests all SEO-related endpoints to ensure they're working correctly.
"""

import asyncio
import aiohttp
import json
from typing import Dict, List

class SEOEndpointTester:
    """Test SEO endpoints functionality."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.results = []
    
    async def test_endpoint(self, session: aiohttp.ClientSession, endpoint: str, expected_status: int = 200, description: str = "") -> Dict:
        """Test a single endpoint."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with session.get(url) as response:
                status = response.status
                content_type = response.headers.get('content-type', '')
                content_length = len(await response.text())
                
                success = status == expected_status
                
                result = {
                    "endpoint": endpoint,
                    "description": description,
                    "url": url,
                    "status": status,
                    "expected_status": expected_status,
                    "success": success,
                    "content_type": content_type,
                    "content_length": content_length
                }
                
                if success:
                    print(f"✅ {endpoint} - {description}")
                else:
                    print(f"❌ {endpoint} - {description} (Status: {status})")
                
                return result
                
        except Exception as e:
            result = {
                "endpoint": endpoint,
                "description": description,
                "url": url,
                "status": None,
                "expected_status": expected_status,
                "success": False,
                "error": str(e),
                "content_type": None,
                "content_length": 0
            }
            
            print(f"❌ {endpoint} - {description} (Error: {e})")
            return result
    
    async def test_json_endpoint(self, session: aiohttp.ClientSession, endpoint: str, description: str = "") -> Dict:
        """Test a JSON API endpoint."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with session.get(url) as response:
                status = response.status
                content_type = response.headers.get('content-type', '')
                
                if status == 200:
                    data = await response.json()
                    success = True
                    print(f"✅ {endpoint} - {description}")
                else:
                    data = None
                    success = False
                    print(f"❌ {endpoint} - {description} (Status: {status})")
                
                return {
                    "endpoint": endpoint,
                    "description": description,
                    "url": url,
                    "status": status,
                    "success": success,
                    "content_type": content_type,
                    "data": data
                }
                
        except Exception as e:
            print(f"❌ {endpoint} - {description} (Error: {e})")
            return {
                "endpoint": endpoint,
                "description": description,
                "url": url,
                "status": None,
                "success": False,
                "error": str(e)
            }
    
    async def run_tests(self):
        """Run all SEO endpoint tests."""
        print("🧪 Testing SEO Endpoints...")
        print("=" * 50)
        
        async with aiohttp.ClientSession() as session:
            # Test basic API health
            result = await self.test_endpoint(session, "/api/v1/", 200, "API Health Check")
            self.results.append(result)
            
            # Test SEO endpoints
            seo_tests = [
                # Core SEO endpoints
                ("/api/v1/seo/sitemap.xml", "Sitemap XML"),
                ("/api/v1/seo/robots.txt", "Robots.txt"),
                ("/sitemap.xml", "Root Sitemap XML"),
                ("/robots.txt", "Root Robots.txt"),
                
                # Part SEO pages (these might 404 if no data)
                ("/api/v1/seo/parts/TEST123", "Part SEO Page"),
                ("/parts/TEST123", "Root Part SEO Page"),
                
                # Search SEO pages
                ("/api/v1/seo/search/filtro", "Search SEO Page"),
                ("/search/filtro", "Root Search SEO Page"),
                
                # Category pages
                ("/api/v1/seo/categories/motor", "Category SEO Page"),
                ("/categories/motor", "Root Category SEO Page"),
            ]
            
            for endpoint, description in seo_tests:
                # Allow 404 for part/category pages if no data exists
                expected_status = 404 if any(x in endpoint for x in ['/parts/', '/categories/', '/dealerships/']) else 200
                result = await self.test_endpoint(session, endpoint, expected_status, description)
                self.results.append(result)
            
            # Test JSON API endpoints
            json_tests = [
                ("/api/v1/seo/popular-parts", "Popular Parts API"),
                ("/api/v1/seo/popular-searches", "Popular Searches API"),
            ]
            
            for endpoint, description in json_tests:
                result = await self.test_json_endpoint(session, endpoint, description)
                self.results.append(result)
            
            # Test cache management endpoints (POST)
            print("\n🔧 Testing Cache Management...")
            cache_tests = [
                ("/api/v1/seo/cache/invalidate-all", "Invalidate All Cache"),
                ("/api/v1/seo/cache/warm-popular?limit=5", "Warm Popular Parts Cache"),
                ("/api/v1/seo/cache/warm-searches?limit=5", "Warm Popular Searches Cache"),
            ]
            
            for endpoint, description in cache_tests:
                url = f"{self.base_url}{endpoint}"
                try:
                    async with session.post(url) as response:
                        status = response.status
                        if status == 200:
                            data = await response.json()
                            print(f"✅ {endpoint} - {description}")
                            success = True
                        else:
                            data = None
                            print(f"❌ {endpoint} - {description} (Status: {status})")
                            success = False
                        
                        self.results.append({
                            "endpoint": endpoint,
                            "description": description,
                            "method": "POST",
                            "status": status,
                            "success": success,
                            "data": data
                        })
                        
                except Exception as e:
                    print(f"❌ {endpoint} - {description} (Error: {e})")
                    self.results.append({
                        "endpoint": endpoint,
                        "description": description,
                        "method": "POST",
                        "success": False,
                        "error": str(e)
                    })
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 SEO ENDPOINT TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.get('success', False))
        failed_tests = total_tests - successful_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n⚠️  {failed_tests} tests failed. This might be expected if:")
            print("   - Backend is not running")
            print("   - Database has no sample data")
            print("   - Redis is not available")
        
        print(f"\n🎯 SEO System Status: {'✅ READY' if successful_tests >= total_tests * 0.7 else '⚠️ NEEDS ATTENTION'}")

async def main():
    """Main test function."""
    tester = SEOEndpointTester()
    await tester.run_tests()
    tester.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
