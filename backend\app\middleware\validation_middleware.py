"""
Request/Response validation middleware for enhanced API security and consistency.
"""
import time
import json
from typing import Callable, Any, Dict
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import ValidationError

from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger("app.middleware.validation")


class ValidationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for request/response validation and standardization.
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = {
            "/health",
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/favicon.ico"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and response with validation.
        """
        start_time = time.time()
        
        # Skip validation for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        # Validate request
        try:
            await self._validate_request(request)
        except HTTPException as e:
            return await self._create_error_response(e.status_code, e.detail, start_time)
        except Exception as e:
            logger.error(f"Request validation error: {e}")
            return await self._create_error_response(500, "Internal server error", start_time)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Validate and standardize response
            if response.status_code >= 400:
                response = await self._standardize_error_response(response, start_time)
            else:
                response = await self._standardize_success_response(response, start_time)
            
            return response
            
        except ValidationError as e:
            logger.error(f"Response validation error: {e}")
            return await self._create_error_response(422, "Validation error", start_time, {"validation_errors": e.errors()})
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return await self._create_error_response(500, "Internal server error", start_time)
    
    async def _validate_request(self, request: Request) -> None:
        """
        Validate incoming request.
        """
        # Check content type for POST/PUT requests
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if content_type and not content_type.startswith(("application/json", "multipart/form-data")):
                raise HTTPException(
                    status_code=415,
                    detail="Unsupported media type. Expected application/json or multipart/form-data"
                )
        
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"Request too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Validate API version if specified
        api_version = request.headers.get("api-version")
        if api_version and api_version not in ["v1", "1.0"]:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported API version: {api_version}"
            )
    
    async def _standardize_success_response(self, response: Response, start_time: float) -> Response:
        """
        Standardize successful response format.
        """
        # Add standard headers
        response.headers["X-API-Version"] = "v1"
        response.headers["X-Response-Time"] = str(round((time.time() - start_time) * 1000, 2))
        
        # For JSON responses, ensure consistent format
        if response.headers.get("content-type", "").startswith("application/json"):
            try:
                # Read response body
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                
                if body:
                    data = json.loads(body.decode())
                    
                    # Wrap in standard format if not already wrapped
                    if not isinstance(data, dict) or "success" not in data:
                        standardized_data = {
                            "success": True,
                            "data": data,
                            "timestamp": time.time()
                        }
                    else:
                        standardized_data = data
                        standardized_data["timestamp"] = time.time()
                    
                    return JSONResponse(
                        content=standardized_data,
                        status_code=response.status_code,
                        headers=dict(response.headers)
                    )
            except Exception as e:
                logger.warning(f"Failed to standardize response: {e}")
        
        return response
    
    async def _standardize_error_response(self, response: Response, start_time: float) -> Response:
        """
        Standardize error response format.
        """
        # Add standard headers
        response.headers["X-API-Version"] = "v1"
        response.headers["X-Response-Time"] = str(round((time.time() - start_time) * 1000, 2))
        
        try:
            # Read response body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            if body:
                try:
                    data = json.loads(body.decode())
                except json.JSONDecodeError:
                    data = {"detail": body.decode()}
                
                # Standardize error format
                error_response = {
                    "success": False,
                    "error": {
                        "code": response.status_code,
                        "message": data.get("detail", "An error occurred"),
                        "details": data if isinstance(data, dict) and "detail" not in data else None
                    },
                    "timestamp": time.time()
                }
                
                return JSONResponse(
                    content=error_response,
                    status_code=response.status_code,
                    headers=dict(response.headers)
                )
        except Exception as e:
            logger.error(f"Failed to standardize error response: {e}")
        
        return response
    
    async def _create_error_response(
        self, 
        status_code: int, 
        message: str, 
        start_time: float,
        details: Dict[str, Any] = None
    ) -> JSONResponse:
        """
        Create standardized error response.
        """
        error_response = {
            "success": False,
            "error": {
                "code": status_code,
                "message": message,
                "details": details
            },
            "timestamp": time.time()
        }
        
        headers = {
            "X-API-Version": "v1",
            "X-Response-Time": str(round((time.time() - start_time) * 1000, 2))
        }
        
        return JSONResponse(
            content=error_response,
            status_code=status_code,
            headers=headers
        )
