"use client"

import type React from "react"

import { useState } from "react"
import { useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { MapPin, Phone, Mail, Clock, Search, Tag, ShoppingCart } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Sample dealerships data (in a real app, this would come from an API)
const dealerships = [
  {
    id: "1",
    name: "AutoPeças Premium",
    brands: ["Toyota", "Honda", "Nissan"],
    address: "Av. Paulista, 1000",
    city: "São Paulo",
    state: "SP",
    phone: "(11) 3333-4444",
    whatsapp: "+5511999999999",
    email: "<EMAIL>",
    description: "Concessionária especializada em peças originais Toyota, Honda e Nissan.",
    image: "/placeholder.svg?height=200&width=400",
    openingHours: "Segunda a Sexta: 8h às 18h | Sábado: 8h às 13h",
    totalParts: 5842,
  },
  {
    id: "2",
    name: "Center Parts",
    brands: ["Volkswagen", "Fiat", "Chevrolet"],
    address: "Rua das Flores, 500",
    city: "Campinas",
    state: "SP",
    phone: "(19) 3333-4444",
    whatsapp: "+5519999999999",
    email: "<EMAIL>",
    description: "Amplo estoque de peças para veículos nacionais e importados.",
    image: "/placeholder.svg?height=200&width=400",
    openingHours: "Segunda a Sexta: 8h às 18h | Sábado: 8h às 13h",
    totalParts: 4231,
  },
  {
    id: "3",
    name: "Freios & Cia",
    brands: ["Ford", "Chevrolet", "Fiat"],
    address: "Av. Rio Branco, 150",
    city: "Rio de Janeiro",
    state: "RJ",
    phone: "(21) 3333-4444",
    whatsapp: "+5521999999999",
    email: "<EMAIL>",
    description: "Especializada em sistemas de freios e suspensão para todas as marcas.",
    image: "/placeholder.svg?height=200&width=400",
    openingHours: "Segunda a Sexta: 8h às 18h | Sábado: 8h às 13h",
    totalParts: 3156,
  },
]

// Sample products data
const generateProducts = (dealerId: string) => {
  const currentDate = new Date()

  return [
    {
      id: `${dealerId}-1`,
      name: "Kit de Embreagem Completo",
      code: "KEC-1234",
      price: 789.9,
      image: "/placeholder.svg?height=300&width=300",
      brand: "Sachs",
      isOriginal: true,
      inStock: true,
      isPromotion: true,
      oldPrice: 899.9,
      lastUpdate: new Date(currentDate.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
    },
    {
      id: `${dealerId}-2`,
      name: "Amortecedor Dianteiro",
      code: "AMD-5678",
      price: 349.9,
      image: "/placeholder.svg?height=300&width=300",
      brand: "Monroe",
      isOriginal: true,
      inStock: true,
      isPromotion: true,
      oldPrice: 399.9,
      lastUpdate: new Date(currentDate.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
    },
    {
      id: `${dealerId}-3`,
      name: "Filtro de Óleo",
      code: "FLO-9012",
      price: 39.9,
      image: "/placeholder.svg?height=300&width=300",
      brand: "Fram",
      isOriginal: false,
      inStock: true,
      isPromotion: false,
      lastUpdate: new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
    },
    {
      id: `${dealerId}-4`,
      name: "Pastilha de Freio Dianteira",
      code: "PFD-3456",
      price: 129.9,
      image: "/placeholder.svg?height=300&width=300",
      brand: "Frasle",
      isOriginal: true,
      inStock: false,
      isPromotion: true,
      oldPrice: 159.9,
      lastUpdate: new Date(currentDate.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
    },
  ]
}

// Function to calculate days since last update
const getDaysSinceUpdate = (lastUpdateDate: string): number => {
  const lastUpdate = new Date(lastUpdateDate)
  const today = new Date()
  const diffTime = Math.abs(today.getTime() - lastUpdate.getTime())
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// Function to format the last update message
const formatLastUpdate = (lastUpdateDate: string): string => {
  const days = getDaysSinceUpdate(lastUpdateDate)

  if (days === 0) {
    return "hoje"
  } else if (days === 1) {
    return "há 1 dia"
  } else {
    return `há ${days} dias`
  }
}

export default function DealerProfilePage() {
  const params = useParams()
  const dealerId = params.id as string

  // Find the dealer by ID
  const dealer = dealerships.find((d) => d.id === dealerId) || dealerships[0]

  // Generate products for this dealer
  const allProducts = generateProducts(dealerId)
  const promotionProducts = allProducts.filter((p) => p.isPromotion)

  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<typeof allProducts>([])
  const [hasSearched, setHasSearched] = useState(false)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()

    if (!searchQuery.trim()) {
      setSearchResults([])
      setHasSearched(false)
      return
    }

    // Filter products based on search query
    const results = allProducts.filter(
      (product) =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchQuery.toLowerCase()),
    )

    setSearchResults(results)
    setHasSearched(true)
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1">
        {/* Dealer Header */}
        <div className="relative h-48 w-full bg-gradient-to-r from-blue-600 to-blue-800">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container relative flex h-full flex-col justify-end pb-6">
            <div className="flex items-center gap-4">
              <div className="h-20 w-20 overflow-hidden rounded-lg bg-white p-1">
                <img
                  src={dealer.image || "/placeholder.svg"}
                  alt={dealer.name}
                  className="h-full w-full object-cover"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white md:text-3xl">{dealer.name}</h1>
                <div className="flex flex-wrap gap-1">
                  {dealer.brands.map((brand) => (
                    <Badge key={brand} variant="secondary" className="bg-white/20 text-white">
                      {brand}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="container py-8">
          <div className="grid gap-8 md:grid-cols-3">
            {/* Dealer Info */}
            <div className="md:col-span-1">
              <Card>
                <CardContent className="p-6">
                  <h2 className="mb-4 text-xl font-semibold">Informações</h2>

                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <MapPin className="mt-0.5 h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">Endereço</p>
                        <p className="text-sm text-muted-foreground">{dealer.address}</p>
                        <p className="text-sm text-muted-foreground">
                          {dealer.city}, {dealer.state}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Phone className="mt-0.5 h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">Telefone</p>
                        <p className="text-sm text-muted-foreground">{dealer.phone}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Mail className="mt-0.5 h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">Email</p>
                        <a href={`mailto:${dealer.email}`} className="text-sm text-primary hover:underline">
                          {dealer.email}
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Clock className="mt-0.5 h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">Horário de Funcionamento</p>
                        <p className="text-sm text-muted-foreground">{dealer.openingHours}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <ShoppingCart className="mt-0.5 h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">Estoque</p>
                        <p className="text-sm text-muted-foreground">
                          {dealer.totalParts.toLocaleString()} peças disponíveis
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <Button
                      className="w-full bg-green-500 hover:bg-green-600"
                      onClick={() => window.open(`https://wa.me/${dealer.whatsapp}`, "_blank")}
                    >
                      Contatar via WhatsApp
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="md:col-span-2">
              <Tabs defaultValue="search">
                <TabsList className="mb-6 w-full">
                  <TabsTrigger value="search" className="flex-1">
                    Buscar no Estoque
                  </TabsTrigger>
                  <TabsTrigger value="promotions" className="flex-1">
                    Promoções
                  </TabsTrigger>
                  <TabsTrigger value="about" className="flex-1">
                    Sobre
                  </TabsTrigger>
                </TabsList>

                {/* Search Tab */}
                <TabsContent value="search">
                  <Card>
                    <CardContent className="p-6">
                      <form onSubmit={handleSearch} className="mb-6">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Digite o código ou nome da peça..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="flex-1"
                          />
                          <Button type="submit">
                            <Search className="mr-2 h-4 w-4" />
                            Buscar
                          </Button>
                        </div>
                      </form>

                      {hasSearched && (
                        <div>
                          <h3 className="mb-4 text-lg font-medium">Resultados da busca</h3>

                          {searchResults.length > 0 ? (
                            <div className="overflow-x-auto rounded-md border">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead className="w-[120px]">CÓDIGO</TableHead>
                                    <TableHead>DESCRIÇÃO</TableHead>
                                    <TableHead>MARCA</TableHead>
                                    <TableHead className="text-right">PREÇO</TableHead>
                                    <TableHead className="w-[150px]">ATUALIZAÇÃO</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {searchResults.map((product) => (
                                    <TableRow key={product.id}>
                                      <TableCell>
                                        <span className="font-medium text-primary">{product.code}</span>
                                      </TableCell>
                                      <TableCell>{product.name}</TableCell>
                                      <TableCell>{product.brand}</TableCell>
                                      <TableCell className="text-right font-medium text-green-600">
                                        {new Intl.NumberFormat("pt-BR", {
                                          style: "currency",
                                          currency: "BRL",
                                        }).format(product.price)}
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex items-center">
                                          <Clock className="mr-1.5 h-3.5 w-3.5" />
                                          <span className="text-sm">{formatLastUpdate(product.lastUpdate)}</span>
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          ) : (
                            <div className="rounded-lg border border-dashed p-8 text-center">
                              <h3 className="mb-2 text-lg font-semibold">Nenhum resultado encontrado</h3>
                              <p className="text-muted-foreground">Tente buscar por outro termo</p>
                            </div>
                          )}
                        </div>
                      )}

                      {!hasSearched && (
                        <div className="rounded-lg border border-dashed p-8 text-center">
                          <h3 className="mb-2 text-lg font-semibold">Busque peças no estoque</h3>
                          <p className="text-muted-foreground">
                            Digite o código ou nome da peça para encontrar no estoque da {dealer.name}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Promotions Tab */}
                <TabsContent value="promotions">
                  <Card>
                    <CardContent className="p-6">
                      <div className="mb-4 flex items-center gap-2">
                        <Tag className="h-5 w-5 text-primary" />
                        <h3 className="text-lg font-medium">Peças em Promoção</h3>
                      </div>

                      {promotionProducts.length > 0 ? (
                        <div className="grid gap-6 sm:grid-cols-2">
                          {promotionProducts.map((product) => (
                            <div key={product.id} className="relative overflow-hidden rounded-lg border">
                              <Badge className="absolute right-2 top-2 bg-red-500">
                                {Math.round(((product.oldPrice! - product.price) / product.oldPrice!) * 100)}% OFF
                              </Badge>
                              <div className="flex gap-4 p-4">
                                <div className="h-24 w-24 shrink-0 overflow-hidden rounded-md bg-gray-100">
                                  <img
                                    src={product.image || "/placeholder.svg"}
                                    alt={product.name}
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                                <div className="flex flex-1 flex-col">
                                  <h4 className="font-medium">{product.name}</h4>
                                  <p className="text-sm text-muted-foreground">Código: {product.code}</p>
                                  <div className="mt-auto">
                                    <div className="text-sm line-through text-muted-foreground">
                                      {new Intl.NumberFormat("pt-BR", {
                                        style: "currency",
                                        currency: "BRL",
                                      }).format(product.oldPrice!)}
                                    </div>
                                    <div className="text-lg font-bold text-red-500">
                                      {new Intl.NumberFormat("pt-BR", {
                                        style: "currency",
                                        currency: "BRL",
                                      }).format(product.price)}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="rounded-lg border border-dashed p-8 text-center">
                          <h3 className="mb-2 text-lg font-semibold">Nenhuma promoção disponível</h3>
                          <p className="text-muted-foreground">
                            No momento não há peças em promoção nesta concessionária
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* About Tab */}
                <TabsContent value="about">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="mb-4 text-lg font-medium">Sobre {dealer.name}</h3>
                      <p className="mb-4 text-muted-foreground">{dealer.description}</p>

                      <h4 className="mb-2 font-medium">Marcas Atendidas</h4>
                      <div className="mb-6 flex flex-wrap gap-2">
                        {dealer.brands.map((brand) => (
                          <Badge key={brand} variant="outline">
                            {brand}
                          </Badge>
                        ))}
                      </div>

                      <h4 className="mb-2 font-medium">Localização</h4>
                      <div className="overflow-hidden rounded-lg border">
                        <div className="aspect-video bg-gray-100">
                          {/* In a real app, this would be a Google Maps embed */}
                          <div className="flex h-full items-center justify-center">
                            <p className="text-muted-foreground">Mapa de localização</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

