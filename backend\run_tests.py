"""
Test runner script for AutoParts API.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """Run a command and return the result."""
    print(f"\n🔄 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr and result.returncode != 0:
            print(f"❌ Error: {result.stderr}")
            return False
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
        else:
            print(f"❌ {description} failed with exit code {result.returncode}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="AutoParts API Test Runner")
    parser.add_argument(
        "--type",
        choices=["all", "unit", "integration", "coverage", "lint"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--fast",
        action="store_true",
        help="Skip slow tests"
    )
    parser.add_argument(
        "--parallel",
        "-n",
        type=int,
        default=1,
        help="Number of parallel workers"
    )
    
    args = parser.parse_args()
    
    print("🧪 AutoParts API Test Suite")
    print("=" * 60)
    print(f"Test type: {args.type}")
    print(f"Verbose: {args.verbose}")
    print(f"Fast mode: {args.fast}")
    print(f"Parallel workers: {args.parallel}")
    
    # Set environment variables
    os.environ["TESTING"] = "1"
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    
    # Base pytest command
    pytest_cmd = ["python", "-m", "pytest"]
    
    if args.verbose:
        pytest_cmd.append("-v")
    
    if args.fast:
        pytest_cmd.extend(["-m", "not slow"])
    
    if args.parallel > 1:
        pytest_cmd.extend(["-n", str(args.parallel)])
    
    success = True
    
    if args.type in ["all", "unit"]:
        # Run unit tests
        unit_cmd = pytest_cmd + [
            "tests/unit/",
            "-m", "unit",
            "--tb=short"
        ]
        success &= run_command(" ".join(unit_cmd), "Running Unit Tests")
    
    if args.type in ["all", "integration"]:
        # Run integration tests
        integration_cmd = pytest_cmd + [
            "tests/integration/",
            "-m", "integration",
            "--tb=short"
        ]
        success &= run_command(" ".join(integration_cmd), "Running Integration Tests")
    
    if args.type in ["all", "coverage"]:
        # Run tests with coverage
        coverage_cmd = pytest_cmd + [
            "tests/",
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-report=xml:coverage.xml",
            "--cov-fail-under=80"
        ]
        success &= run_command(" ".join(coverage_cmd), "Running Coverage Analysis")
        
        # Generate coverage report
        if success:
            print("\n📊 Coverage Report Generated:")
            print("  - HTML: htmlcov/index.html")
            print("  - XML: coverage.xml")
    
    if args.type in ["all", "lint"]:
        # Run linting
        lint_commands = [
            ("python -m flake8 app/ --max-line-length=100 --ignore=E203,W503", "Running Flake8 Linting"),
            ("python -m black --check app/", "Running Black Code Formatting Check"),
            ("python -m isort --check-only app/", "Running Import Sorting Check"),
            ("python -m mypy app/ --ignore-missing-imports", "Running Type Checking")
        ]
        
        for cmd, desc in lint_commands:
            success &= run_command(cmd, desc)
    
    # Summary
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed successfully!")
        print("\n📋 Next Steps:")
        print("  1. Review coverage report in htmlcov/index.html")
        print("  2. Fix any linting issues if found")
        print("  3. Add more tests for uncovered code")
        print("  4. Run tests in CI/CD pipeline")
    else:
        print("❌ Some tests failed. Please review the output above.")
        print("\n🔧 Troubleshooting:")
        print("  1. Check test failures and fix issues")
        print("  2. Ensure all dependencies are installed")
        print("  3. Verify database connectivity")
        print("  4. Check environment variables")
        sys.exit(1)


if __name__ == "__main__":
    main()
