import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ScrollToTop } from "@/components/scroll-to-top"
import { FloatingThemeToggle } from "@/components/floating-theme-toggle"
import { QueryProvider } from "@/lib/providers/QueryProvider"
import { AuthProvider } from "@/lib/auth/context"
import { Toaster } from "sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "AutoParts - Encontre peças para seu veículo",
  description: "Plataforma de busca de peças automotivas",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <body className={inter.className}>
        <QueryProvider>
          <AuthProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem
              disableTransitionOnChange={false}
              storageKey="autoparts-theme"
            >
              <div className="flex min-h-screen flex-col bg-background text-foreground transition-colors duration-300">
                <ScrollToTop />
                {children}
                <FloatingThemeToggle />
                <Toaster richColors position="top-right" />
              </div>
            </ThemeProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  )
}

