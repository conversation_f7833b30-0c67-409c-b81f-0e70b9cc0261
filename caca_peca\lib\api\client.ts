/**
 * AutoParts API Client
 * Comprehensive TypeScript client for the AutoParts backend API
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { jwtService } from '../auth/jwt-service';
import {
  ApiResponse,
  ErrorResponse,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  User,
  UserCreate
} from '../types/api';

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';

class AutoPartsAPIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const authHeader = jwtService.getAuthorizationHeader();
        if (authHeader) {
          config.headers.Authorization = authHeader;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Check if token should be refreshed
            if (jwtService.shouldRefreshToken()) {
              await jwtService.refreshAccessToken();
              const newAuthHeader = jwtService.getAuthorizationHeader();
              if (newAuthHeader) {
                originalRequest.headers.Authorization = newAuthHeader;
              }
              return this.client(originalRequest);
            } else {
              throw new Error('Token refresh not needed or failed');
            }
          } catch (refreshError) {
            jwtService.clearTokens();
            // Redirect to login or emit auth error event
            if (typeof window !== 'undefined') {
              window.location.href = '/auth';
            }
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }



  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await this.client.post<LoginResponse>('/auth/login', credentials);
      const loginData = response.data;

      // Store tokens using JWT service
      jwtService.setTokens(loginData.access_token, loginData.refresh_token);

      // Schedule automatic token refresh
      jwtService.scheduleTokenRefresh();

      return {
        data: loginData,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async logout(): Promise<ApiResponse<void>> {
    try {
      await this.client.post('/auth/logout');
      jwtService.clearTokens();

      return { success: true };
    } catch (error: any) {
      jwtService.clearTokens(); // Clear tokens even if logout fails
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }



  async register(userData: UserCreate): Promise<ApiResponse<User>> {
    try {
      const response = await this.client.post<User>('/auth/register', userData);

      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await this.client.get<User>('/auth/me');

      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  // Utility methods
  isAuthenticated(): boolean {
    return jwtService.isAuthenticated();
  }

  getAccessToken(): string | null {
    return jwtService.getAccessToken();
  }

  // HTTP methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.get(url, config);
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.post(url, data, config);
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.put(url, data, config);
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(url, data, config);
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(url, config);
      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  // Generic request method
  async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.client(config);

      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }

  // Error handling
  private handleError(error: any): ErrorResponse {
    if (error.response) {
      // Server responded with error status
      return {
        error: error.response.data?.error || 'API Error',
        message: error.response.data?.message || error.message,
        details: error.response.data?.details,
        timestamp: Date.now()
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        error: 'Network Error',
        message: 'Unable to connect to the server. Please check your internet connection.',
        timestamp: Date.now()
      };
    } else {
      // Something else happened
      return {
        error: 'Unknown Error',
        message: error.message || 'An unexpected error occurred',
        timestamp: Date.now()
      };
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    try {
      const response = await axios.get(`${API_BASE_URL}/health`);

      return {
        data: response.data,
        success: true
      };
    } catch (error: any) {
      return {
        error: this.handleError(error),
        success: false
      };
    }
  }
}

// Export singleton instance
export const apiClient = new AutoPartsAPIClient();
export default apiClient;
