"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapPin, Phone, MessageCircle } from "lucide-react"

// Sample data
const BRANDS = [
  { value: "all", label: "Todas as marcas" },
  { value: "toyota", label: "Toyota" },
  { value: "volkswagen", label: "Volkswagen" },
  { value: "ford", label: "Ford" },
  { value: "chevrolet", label: "Chevrolet" },
  { value: "honda", label: "Honda" },
  { value: "hyundai", label: "Hyundai" },
  { value: "nissan", label: "Nissan" },
  { value: "fiat", label: "Fiat" },
  { value: "bmw", label: "BMW" },
  { value: "mercedes", label: "Mercedes-Benz" },
]

const STATES = [
  { value: "all", label: "Todos os estados" },
  { value: "SP", label: "São Paulo" },
  { value: "RJ", label: "Rio de Janeiro" },
  { value: "MG", label: "Minas Gerais" },
  { value: "PR", label: "Paraná" },
  { value: "RS", label: "Rio Grande do Sul" },
  { value: "SC", label: "Santa Catarina" },
]

const CITIES = [
  { value: "all", label: "Todas as cidades" },
  { value: "sao-paulo", label: "São Paulo" },
  { value: "campinas", label: "Campinas" },
  { value: "rio-de-janeiro", label: "Rio de Janeiro" },
  { value: "belo-horizonte", label: "Belo Horizonte" },
  { value: "curitiba", label: "Curitiba" },
  { value: "porto-alegre", label: "Porto Alegre" },
]

// Sample dealerships data
const dealerships = [
  {
    id: "1",
    name: "AutoPeças Premium",
    brands: ["toyota", "honda", "nissan"],
    address: "Av. Paulista, 1000",
    city: "São Paulo",
    state: "SP",
    phone: "(11) 3333-4444",
    whatsapp: "11999999999",
    description: "Concessionária especializada em peças originais Toyota, Honda e Nissan.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "2",
    name: "Center Parts",
    brands: ["volkswagen", "fiat", "chevrolet"],
    address: "Rua das Flores, 500",
    city: "Campinas",
    state: "SP",
    phone: "(19) 3333-4444",
    whatsapp: "19999999999",
    description: "Amplo estoque de peças para veículos nacionais e importados.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "3",
    name: "Freios & Cia",
    brands: ["ford", "chevrolet", "fiat"],
    address: "Av. Rio Branco, 150",
    city: "Rio de Janeiro",
    state: "RJ",
    phone: "(21) 3333-4444",
    whatsapp: "21999999999",
    description: "Especializada em sistemas de freios e suspensão para todas as marcas.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "4",
    name: "Amortex",
    brands: ["hyundai", "kia", "toyota"],
    address: "Av. Afonso Pena, 1500",
    city: "Belo Horizonte",
    state: "MG",
    phone: "(31) 3333-4444",
    whatsapp: "31999999999",
    description: "Amortecedores e suspensão para veículos nacionais e importados.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "5",
    name: "Sul Peças",
    brands: ["volkswagen", "audi", "bmw"],
    address: "Av. Ipiranga, 1200",
    city: "Porto Alegre",
    state: "RS",
    phone: "(51) 3333-4444",
    whatsapp: "51999999999",
    description: "Especializada em veículos alemães com peças originais e paralelas.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "6",
    name: "Paraná Autopeças",
    brands: ["fiat", "renault", "peugeot"],
    address: "Rua XV de Novembro, 700",
    city: "Curitiba",
    state: "PR",
    phone: "(41) 3333-4444",
    whatsapp: "41999999999",
    description: "Peças para veículos nacionais e importados com os melhores preços.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "7",
    name: "Mercedes Peças Originais",
    brands: ["mercedes"],
    address: "Av. Brasil, 2000",
    city: "São Paulo",
    state: "SP",
    phone: "(11) 3333-5555",
    whatsapp: "11988888888",
    description: "Concessionária oficial Mercedes-Benz com peças originais e serviços especializados.",
    image: "/placeholder.svg?height=200&width=400",
  },
  {
    id: "8",
    name: "BMW Premium",
    brands: ["bmw"],
    address: "Av. Rebouças, 1800",
    city: "São Paulo",
    state: "SP",
    phone: "(11) 3333-6666",
    whatsapp: "11977777777",
    description: "Peças originais e serviços especializados para veículos BMW.",
    image: "/placeholder.svg?height=200&width=400",
  },
]

export default function ConcessionariasPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedState, setSelectedState] = useState("all")
  const [selectedCity, setSelectedCity] = useState("all")

  // Função para formatar o número do WhatsApp corretamente
  const formatWhatsAppNumber = (number: string) => {
    // Remove todos os caracteres não numéricos
    const cleanNumber = number.replace(/\D/g, "")

    // Se o número não começar com +55, adiciona o código do país
    if (!cleanNumber.startsWith("55")) {
      return `55${cleanNumber}`
    }

    return cleanNumber
  }

  // Função para criar o link do WhatsApp
  const createWhatsAppLink = (number: string, message?: string) => {
    const formattedNumber = formatWhatsAppNumber(number)
    const baseUrl = "https://wa.me"
    const defaultMessage = "Olá! Gostaria de mais informações sobre peças."

    const encodedMessage = encodeURIComponent(message || defaultMessage)
    return `${baseUrl}/${formattedNumber}?text=${encodedMessage}`
  }

  // Função para abrir o WhatsApp
  const handleWhatsAppClick = (number: string) => {
    const whatsappUrl = createWhatsAppLink(number)
    window.open(whatsappUrl, "_blank")
  }

  // Filter dealerships based on search and filters
  const filteredDealerships = dealerships.filter((dealership) => {
    const matchesSearch =
      searchTerm === "" ||
      dealership.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dealership.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesBrand = selectedBrand === "all" || dealership.brands.includes(selectedBrand)
    const matchesState = selectedState === "all" || dealership.state === selectedState
    const matchesCity =
      selectedCity === "all" ||
      dealership.city.toLowerCase() === CITIES.find((c) => c.value === selectedCity)?.label.toLowerCase()

    return matchesSearch && matchesBrand && matchesState && matchesCity
  })

  const handleDealerClick = (dealerId: string) => {
    router.push(`/concessionarias/${dealerId}`)
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1">
        <div className="bg-gradient-to-b from-blue-50 to-white py-12 dark:from-gray-900 dark:to-gray-950">
          <div className="container">
            <h1 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl">Concessionárias</h1>
            <p className="mb-8 text-lg text-muted-foreground">Encontre concessionárias parceiras em todo o Brasil</p>

            <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <Label htmlFor="search" className="mb-2 block">
                  Buscar
                </Label>
                <Input
                  id="search"
                  placeholder="Nome da concessionária"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="brand" className="mb-2 block">
                  Marca
                </Label>
                <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                  <SelectTrigger id="brand">
                    <SelectValue placeholder="Selecione uma marca" />
                  </SelectTrigger>
                  <SelectContent>
                    {BRANDS.map((brand) => (
                      <SelectItem key={brand.value} value={brand.value}>
                        {brand.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="state" className="mb-2 block">
                  Estado
                </Label>
                <Select value={selectedState} onValueChange={setSelectedState}>
                  <SelectTrigger id="state">
                    <SelectValue placeholder="Selecione um estado" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATES.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="city" className="mb-2 block">
                  Cidade
                </Label>
                <Select value={selectedCity} onValueChange={setSelectedCity}>
                  <SelectTrigger id="city">
                    <SelectValue placeholder="Selecione uma cidade" />
                  </SelectTrigger>
                  <SelectContent>
                    {CITIES.map((city) => (
                      <SelectItem key={city.value} value={city.value}>
                        {city.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mb-4 flex items-center justify-between">
              <p className="text-sm text-muted-foreground">{filteredDealerships.length} concessionárias encontradas</p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("")
                  setSelectedBrand("all")
                  setSelectedState("all")
                  setSelectedCity("all")
                }}
              >
                Limpar filtros
              </Button>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredDealerships.map((dealership) => (
                <Card key={dealership.id} className="overflow-hidden">
                  <div className="relative h-48 w-full">
                    <img
                      src={dealership.image || "/placeholder.svg"}
                      alt={dealership.name}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3
                      className="mb-2 text-xl font-bold text-primary hover:underline cursor-pointer"
                      onClick={() => handleDealerClick(dealership.id)}
                    >
                      {dealership.name}
                    </h3>
                    <p className="mb-4 text-sm text-muted-foreground">{dealership.description}</p>

                    <div className="mb-4 flex items-start gap-2 text-sm">
                      <MapPin className="mt-0.5 h-4 w-4 shrink-0 text-muted-foreground" />
                      <div>
                        <p>{dealership.address}</p>
                        <p>
                          {dealership.city}, {dealership.state}
                        </p>
                      </div>
                    </div>

                    <div className="mb-4 flex flex-wrap gap-1">
                      {dealership.brands.map((brand) => (
                        <span
                          key={brand}
                          className="inline-block rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                        >
                          {BRANDS.find((b) => b.value === brand)?.label || brand}
                        </span>
                      ))}
                    </div>

                    <div className="flex flex-col gap-2 sm:flex-row">
                      {/* Substituído o botão por um div não clicável */}
                      <div className="flex h-9 items-center justify-center gap-1 rounded-md border border-input bg-background px-3 text-sm font-medium text-foreground sm:flex-1">
                        <Phone className="h-4 w-4" />
                        {dealership.phone}
                      </div>

                      <Button
                        size="sm"
                        className="flex items-center justify-center gap-1 bg-green-500 hover:bg-green-600 sm:flex-1"
                        onClick={() => handleWhatsAppClick(dealership.whatsapp)}
                      >
                        <MessageCircle className="h-4 w-4" />
                        WhatsApp
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredDealerships.length === 0 && (
              <div className="mt-8 rounded-lg border border-dashed p-8 text-center">
                <h3 className="mb-2 text-lg font-semibold">Nenhuma concessionária encontrada</h3>
                <p className="text-muted-foreground">Tente ajustar seus filtros ou buscar por outro termo</p>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

