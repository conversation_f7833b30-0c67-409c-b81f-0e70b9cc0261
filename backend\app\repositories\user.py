"""
User repository for authentication and user management.
"""
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from uuid import UUID

from app.models.user import User, UserRole, UserStatus
from app.repositories.base import BaseRepository
from app.core.security import get_password_hash, verify_password


class UserRepository(BaseRepository[User]):
    """
    Repository for user-specific database operations.
    """

    def __init__(self, db: Session):
        super().__init__(User, db)

    def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.
        
        Args:
            email: Email address
            
        Returns:
            User instance or None if not found
        """
        return self.db.query(User).filter(User.email == email).first()

    def get_by_api_key(self, api_key: str) -> Optional[User]:
        """
        Get user by API key.
        
        Args:
            api_key: API key
            
        Returns:
            User instance or None if not found
        """
        return self.db.query(User).filter(User.api_key == api_key).first()

    def create_user(
        self,
        email: str,
        password: str,
        full_name: str,
        role: UserRole = UserRole.CUSTOMER,
        dealership_id: Optional[UUID] = None,
        auto_activate: bool = False
    ) -> User:
        """
        Create a new user with hashed password.

        Args:
            email: User email
            password: Plain text password
            full_name: User's full name
            role: User role
            dealership_id: Associated dealership ID
            auto_activate: Whether to auto-activate the user (for development)

        Returns:
            Created user instance
        """
        password_hash = get_password_hash(password)

        # For development, auto-activate users to skip email verification
        status = UserStatus.ACTIVE if auto_activate else UserStatus.PENDING_VERIFICATION
        email_verified = auto_activate

        user_data = {
            "email": email,
            "password_hash": password_hash,
            "full_name": full_name,
            "role": role,
            "dealership_id": dealership_id,
            "status": status,
            "email_verified": email_verified
        }

        return self.create(user_data)

    def authenticate(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password.
        
        Args:
            email: User email
            password: Plain text password
            
        Returns:
            User instance if authentication successful, None otherwise
        """
        user = self.get_by_email(email)
        if not user:
            return None
        
        if not verify_password(password, user.password_hash):
            # Increment login attempts
            user.increment_login_attempts()
            self.db.commit()
            return None
        
        # Reset login attempts on successful authentication
        user.reset_login_attempts()
        user.update_last_login()
        self.db.commit()
        
        return user

    def get_active_users(
        self,
        skip: int = 0,
        limit: int = 100,
        role: Optional[UserRole] = None
    ) -> List[User]:
        """
        Get active users with optional role filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            role: Filter by user role
            
        Returns:
            List of active user instances
        """
        query = self.db.query(User).filter(User.status == UserStatus.ACTIVE)
        
        if role:
            query = query.filter(User.role == role)
        
        return query.offset(skip).limit(limit).all()

    def get_dealership_users(self, dealership_id: UUID) -> List[User]:
        """
        Get all users associated with a dealership.
        
        Args:
            dealership_id: Dealership UUID
            
        Returns:
            List of user instances
        """
        return self.db.query(User).filter(
            and_(
                User.dealership_id == dealership_id,
                User.role == UserRole.DEALERSHIP
            )
        ).all()

    def search_users(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        role: Optional[UserRole] = None
    ) -> List[User]:
        """
        Search users by email or full name.
        
        Args:
            search_term: Search term to match against email or full name
            skip: Number of records to skip
            limit: Maximum number of records to return
            role: Filter by user role
            
        Returns:
            List of matching user instances
        """
        search_pattern = f"%{search_term}%"
        query = self.db.query(User).filter(
            or_(
                User.email.ilike(search_pattern),
                User.full_name.ilike(search_pattern)
            )
        )
        
        if role:
            query = query.filter(User.role == role)
        
        return query.offset(skip).limit(limit).all()

    def update_password(self, user_id: UUID, new_password: str) -> Optional[User]:
        """
        Update user password.
        
        Args:
            user_id: User UUID
            new_password: New plain text password
            
        Returns:
            Updated user instance or None if not found
        """
        password_hash = get_password_hash(new_password)
        return self.update(user_id, {"password_hash": password_hash})

    def set_password_reset_token(
        self,
        user_id: UUID,
        token: str,
        expires_at: Optional[str] = None
    ) -> Optional[User]:
        """
        Set password reset token for user.
        
        Args:
            user_id: User UUID
            token: Reset token
            expires_at: Token expiration timestamp
            
        Returns:
            Updated user instance or None if not found
        """
        return self.update(user_id, {
            "password_reset_token": token,
            "password_reset_expires": expires_at
        })

    def verify_email(self, user_id: UUID) -> Optional[User]:
        """
        Mark user email as verified and activate account.
        
        Args:
            user_id: User UUID
            
        Returns:
            Updated user instance or None if not found
        """
        return self.update(user_id, {
            "email_verified": True,
            "status": UserStatus.ACTIVE,
            "email_verification_token": None
        })

    def activate_user(self, user_id: UUID) -> Optional[User]:
        """
        Activate user account.
        
        Args:
            user_id: User UUID
            
        Returns:
            Updated user instance or None if not found
        """
        return self.update(user_id, {"status": UserStatus.ACTIVE})

    def suspend_user(self, user_id: UUID) -> Optional[User]:
        """
        Suspend user account.
        
        Args:
            user_id: User UUID
            
        Returns:
            Updated user instance or None if not found
        """
        return self.update(user_id, {"status": UserStatus.SUSPENDED})

    def set_api_key(self, user_id: UUID, api_key: str) -> Optional[User]:
        """
        Set API key for user.
        
        Args:
            user_id: User UUID
            api_key: API key
            
        Returns:
            Updated user instance or None if not found
        """
        return self.update(user_id, {"api_key": api_key})
