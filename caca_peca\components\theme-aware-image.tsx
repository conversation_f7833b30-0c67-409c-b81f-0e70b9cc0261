"use client"

import * as React from "react"
import Image from "next/image"
import { useTheme } from "next-themes"

interface ThemeAwareImageProps {
  lightSrc: string
  darkSrc: string
  alt: string
  width: number
  height: number
  className?: string
}

export function ThemeAwareImage({ lightSrc, darkSrc, alt, width, height, className }: ThemeAwareImageProps) {
  const { resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    // Return a placeholder or the light theme image during SSR
    return <div className={`bg-muted ${className}`} style={{ width, height }} aria-hidden="true" />
  }

  const src = resolvedTheme === "dark" ? darkSrc : lightSrc

  return <Image src={src || "/placeholder.svg"} alt={alt} width={width} height={height} className={className} />
}

