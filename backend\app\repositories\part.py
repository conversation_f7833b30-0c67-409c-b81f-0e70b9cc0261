"""
Part repository for data access operations.
"""
from typing import Optional, List, Tuple, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text, desc, asc
from uuid import UUID

from app.models.part import Part
from app.models.inventory import Inventory
from app.repositories.base import BaseRepository


class PartRepository(BaseRepository[Part]):
    """
    Repository for part-specific database operations.
    """

    def __init__(self, db: Session):
        super().__init__(Part, db)

    def get_by_code(self, code: str) -> Optional[Part]:
        """
        Get part by code.
        
        Args:
            code: Part code
            
        Returns:
            Part instance or None if not found
        """
        return self.db.query(Part).filter(Part.code == code.upper()).first()

    def get_by_oem_code(self, oem_code: str) -> Optional[Part]:
        """
        Get part by OEM code.
        
        Args:
            oem_code: OEM code
            
        Returns:
            Part instance or None if not found
        """
        return self.db.query(Part).filter(Part.oem_code == oem_code.upper()).first()

    def search_parts(
        self,
        search_term: Optional[str] = None,
        code: Optional[str] = None,
        brand: Optional[str] = None,
        model: Optional[str] = None,
        year: Optional[int] = None,
        year_start: Optional[int] = None,
        year_end: Optional[int] = None,
        category: Optional[str] = None,
        subcategory: Optional[str] = None,
        has_inventory: bool = False,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "name",
        sort_order: str = "asc"
    ) -> Tuple[List[Part], int]:
        """
        Search parts with advanced filtering.
        
        Args:
            search_term: General search term
            code: Exact part code
            brand: Vehicle brand filter
            model: Vehicle model filter
            year: Specific year filter
            year_start: Year range start
            year_end: Year range end
            category: Category filter
            subcategory: Subcategory filter
            has_inventory: Only parts with inventory
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            
        Returns:
            Tuple of (parts list, total count)
        """
        query = self.db.query(Part)
        
        # Apply filters
        conditions = []
        
        # General search term (full-text search)
        if search_term:
            search_pattern = f"%{search_term}%"
            conditions.append(
                or_(
                    Part.code.ilike(search_pattern),
                    Part.name.ilike(search_pattern),
                    Part.description.ilike(search_pattern),
                    Part.oem_code.ilike(search_pattern),
                    Part.alternative_codes.ilike(search_pattern)
                )
            )
        
        # Exact code search
        if code:
            conditions.append(Part.code == code.upper())
        
        # Brand filter
        if brand:
            conditions.append(Part.brand.ilike(f"%{brand}%"))
        
        # Model filter
        if model:
            conditions.append(Part.model.ilike(f"%{model}%"))
        
        # Year filters
        if year:
            conditions.append(
                or_(
                    Part.year == year,
                    and_(
                        Part.year_start <= year,
                        Part.year_end >= year
                    ),
                    and_(
                        Part.year_start.is_(None),
                        Part.year_end.is_(None),
                        Part.year == year
                    )
                )
            )
        
        if year_start and year_end:
            conditions.append(
                or_(
                    and_(Part.year >= year_start, Part.year <= year_end),
                    and_(Part.year_start >= year_start, Part.year_start <= year_end),
                    and_(Part.year_end >= year_start, Part.year_end <= year_end)
                )
            )
        
        # Category filters
        if category:
            conditions.append(Part.category.ilike(f"%{category}%"))
        
        if subcategory:
            conditions.append(Part.subcategory.ilike(f"%{subcategory}%"))
        
        # Apply all conditions
        if conditions:
            query = query.filter(and_(*conditions))
        
        # Filter by inventory availability
        if has_inventory:
            query = query.join(Inventory).filter(
                and_(
                    Inventory.quantity > 0,
                    Inventory.is_available == True
                )
            ).distinct()
        
        # Get total count before pagination
        total = query.count()
        
        # Apply sorting
        sort_field = getattr(Part, sort_by, Part.name)
        if sort_order == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(asc(sort_field))
        
        # Apply pagination
        parts = query.offset(skip).limit(limit).all()
        
        return parts, total

    def full_text_search(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Part], int]:
        """
        Perform full-text search using PostgreSQL.
        
        Args:
            search_term: Search term
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (parts list, total count)
        """
        # Use PostgreSQL full-text search
        search_query = text("""
            SELECT p.*, ts_rank(search_vector, plainto_tsquery('portuguese', :search_term)) as rank
            FROM parts p,
                 to_tsvector('portuguese', p.name || ' ' || COALESCE(p.description, '')) as search_vector
            WHERE search_vector @@ plainto_tsquery('portuguese', :search_term)
            ORDER BY rank DESC, p.name
            OFFSET :skip LIMIT :limit
        """)
        
        count_query = text("""
            SELECT COUNT(*)
            FROM parts p,
                 to_tsvector('portuguese', p.name || ' ' || COALESCE(p.description, '')) as search_vector
            WHERE search_vector @@ plainto_tsquery('portuguese', :search_term)
        """)
        
        # Execute search
        result = self.db.execute(
            search_query,
            {"search_term": search_term, "skip": skip, "limit": limit}
        )
        
        # Get total count
        total_result = self.db.execute(count_query, {"search_term": search_term})
        total = total_result.scalar()
        
        # Convert results to Part objects
        parts = []
        for row in result:
            part = self.db.query(Part).filter(Part.id == row.id).first()
            if part:
                parts.append(part)
        
        return parts, total or 0

    def get_similar_parts(self, part_id: UUID, limit: int = 10) -> List[Part]:
        """
        Get similar parts based on brand, model, and category.
        
        Args:
            part_id: Reference part ID
            limit: Maximum number of similar parts to return
            
        Returns:
            List of similar parts
        """
        reference_part = self.get_by_id(part_id)
        if not reference_part:
            return []
        
        query = self.db.query(Part).filter(Part.id != part_id)
        
        # Build similarity conditions
        conditions = []
        
        if reference_part.brand:
            conditions.append(Part.brand == reference_part.brand)
        
        if reference_part.model:
            conditions.append(Part.model == reference_part.model)
        
        if reference_part.category:
            conditions.append(Part.category == reference_part.category)
        
        if reference_part.year:
            conditions.append(
                or_(
                    Part.year == reference_part.year,
                    and_(
                        Part.year_start <= reference_part.year,
                        Part.year_end >= reference_part.year
                    )
                )
            )
        
        # Apply conditions with OR logic for flexibility
        if conditions:
            query = query.filter(or_(*conditions))
        
        return query.limit(limit).all()

    def get_parts_by_alternative_code(self, alternative_code: str) -> List[Part]:
        """
        Get parts that have the given code as an alternative.
        
        Args:
            alternative_code: Alternative part code to search for
            
        Returns:
            List of parts with matching alternative code
        """
        search_pattern = f"%{alternative_code.upper()}%"
        return self.db.query(Part).filter(
            Part.alternative_codes.ilike(search_pattern)
        ).all()

    def get_parts_by_category(
        self,
        category: str,
        subcategory: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Part], int]:
        """
        Get parts by category and optional subcategory.
        
        Args:
            category: Part category
            subcategory: Optional subcategory
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (parts list, total count)
        """
        query = self.db.query(Part).filter(Part.category.ilike(f"%{category}%"))
        
        if subcategory:
            query = query.filter(Part.subcategory.ilike(f"%{subcategory}%"))
        
        total = query.count()
        parts = query.offset(skip).limit(limit).all()
        
        return parts, total

    def get_parts_by_brand_model(
        self,
        brand: str,
        model: Optional[str] = None,
        year: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Part], int]:
        """
        Get parts by brand, model, and optional year.
        
        Args:
            brand: Vehicle brand
            model: Optional vehicle model
            year: Optional vehicle year
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (parts list, total count)
        """
        query = self.db.query(Part).filter(Part.brand.ilike(f"%{brand}%"))
        
        if model:
            query = query.filter(Part.model.ilike(f"%{model}%"))
        
        if year:
            query = query.filter(
                or_(
                    Part.year == year,
                    and_(
                        Part.year_start <= year,
                        Part.year_end >= year
                    )
                )
            )
        
        total = query.count()
        parts = query.offset(skip).limit(limit).all()
        
        return parts, total

    def get_parts_with_inventory_stats(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get parts with inventory statistics.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of parts with inventory stats
        """
        query = self.db.query(
            Part,
            func.count(Inventory.id).label('inventory_count'),
            func.min(Inventory.price).label('min_price'),
            func.max(Inventory.price).label('max_price'),
            func.sum(Inventory.quantity).label('total_quantity'),
            func.count(func.distinct(Inventory.dealership_id)).label('dealership_count')
        ).outerjoin(Inventory).group_by(Part.id)
        
        results = query.offset(skip).limit(limit).all()
        
        return [
            {
                'part': result.Part,
                'inventory_count': result.inventory_count or 0,
                'min_price': float(result.min_price) if result.min_price else None,
                'max_price': float(result.max_price) if result.max_price else None,
                'total_quantity': result.total_quantity or 0,
                'dealership_count': result.dealership_count or 0
            }
            for result in results
        ]

    def count_by_category(self) -> List[Tuple[str, int]]:
        """
        Count parts by category.
        
        Returns:
            List of tuples (category, count)
        """
        return (
            self.db.query(Part.category, func.count(Part.id))
            .filter(Part.category.isnot(None))
            .group_by(Part.category)
            .order_by(func.count(Part.id).desc())
            .all()
        )

    def count_by_brand(self) -> List[Tuple[str, int]]:
        """
        Count parts by brand.
        
        Returns:
            List of tuples (brand, count)
        """
        return (
            self.db.query(Part.brand, func.count(Part.id))
            .filter(Part.brand.isnot(None))
            .group_by(Part.brand)
            .order_by(func.count(Part.id).desc())
            .all()
        )
