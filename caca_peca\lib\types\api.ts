/**
 * TypeScript types for AutoParts API
 * Generated from backend Pydantic schemas
 */

// Base types
export interface BaseResponse {
  message?: string;
  timestamp?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ErrorResponse {
  error: string;
  message: string;
  details?: Record<string, any>;
  timestamp: number;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface UserCreate {
  email: string;
  password: string;
  full_name: string;
  role?: UserRole;
  dealership_id?: string;
}

export interface UserUpdate {
  full_name?: string;
  email?: string;
  role?: UserRole;
  status?: UserStatus;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

export interface PasswordChangeRequest {
  current_password: string;
  new_password: string;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  role: UserRole;
  status: UserStatus;
  is_active?: boolean;
  email_verified?: boolean;
  dealership_id?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export enum UserRole {
  ADMIN = "admin",
  DEALERSHIP = "dealership",
  CUSTOMER = "customer"
}

export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended"
}

// Dealership types
export interface Dealership {
  id: string;
  name: string;
  cnpj: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  is_active: boolean;
  subscription_status: SubscriptionStatus;
  created_at: string;
  updated_at: string;
}

export interface DealershipCreate {
  name: string;
  cnpj: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
}

export interface DealershipUpdate {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
}

// Parts types
export interface Part {
  id: string;
  part_number: string;
  name: string;
  description?: string;
  brand: string;
  category: string;
  subcategory?: string;
  oem_numbers: string[];
  compatible_vehicles: VehicleCompatibility[];
  specifications: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PartCreate {
  part_number: string;
  name: string;
  description?: string;
  brand: string;
  category: string;
  subcategory?: string;
  oem_numbers?: string[];
  compatible_vehicles?: VehicleCompatibility[];
  specifications?: Record<string, any>;
}

export interface VehicleCompatibility {
  make: string;
  model: string;
  year_start: number;
  year_end: number;
  engine?: string;
  trim?: string;
}

export interface PartSearchRequest {
  query?: string;
  part_number?: string;
  brand?: string;
  category?: string;
  subcategory?: string;
  make?: string;
  model?: string;
  year?: number;
  year_start?: number;
  year_end?: number;
  has_inventory?: boolean;
  page?: number;
  size?: number;
  sort_by?: 'name' | 'price' | 'brand' | 'category' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

// Advanced search filters
export interface PartSearchFilters {
  search_terms?: string[];
  brands?: string[];
  categories?: string[];
  subcategories?: string[];
  vehicle_makes?: string[];
  vehicle_models?: string[];
  year_range?: {
    start: number;
    end: number;
  };
  price_range?: {
    min: number;
    max: number;
  };
  specifications?: Record<string, any>;
  has_inventory?: boolean;
  in_stock_only?: boolean;
  location?: {
    state?: string;
    city?: string;
    radius_km?: number;
  };
  page?: number;
  size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// Part compatibility check
export interface PartCompatibilityCheck {
  compatible: boolean;
  confidence: number; // 0-100
  notes?: string;
  alternative_parts?: Part[];
  required_modifications?: string[];
}

// Vehicle compatibility info
export interface VehicleCompatibility {
  make: string;
  model: string;
  year_start: number;
  year_end: number;
  engine?: string;
  trim?: string;
  notes?: string;
}

// Part specifications
export interface PartSpecifications {
  part_id: string;
  technical_specs: Record<string, any>;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    weight?: number;
    unit?: string;
  };
  materials?: string[];
  certifications?: string[];
  warranty_info?: {
    duration_months: number;
    coverage: string;
    terms?: string;
  };
  installation_notes?: string;
  compatibility_notes?: string;
}

// Part comparison result
export interface PartComparison {
  parts: Part[];
  comparison_matrix: {
    [partId: string]: {
      [attribute: string]: any;
    };
  };
  recommendations?: {
    best_value: string;
    highest_quality: string;
    most_compatible: string;
  };
}

// Inventory types
export interface Inventory {
  id: string;
  dealership_id: string;
  part_id: string;
  quantity: number;
  price: number;
  condition: InventoryCondition;
  location?: string;
  notes?: string;
  is_available: boolean;
  created_at: string;
  updated_at: string;
  part: Part;
  dealership: Dealership;
}

export interface InventoryCreate {
  part_id: string;
  quantity: number;
  price: number;
  condition: InventoryCondition;
  location?: string;
  notes?: string;
}

export interface InventoryUpdate {
  quantity?: number;
  price?: number;
  condition?: InventoryCondition;
  location?: string;
  notes?: string;
  is_available?: boolean;
}

export enum InventoryCondition {
  NEW = "new",
  USED = "used",
  REFURBISHED = "refurbished",
  DAMAGED = "damaged"
}

// Subscription types
export interface Subscription {
  id: string;
  dealership_id: string;
  asaas_subscription_id: string;
  asaas_customer_id: string;
  plan_type: SubscriptionPlan;
  status: SubscriptionStatus;
  value: number;
  next_due_date?: string;
  billing_type: BillingType;
  cycle: BillingCycle;
  description?: string;
  external_reference?: string;
  discount_value?: number;
  interest_value?: number;
  created_at: string;
  updated_at: string;
}

export enum SubscriptionPlan {
  BASIC = "BASIC",
  PREMIUM = "PREMIUM",
  ENTERPRISE = "ENTERPRISE"
}

export enum BillingType {
  BOLETO = "BOLETO",
  CREDIT_CARD = "CREDIT_CARD",
  PIX = "PIX"
}

export enum BillingCycle {
  MONTHLY = "MONTHLY",
  YEARLY = "YEARLY"
}

export interface SubscriptionCreate {
  plan_type: SubscriptionPlan;
  billing_type: BillingType;
  cycle: BillingCycle;
  value?: number;
  description?: string;
  external_reference?: string;
}

export interface SubscriptionUpdate {
  plan_type?: SubscriptionPlan;
  billing_type?: BillingType;
  cycle?: BillingCycle;
  value?: number;
  description?: string;
}

export interface BillingHistory {
  id: string;
  subscription_id: string;
  asaas_payment_id: string;
  value: number;
  net_value: number;
  status: PaymentStatus;
  billing_type: BillingType;
  due_date: string;
  payment_date?: string;
  description?: string;
  invoice_url?: string;
  created_at: string;
  updated_at: string;
}

export enum PaymentStatus {
  PENDING = "PENDING",
  RECEIVED = "RECEIVED",
  CONFIRMED = "CONFIRMED",
  OVERDUE = "OVERDUE",
  REFUNDED = "REFUNDED",
  RECEIVED_IN_CASH = "RECEIVED_IN_CASH",
  REFUND_REQUESTED = "REFUND_REQUESTED",
  REFUND_IN_PROGRESS = "REFUND_IN_PROGRESS",
  CHARGEBACK_REQUESTED = "CHARGEBACK_REQUESTED",
  CHARGEBACK_DISPUTE = "CHARGEBACK_DISPUTE",
  AWAITING_CHARGEBACK_REVERSAL = "AWAITING_CHARGEBACK_REVERSAL",
  DUNNING_REQUESTED = "DUNNING_REQUESTED",
  DUNNING_RECEIVED = "DUNNING_RECEIVED",
  AWAITING_RISK_ANALYSIS = "AWAITING_RISK_ANALYSIS"
}

export interface SubscriptionPlanInfo {
  type: SubscriptionPlan;
  name: string;
  description: string;
  features: string[];
  monthly_price: number;
  yearly_price: number;
  popular?: boolean;
  recommended?: boolean;
}

export enum SubscriptionStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  PAST_DUE = "past_due",
  CANCELED = "canceled",
  UNPAID = "unpaid"
}

// File import types
export interface FileImportJob {
  id: string;
  filename: string;
  status: ImportStatus;
  total_rows: number;
  processed_rows: number;
  success_count: number;
  error_count: number;
  errors: ImportError[];
  created_at: string;
  updated_at: string;
}

export enum ImportStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed"
}

export interface ImportError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

// Health check types
export interface HealthCheck {
  status: string;
  app_name: string;
  version: string;
  database: string;
  timestamp: number;
}

// API response wrapper
export interface ApiResponse<T = any> {
  data?: T;
  error?: ErrorResponse;
  success: boolean;
}
