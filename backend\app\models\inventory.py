"""
Inventory model for AutoParts API.
"""
from sqlalchemy import Column, Integer, String, DateTime, Index, ForeignKey, DECIMAL, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from app.models.base import BaseModel
from app.core.database_types import UUID


class Inventory(BaseModel):
    """
    Inventory model representing parts stock in dealerships.
    
    Attributes:
        dealership_id: Reference to the dealership
        part_id: Reference to the part
        quantity: Available quantity in stock
        price: Selling price
        cost: Cost price (for dealership)
        location: Storage location within dealership
        condition: Part condition (new, used, refurbished)
        warranty_months: Warranty period in months
        last_updated: When inventory was last updated
        is_available: Whether the part is available for sale
        minimum_stock: Minimum stock level for alerts
        notes: Additional notes about the inventory item
    """
    __tablename__ = "inventories"

    # Foreign Keys
    dealership_id = Column(
        UUID(),
        ForeignKey("dealerships.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the dealership"
    )
    
    part_id = Column(
        UUID(),
        ForeignKey("parts.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the part"
    )

    # Stock Information
    quantity = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Available quantity in stock"
    )
    
    minimum_stock = Column(
        Integer,
        nullable=True,
        default=1,
        doc="Minimum stock level for alerts"
    )

    # Pricing
    price = Column(
        DECIMAL(10, 2),
        nullable=False,
        doc="Selling price"
    )
    
    cost = Column(
        DECIMAL(10, 2),
        nullable=True,
        doc="Cost price (for dealership internal use)"
    )

    # Location and Condition
    location = Column(
        String(100),
        nullable=True,
        doc="Storage location within dealership (e.g., A1-B2, Shelf 3)"
    )
    
    condition = Column(
        String(20),
        nullable=False,
        default="new",
        doc="Part condition: new, used, refurbished"
    )
    
    warranty_months = Column(
        Integer,
        nullable=True,
        default=12,
        doc="Warranty period in months"
    )

    # Status
    is_available = Column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        doc="Whether the part is available for sale"
    )
    
    last_updated = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        doc="When inventory was last updated"
    )

    # Additional Information
    notes = Column(
        String(500),
        nullable=True,
        doc="Additional notes about the inventory item"
    )
    
    supplier = Column(
        String(255),
        nullable=True,
        doc="Supplier information"
    )

    # Relationships
    dealership = relationship(
        "Dealership",
        back_populates="inventories"
    )
    
    part = relationship(
        "Part",
        back_populates="inventories"
    )

    def __repr__(self):
        return f"<Inventory(id={self.id}, dealership_id={self.dealership_id}, part_id={self.part_id}, quantity={self.quantity})>"

    @property
    def is_low_stock(self):
        """Check if inventory is below minimum stock level."""
        return self.quantity <= (self.minimum_stock or 0)

    @property
    def is_in_stock(self):
        """Check if part is in stock and available."""
        return self.quantity > 0 and self.is_available

    def update_stock(self, new_quantity: int, notes: str = None):
        """Update stock quantity and timestamp."""
        self.quantity = new_quantity
        self.last_updated = datetime.utcnow()
        if notes:
            self.notes = notes


# Create indexes for performance optimization
Index('idx_inventory_dealership', Inventory.dealership_id)
Index('idx_inventory_part', Inventory.part_id)
Index('idx_inventory_search', Inventory.dealership_id, Inventory.part_id)
Index('idx_inventory_available', Inventory.is_available)
Index('idx_inventory_quantity', Inventory.quantity)
Index('idx_inventory_price', Inventory.price)
Index('idx_inventory_last_updated', Inventory.last_updated)

# Composite index for common search patterns
Index('idx_inventory_dealership_available', Inventory.dealership_id, Inventory.is_available)
Index('idx_inventory_part_available', Inventory.part_id, Inventory.is_available)
