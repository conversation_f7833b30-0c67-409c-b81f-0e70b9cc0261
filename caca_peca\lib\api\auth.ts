/**
 * Authentication API Service
 * Handles all authentication-related API calls
 */

import { apiClient } from './client';
import {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  UserCreate,
  User
} from '../types/api';

export class AuthService {
  /**
   * <PERSON>gin user with email and password
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.login(credentials);
  }

  /**
   * Logout current user
   */
  async logout(): Promise<ApiResponse<void>> {
    return apiClient.logout();
  }

  /**
   * Register new user
   */
  async register(userData: UserCreate): Promise<ApiResponse<User>> {
    return apiClient.register(userData);
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiClient.getCurrentUser();
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<RefreshTokenResponse>> {
    return apiClient.request<RefreshTokenResponse>({
      method: 'POST',
      url: '/auth/refresh',
      data: { refresh_token: refreshToken }
    });
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'POST',
      url: '/auth/password-reset',
      data: { email }
    });
  }

  /**
   * Confirm password reset with token
   */
  async confirmPasswordReset(
    token: string, 
    newPassword: string
  ): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'POST',
      url: '/auth/password-reset/confirm',
      data: { token, new_password: newPassword }
    });
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(
    currentPassword: string, 
    newPassword: string
  ): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'POST',
      url: '/auth/change-password',
      data: { 
        current_password: currentPassword, 
        new_password: newPassword 
      }
    });
  }

  /**
   * Generate API key for authenticated user
   */
  async generateApiKey(): Promise<ApiResponse<{ api_key: string }>> {
    return apiClient.request<{ api_key: string }>({
      method: 'POST',
      url: '/auth/api-key'
    });
  }

  /**
   * Revoke API key for authenticated user
   */
  async revokeApiKey(): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'DELETE',
      url: '/auth/api-key'
    });
  }



  /**
   * Update user profile
   */
  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.request<User>({
      method: 'PUT',
      url: '/auth/me',
      data
    });
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return apiClient.isAuthenticated();
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return apiClient.getAccessToken();
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
