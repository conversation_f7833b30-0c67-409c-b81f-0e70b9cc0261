"use client"

import { useState } from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Dados simulados para paginação
const generateItems = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    title: `Item ${i + 1}`,
    description: `Esta é a descrição do item ${i + 1}. Conteúdo de exemplo para demonstrar a paginação.`,
  }))
}

const allItems = generateItems(50)
const ITEMS_PER_PAGE = 5

export function PaginatedContent() {
  const [currentPage, setCurrentPage] = useState(1)

  const totalPages = Math.ceil(allItems.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const currentItems = allItems.slice(startIndex, startIndex + ITEMS_PER_PAGE)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll para o topo da lista quando mudar de página
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  // Determina quais números de página mostrar
  const getPageNumbers = () => {
    const pageNumbers = []

    // Sempre mostrar a primeira página
    pageNumbers.push(1)

    // Lógica para mostrar páginas ao redor da página atual
    if (currentPage > 3) {
      pageNumbers.push("ellipsis-start")
    }

    // Páginas ao redor da atual
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i !== 1 && i !== totalPages) {
        pageNumbers.push(i)
      }
    }

    // Ellipsis antes da última página
    if (currentPage < totalPages - 2) {
      pageNumbers.push("ellipsis-end")
    }

    // Sempre mostrar a última página se houver mais de uma página
    if (totalPages > 1) {
      pageNumbers.push(totalPages)
    }

    return pageNumbers
  }

  return (
    <div className="space-y-8">
      <div className="grid gap-4">
        {currentItems.map((item) => (
          <Card key={item.id}>
            <CardHeader>
              <CardTitle>{item.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{item.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-between items-center text-sm text-muted-foreground">
        <div>
          Mostrando {startIndex + 1}-{Math.min(startIndex + ITEMS_PER_PAGE, allItems.length)} de {allItems.length} itens
        </div>
        <div>
          Página {currentPage} de {totalPages}
        </div>
      </div>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault()
                if (currentPage > 1) handlePageChange(currentPage - 1)
              }}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              aria-disabled={currentPage === 1}
            />
          </PaginationItem>

          {getPageNumbers().map((page, index) => {
            if (page === "ellipsis-start" || page === "ellipsis-end") {
              return (
                <PaginationItem key={`ellipsis-${index}`}>
                  <PaginationEllipsis />
                </PaginationItem>
              )
            }

            return (
              <PaginationItem key={`page-${page}`}>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault()
                    handlePageChange(page as number)
                  }}
                  isActive={currentPage === page}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            )
          })}

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault()
                if (currentPage < totalPages) handlePageChange(currentPage + 1)
              }}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
              aria-disabled={currentPage === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )
}

