"""
User model for authentication and authorization.
"""
from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, Index, Enum as SQLEnum
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum

from app.models.base import BaseModel
from app.core.database_types import UUID


class UserRole(str, Enum):
    """User role enumeration."""
    ADMIN = "admin"
    DEALERSHIP = "dealership"
    CUSTOMER = "customer"


class UserStatus(str, Enum):
    """User status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class User(BaseModel):
    """
    User model for authentication and authorization.
    
    Attributes:
        email: User email address (unique)
        password_hash: Hashed password
        full_name: User's full name
        role: User role (admin, dealership, customer)
        status: User status (active, inactive, suspended, pending_verification)
        dealership_id: Reference to dealership (if role is dealership)
        last_login: Last login timestamp
        login_attempts: Number of failed login attempts
        password_reset_token: Token for password reset
        password_reset_expires: Password reset token expiration
        email_verified: Whether email is verified
        email_verification_token: Token for email verification
        api_key: API key for programmatic access
        is_superuser: Whether user has superuser privileges
    """
    __tablename__ = "users"

    # Authentication
    email = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="User email address"
    )
    
    password_hash = Column(
        String(255),
        nullable=False,
        doc="Hashed password"
    )

    # Personal Information
    full_name = Column(
        String(255),
        nullable=False,
        doc="User's full name"
    )

    # Authorization
    role = Column(
        SQLEnum(UserRole),
        nullable=False,
        default=UserRole.CUSTOMER,
        index=True,
        doc="User role"
    )
    
    status = Column(
        SQLEnum(UserStatus),
        nullable=False,
        default=UserStatus.PENDING_VERIFICATION,
        index=True,
        doc="User status"
    )
    
    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether user has superuser privileges"
    )

    # Dealership Association
    dealership_id = Column(
        UUID(),
        ForeignKey("dealerships.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Reference to dealership (if role is dealership)"
    )

    # Security
    last_login = Column(
        DateTime,
        nullable=True,
        doc="Last login timestamp"
    )
    
    login_attempts = Column(
        String(10),
        default="0",
        nullable=False,
        doc="Number of failed login attempts"
    )
    
    password_reset_token = Column(
        String(255),
        nullable=True,
        doc="Token for password reset"
    )
    
    password_reset_expires = Column(
        DateTime,
        nullable=True,
        doc="Password reset token expiration"
    )

    # Email Verification
    email_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether email is verified"
    )
    
    email_verification_token = Column(
        String(255),
        nullable=True,
        doc="Token for email verification"
    )

    # API Access
    api_key = Column(
        String(255),
        nullable=True,
        unique=True,
        doc="API key for programmatic access"
    )

    # Relationships
    dealership = relationship(
        "Dealership",
        back_populates="users",
        foreign_keys=[dealership_id]
    )

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', role='{self.role}')>"

    @property
    def is_active(self):
        """Check if user is active."""
        return self.status == UserStatus.ACTIVE

    @property
    def is_admin(self):
        """Check if user is admin."""
        return self.role == UserRole.ADMIN or self.is_superuser

    @property
    def is_dealership_user(self):
        """Check if user is associated with a dealership."""
        return self.role == UserRole.DEALERSHIP and self.dealership_id is not None

    @property
    def can_access_dealership(self):
        """Check if user can access dealership data."""
        return self.is_admin or self.is_dealership_user

    def update_last_login(self):
        """Update last login timestamp."""
        self.last_login = datetime.utcnow()

    def reset_login_attempts(self):
        """Reset failed login attempts counter."""
        self.login_attempts = "0"

    def increment_login_attempts(self):
        """Increment failed login attempts counter."""
        current_attempts = int(self.login_attempts or "0")
        self.login_attempts = str(current_attempts + 1)

    def is_locked(self):
        """Check if account is locked due to too many failed attempts."""
        return int(self.login_attempts or "0") >= 5

    def activate(self):
        """Activate user account."""
        self.status = UserStatus.ACTIVE
        self.email_verified = True

    def suspend(self):
        """Suspend user account."""
        self.status = UserStatus.SUSPENDED

    def verify_email(self):
        """Mark email as verified."""
        self.email_verified = True
        if self.status == UserStatus.PENDING_VERIFICATION:
            self.status = UserStatus.ACTIVE


# Create indexes for performance optimization
Index('idx_user_email', User.email)
Index('idx_user_role', User.role)
Index('idx_user_status', User.status)
Index('idx_user_dealership', User.dealership_id)
Index('idx_user_api_key', User.api_key)
Index('idx_user_active', User.status, User.role)
