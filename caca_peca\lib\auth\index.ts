/**
 * Authentication Module Exports
 * Central export point for all authentication-related utilities
 */

// Core services
export { jwtService } from './jwt-service';
export { sessionManager, useSession, initializeSessionPersistence, cleanupSessionPersistence } from './session';

// Context and providers
export { AuthProvider, useAuth, withAuth, useRole } from './context';

// Store
export { useAuthStore, initializeAuth, startTokenRefresh, stopTokenRefresh } from '../stores/auth-store';

// Utilities
export * from './utils';

// Re-export types for convenience
export type { User, LoginRequest, UserCreate, UserRole } from '../types/api';