"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SearchBar } from "@/components/search-bar"
import { HowItWorks } from "@/components/how-it-works"
import { FeaturedProducts } from "@/components/featured-products"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { useRouter } from "next/navigation"

export default function Home() {
  const router = useRouter()

  // Função de navegação forçada que garante o scroll para o topo
  const navigateWithTopScroll = (path: string) => {
    // Primeiro, role para o topo da página atual
    window.scrollTo(0, 0)

    // Em seguida, navegue para a nova página
    router.push(path)

    // Adicione um timeout para garantir que o scroll ocorra após a navegação
    setTimeout(() => {
      window.scrollTo({
        top: 0,
        behavior: "auto", // Use 'auto' em vez de 'smooth' para garantir que seja imediato
      })
    }, 100)
  }

  return (
    <main className="flex min-h-screen flex-col">
      <Header />

      <section className="relative flex flex-col items-center justify-center bg-gradient-to-b from-blue-50 to-background px-4 py-24 dark:from-slate-900 dark:to-background">
        <div className="container max-w-5xl text-center">
          <h1 className="mb-6 text-4xl font-bold tracking-tight text-foreground md:text-6xl">
            Ache a peça certa direto do estoque das concessionárias
          </h1>
          <p className="mb-10 text-xl text-muted-foreground">
            Busque pelo código genuíno da peça e veja onde comprar
          </p>

          <div className="mx-auto max-w-3xl">
            <SearchBar />
          </div>
        </div>
      </section>

      <HowItWorks />

      <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900">
        <div className="container px-4 mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
              Para Concessionárias
            </span>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
              Transforme seu estoque em oportunidades de vendas
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Nossa plataforma conecta compradores diretamente ao seu estoque de peças, aumentando suas vendas e
              reduzindo o tempo de permanência em prateleira
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10 mb-16">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 p-8 border border-gray-100 dark:border-gray-700 hover:-translate-y-1 group">
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-3">Aumente seu faturamento</h3>
              <p className="text-gray-500 dark:text-gray-400 text-lg">
                Venda mais peças e acessórios com nossa plataforma que conecta compradores diretamente ao seu estoque.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 p-8 border border-gray-100 dark:border-gray-700 hover:-translate-y-1 group">
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-3">Reduza estoque parado</h3>
              <p className="text-gray-500 dark:text-gray-400 text-lg">
                Transforme seu inventário de baixa rotatividade em vendas reais, melhorando seu fluxo de caixa.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 p-8 border border-gray-100 dark:border-gray-700 hover:-translate-y-1 group">
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-3">Receba leads qualificados</h3>
              <p className="text-gray-500 dark:text-gray-400 text-lg">
                Clientes interessados entram em contato diretamente com você, prontos para comprar.
              </p>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 dark:from-blue-950/40 dark:via-blue-900/30 dark:to-blue-950/40 rounded-2xl p-10 md:p-12 relative overflow-hidden shadow-lg">
            <div className="absolute right-0 top-0 w-1/3 h-full opacity-10">
              <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
                <path
                  fill="currentColor"
                  d="M47.5,-57.2C59.9,-45.8,67.4,-29.2,70.3,-11.9C73.2,5.5,71.6,23.5,62.9,37.1C54.2,50.7,38.5,59.8,21.2,65.9C3.9,72,-14.9,75,-31.9,69.7C-48.9,64.3,-64.2,50.6,-72.6,33.1C-81,15.6,-82.6,-5.7,-76.3,-24.2C-70,-42.7,-55.9,-58.3,-40,-66.5C-24.1,-74.7,-6.4,-75.5,9.8,-71.8C26,-68.1,35.1,-68.7,47.5,-57.2Z"
                  transform="translate(100 100)"
                />
              </svg>
            </div>

            <div className="relative z-10 flex flex-col md:flex-row items-center justify-between gap-10">
              <div className="md:w-2/3">
                <h3 className="text-2xl md:text-3xl font-bold mb-4">
                  Junte-se a mais de 200 concessionárias parceiras
                </h3>
                <p className="text-lg mb-8">
                  Nossa plataforma já ajudou concessionárias a aumentarem suas vendas de peças em até 30% e a reduzirem
                  o estoque parado em 25%.
                </p>
                <div className="flex flex-col sm:flex-row gap-5">
                  <Button
                    size="lg"
                    className="font-medium relative overflow-hidden group transition-all duration-300 hover:shadow-lg hover:scale-105 active:scale-95 text-lg py-6"
                    onClick={() => navigateWithTopScroll("/para-concessionarias")}
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/30 to-primary/0 group-hover:animate-shimmer"></span>
                    <span className="relative">Conheça nossa plataforma</span>
                  </Button>
                  <Button
                    size="lg"
                    className="font-medium relative overflow-hidden group transition-all duration-300 text-lg py-6 border-2 border-primary bg-white hover:bg-primary/10 dark:bg-gray-800 dark:hover:bg-primary/20 text-primary dark:text-white hover:text-primary-foreground dark:hover:text-white hover:shadow-lg hover:scale-105 active:scale-95"
                    onClick={() => navigateWithTopScroll("/cadastro-concessionaria")}
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary/0 via-primary/20 to-primary/0 opacity-0 group-hover:opacity-100 group-hover:animate-shimmer"></span>
                    <span className="relative flex items-center gap-2 text-inherit">
                      Cadastre sua concessionária
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 transition-transform group-hover:translate-x-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        />
                      </svg>
                    </span>
                  </Button>
                </div>
              </div>

              <div className="md:w-1/3 flex justify-center items-center">
                <div className="relative w-56 h-56 flex items-center justify-center">
                  {/* Círculo externo com sombra suave */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-b from-gray-100 to-white dark:from-gray-800 dark:to-gray-900 shadow-[0_0_40px_rgba(0,0,0,0.1)]"></div>

                  {/* Ícones de crescimento de vendas */}
                  <div className="absolute inset-0 rounded-full flex items-center justify-center">
                    <div className="absolute -top-4 -right-4 w-14 h-14 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center shadow-md">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 text-green-600 dark:text-green-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                        />
                      </svg>
                    </div>
                    <div className="absolute -bottom-4 -left-4 w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center shadow-md">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 text-blue-600 dark:text-blue-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="absolute -top-4 -left-4 w-14 h-14 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center shadow-md">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 text-yellow-600 dark:text-yellow-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
                        />
                      </svg>
                    </div>
                  </div>

                  {/* Círculo branco principal */}
                  <div className="relative w-48 h-48 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center">
                    {/* Círculo interno com ícone */}
                    <div className="w-32 h-32 bg-primary/10 rounded-full flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-16 w-16 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <FeaturedProducts />
      <Footer />
    </main>
  )
}

