/**
 * Authentication Context
 * Provides authentication state and methods throughout the app
 * Now integrated with Zustand store for better state management
 */

'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuthStore, initializeAuth, startTokenRefresh, stopTokenRefresh } from '../stores/auth-store';
import { User, LoginRequest, UserCreate, UserRole } from '../types/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<{ success: boolean; error?: string; redirectTo?: string }>;
  logout: () => Promise<void>;
  register: (userData: UserCreate) => Promise<{ success: boolean; error?: string }>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  getRedirectPath: (role: UserRole) => string;
  hasRole: (role: UserRole) => boolean;
  isAdmin: () => boolean;
  isDealership: () => boolean;
  isCustomer: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const {
    user,
    isLoading,
    isAuthenticated,
    error,
    login,
    logout,
    register,
    refreshUser,
    clearError,
    getRedirectPath,
    hasRole,
    isAdmin,
    isDealership,
    isCustomer,
  } = useAuthStore();

  // Initialize auth state on mount
  useEffect(() => {
    const initialize = async () => {
      await initializeAuth();
      startTokenRefresh();
    };

    initialize();

    // Cleanup on unmount
    return () => {
      stopTokenRefresh();
    };
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    error,
    login,
    logout,
    register,
    refreshUser,
    clearError,
    getRedirectPath,
    hasRole,
    isAdmin,
    isDealership,
    isCustomer,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login';
      }
      return null;
    }

    return <Component {...props} />;
  };
}

// Hook for role-based access control
export function useRole() {
  const { user, hasRole, isAdmin, isDealership, isCustomer } = useAuth();
  
  return {
    isAdmin: isAdmin(),
    isDealership: isDealership(),
    isCustomer: isCustomer(),
    role: user?.role,
    hasRole,
  };
}
