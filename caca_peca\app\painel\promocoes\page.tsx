"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, Search, Percent, Edit, Trash2, CheckCircle, Filter } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON>Des<PERSON>, AlertTitle } from "@/components/ui/alert"
import { Slider } from "@/components/ui/slider"

// Dados simulados de promoções
const promotions = [
  {
    id: "1",
    name: "Promoção de Verão",
    discountType: "percentage",
    discountValue: 15,
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    status: "active",
    itemsCount: 24,
  },
  {
    id: "2",
    name: "Liquidação de Estoque",
    discountType: "percentage",
    discountValue: 20,
    startDate: "2023-11-15",
    endDate: "2023-11-30",
    status: "scheduled",
    itemsCount: 45,
  },
  {
    id: "3",
    name: "Black Friday",
    discountType: "percentage",
    discountValue: 30,
    startDate: "2023-11-24",
    endDate: "2023-11-26",
    status: "scheduled",
    itemsCount: 120,
  },
  {
    id: "4",
    name: "Promoção Relâmpago",
    discountType: "fixed",
    discountValue: 50,
    startDate: "2023-10-10",
    endDate: "2023-10-12",
    status: "expired",
    itemsCount: 15,
  },
]

// Dados simulados de produtos
const products = [
  {
    id: "1",
    reference: "7086701",
    description: "Kit de Embreagem Original",
    brand: "Fiat",
    price: 1250.9,
    isPromotion: true,
    promotionPrice: 999.9,
    promotionEndDate: "2023-12-31",
  },
  {
    id: "2",
    reference: "52089544",
    description: "Filtro de Óleo Motor 1.0",
    brand: "Volkswagen",
    price: 89.9,
    isPromotion: false,
  },
  {
    id: "3",
    reference: "AX889922",
    description: "Pastilha de Freio Dianteira",
    brand: "Frasle",
    price: 129.9,
    isPromotion: true,
    promotionPrice: 99.9,
    promotionEndDate: "2023-11-30",
  },
  {
    id: "4",
    reference: "BB445566",
    description: "Amortecedor Traseiro (Par)",
    brand: "Monroe",
    price: 459.9,
    isPromotion: false,
  },
  {
    id: "5",
    reference: "CC778899",
    description: "Vela de Ignição",
    brand: "NGK",
    price: 29.9,
    isPromotion: false,
  },
]

export default function PromotionsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedTab, setSelectedTab] = useState("mass")
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [newPromotion, setNewPromotion] = useState({
    name: "",
    discountType: "percentage",
    discountValue: 10,
    startDate: "",
    endDate: "",
  })
  const [showSuccess, setShowSuccess] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [priceRange, setPriceRange] = useState([0, 1500])
  const [filterBrand, setFilterBrand] = useState("")

  // Filtrar promoções
  const filteredPromotions = promotions.filter((promo) => promo.name.toLowerCase().includes(searchTerm.toLowerCase()))

  // Filtrar produtos com base em todos os filtros
  const filteredProducts = products.filter(
    (product) =>
      (searchTerm === "" ||
        product.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (filterBrand === "" || product.brand.toLowerCase() === filterBrand.toLowerCase()) &&
      product.price >= priceRange[0] &&
      product.price <= priceRange[1],
  )

  // Alternar seleção de item
  const toggleItemSelection = (id: string) => {
    setSelectedItems((prev) => (prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]))
  }

  // Selecionar todos os itens
  const toggleSelectAll = () => {
    if (selectedItems.length === filteredProducts.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredProducts.map((product) => product.id))
    }
  }

  // Simular criação de promoção
  const handleCreatePromotion = () => {
    // Aqui você enviaria os dados para o backend
    console.log("Nova promoção:", newPromotion)
    console.log("Itens selecionados:", selectedItems)

    // Mostrar mensagem de sucesso
    setShowSuccess(true)
    setTimeout(() => setShowSuccess(false), 3000)

    // Resetar formulário
    setNewPromotion({
      name: "",
      discountType: "percentage",
      discountValue: 10,
      startDate: "",
      endDate: "",
    })
    setSelectedItems([])
  }

  // Formatar status da promoção
  const formatStatus = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Ativa</Badge>
      case "scheduled":
        return <Badge className="bg-blue-500">Agendada</Badge>
      case "expired":
        return <Badge variant="outline">Expirada</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Resetar filtros
  const resetFilters = () => {
    setPriceRange([0, 1500])
    setFilterBrand("")
    setSearchTerm("")
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Promoções</h1>
          <p className="text-muted-foreground">Gerencie promoções para suas peças</p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Nova Promoção
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Criar Nova Promoção</DialogTitle>
              <DialogDescription>Configure os detalhes da promoção e selecione os produtos.</DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="details" className="mt-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">Detalhes</TabsTrigger>
                <TabsTrigger value="products">Produtos</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="promotion-name">Nome da Promoção</Label>
                  <Input
                    id="promotion-name"
                    placeholder="Ex: Promoção de Verão"
                    value={newPromotion.name}
                    onChange={(e) => setNewPromotion({ ...newPromotion, name: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="discount-type">Tipo de Desconto</Label>
                    <Select
                      value={newPromotion.discountType}
                      onValueChange={(value) => setNewPromotion({ ...newPromotion, discountType: value })}
                    >
                      <SelectTrigger id="discount-type">
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Porcentagem (%)</SelectItem>
                        <SelectItem value="fixed">Valor Fixo (R$)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="discount-value">Valor do Desconto</Label>
                    <div className="relative">
                      <Input
                        id="discount-value"
                        type="number"
                        min="0"
                        value={newPromotion.discountValue}
                        onChange={(e) =>
                          setNewPromotion({ ...newPromotion, discountValue: Number.parseFloat(e.target.value) })
                        }
                        className={newPromotion.discountType === "percentage" ? "pr-8" : ""}
                      />
                      {newPromotion.discountType === "percentage" && (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <Percent className="h-4 w-4 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-date">Data de Início</Label>
                    <div className="relative">
                      <Input
                        id="start-date"
                        type="date"
                        value={newPromotion.startDate}
                        onChange={(e) => setNewPromotion({ ...newPromotion, startDate: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="end-date">Data de Término</Label>
                    <div className="relative">
                      <Input
                        id="end-date"
                        type="date"
                        value={newPromotion.endDate}
                        onChange={(e) => setNewPromotion({ ...newPromotion, endDate: e.target.value })}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="products" className="space-y-4 py-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Buscar produtos..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Button variant="outline" size="icon" className="ml-2" onClick={() => setShowFilters(!showFilters)}>
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>

                {showFilters && (
                  <Card className="mb-4">
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>Faixa de Preço</Label>
                          <div className="pt-4 px-2">
                            <Slider value={priceRange} min={0} max={1500} step={10} onValueChange={setPriceRange} />
                          </div>
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>R$ {priceRange[0]}</span>
                            <span>R$ {priceRange[1]}</span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="brand-filter">Marca</Label>
                          <Select value={filterBrand} onValueChange={setFilterBrand}>
                            <SelectTrigger id="brand-filter">
                              <SelectValue placeholder="Todas as marcas" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="">Todas as marcas</SelectItem>
                              <SelectItem value="Fiat">Fiat</SelectItem>
                              <SelectItem value="Volkswagen">Volkswagen</SelectItem>
                              <SelectItem value="Frasle">Frasle</SelectItem>
                              <SelectItem value="Monroe">Monroe</SelectItem>
                              <SelectItem value="NGK">NGK</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <Button variant="outline" size="sm" onClick={resetFilters}>
                          Limpar Filtros
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">
                          <Checkbox
                            checked={selectedItems.length === filteredProducts.length && filteredProducts.length > 0}
                            onCheckedChange={toggleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Referência</TableHead>
                        <TableHead>Descrição</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead className="text-right">Preço</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.includes(product.id)}
                              onCheckedChange={() => toggleItemSelection(product.id)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{product.reference}</TableCell>
                          <TableCell>{product.description}</TableCell>
                          <TableCell>{product.brand}</TableCell>
                          <TableCell className="text-right">
                            {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                              product.price,
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="text-sm text-muted-foreground">{selectedItems.length} produtos selecionados</div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button variant="outline">Cancelar</Button>
              <Button onClick={handleCreatePromotion}>Criar Promoção</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {showSuccess && (
        <Alert className="bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Promoção criada com sucesso!</AlertTitle>
          <AlertDescription>A promoção foi criada e aplicada aos produtos selecionados.</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={selectedTab} onValueChange={setSelectedTab}>
        <TabsList>
          <TabsTrigger value="mass">Promoções em Massa</TabsTrigger>
          <TabsTrigger value="individual">Promoções Individuais</TabsTrigger>
        </TabsList>

        <TabsContent value="mass" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Promoções em Massa</CardTitle>
              <CardDescription>Gerencie promoções aplicadas a múltiplos produtos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex items-center">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Buscar promoções..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Desconto</TableHead>
                      <TableHead>Período</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Produtos</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPromotions.map((promotion) => (
                      <TableRow key={promotion.id}>
                        <TableCell className="font-medium">{promotion.name}</TableCell>
                        <TableCell>
                          {promotion.discountType === "percentage"
                            ? `${promotion.discountValue}%`
                            : new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                                promotion.discountValue,
                              )}
                        </TableCell>
                        <TableCell>
                          {new Date(promotion.startDate).toLocaleDateString("pt-BR")} a{" "}
                          {new Date(promotion.endDate).toLocaleDateString("pt-BR")}
                        </TableCell>
                        <TableCell>{formatStatus(promotion.status)}</TableCell>
                        <TableCell>{promotion.itemsCount} produtos</TableCell>
                        <TableCell>
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="icon" className="h-8 w-8">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="icon" className="h-8 w-8">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="individual" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Promoções Individuais</CardTitle>
              <CardDescription>Gerencie promoções aplicadas a produtos específicos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex items-center">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Buscar produtos..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Referência</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Preço Original</TableHead>
                      <TableHead>Preço Promocional</TableHead>
                      <TableHead>Validade</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts
                      .filter((p) => p.isPromotion)
                      .map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.reference}</TableCell>
                          <TableCell>{product.description}</TableCell>
                          <TableCell className="text-muted-foreground line-through">
                            {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                              product.price,
                            )}
                          </TableCell>
                          <TableCell className="font-bold text-red-500">
                            {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                              product.promotionPrice!,
                            )}
                          </TableCell>
                          <TableCell>Até {new Date(product.promotionEndDate!).toLocaleDateString("pt-BR")}</TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-2">
                              <Button variant="outline" size="icon" className="h-8 w-8">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="icon" className="h-8 w-8">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

