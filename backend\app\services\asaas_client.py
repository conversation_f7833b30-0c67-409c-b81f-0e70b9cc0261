"""
Asaas API client for payment and subscription management.
"""
import httpx
import hmac
import hashlib
import json
from typing import Optional, Dict, Any, List
from datetime import datetime, date
from decimal import Decimal
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class AsaasAPIError(Exception):
    """Custom exception for Asaas API errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class AsaasClient:
    """
    Client for Asaas API integration.
    
    Handles customer management, subscription creation, and webhook validation.
    """

    def __init__(self, api_key: Optional[str] = None, api_url: Optional[str] = None):
        """
        Initialize Asaas client.
        
        Args:
            api_key: Asaas API key (defaults to settings)
            api_url: Asaas API URL (defaults to settings)
        """
        self.api_key = api_key or settings.ASAAS_API_KEY
        self.api_url = api_url or settings.ASAAS_API_URL
        
        if not self.api_key:
            raise ValueError("Asaas API key is required")
        
        self.headers = {
            "access_token": self.api_key,
            "Content-Type": "application/json",
            "User-Agent": "AutoParts-API/1.0"
        }

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to Asaas API.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            
        Returns:
            Response data
            
        Raises:
            AsaasAPIError: If request fails
        """
        url = f"{self.api_url}/{endpoint.lstrip('/')}"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    json=data,
                    params=params,
                    timeout=30.0
                )
                
                response_data = response.json() if response.content else {}
                
                if response.status_code >= 400:
                    error_message = response_data.get("errors", [{}])[0].get("description", "Unknown error")
                    raise AsaasAPIError(
                        message=error_message,
                        status_code=response.status_code,
                        response_data=response_data
                    )
                
                logger.info(f"Asaas API {method} {endpoint}: {response.status_code}")
                return response_data
                
        except httpx.RequestError as e:
            logger.error(f"Asaas API request error: {e}")
            raise AsaasAPIError(f"Request failed: {str(e)}")

    async def create_customer(
        self,
        name: str,
        email: str,
        cpf_cnpj: str,
        phone: Optional[str] = None,
        address: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Create a customer in Asaas.
        
        Args:
            name: Customer name
            email: Customer email
            cpf_cnpj: CPF or CNPJ
            phone: Phone number
            address: Address information
            
        Returns:
            Customer data from Asaas
        """
        data = {
            "name": name,
            "email": email,
            "cpfCnpj": cpf_cnpj
        }
        
        if phone:
            data["phone"] = phone
            
        if address:
            data.update(address)
        
        return await self._make_request("POST", "/customers", data)

    async def get_customer(self, customer_id: str) -> Dict[str, Any]:
        """
        Get customer by ID.
        
        Args:
            customer_id: Asaas customer ID
            
        Returns:
            Customer data
        """
        return await self._make_request("GET", f"/customers/{customer_id}")

    async def update_customer(
        self,
        customer_id: str,
        name: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        address: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Update customer information.
        
        Args:
            customer_id: Asaas customer ID
            name: Updated name
            email: Updated email
            phone: Updated phone
            address: Updated address
            
        Returns:
            Updated customer data
        """
        data = {}
        
        if name:
            data["name"] = name
        if email:
            data["email"] = email
        if phone:
            data["phone"] = phone
        if address:
            data.update(address)
        
        return await self._make_request("PUT", f"/customers/{customer_id}", data)

    async def create_subscription(
        self,
        customer_id: str,
        billing_type: str,
        value: Decimal,
        next_due_date: date,
        cycle: str = "MONTHLY",
        description: Optional[str] = None,
        external_reference: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a subscription in Asaas.
        
        Args:
            customer_id: Asaas customer ID
            billing_type: Payment method (BOLETO, CREDIT_CARD, PIX)
            value: Subscription value
            next_due_date: Next payment due date
            cycle: Billing cycle (MONTHLY, YEARLY)
            description: Subscription description
            external_reference: External reference
            
        Returns:
            Subscription data from Asaas
        """
        data = {
            "customer": customer_id,
            "billingType": billing_type,
            "value": float(value),
            "nextDueDate": next_due_date.isoformat(),
            "cycle": cycle
        }
        
        if description:
            data["description"] = description
        if external_reference:
            data["externalReference"] = external_reference
        
        return await self._make_request("POST", "/subscriptions", data)

    async def get_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Get subscription by ID.
        
        Args:
            subscription_id: Asaas subscription ID
            
        Returns:
            Subscription data
        """
        return await self._make_request("GET", f"/subscriptions/{subscription_id}")

    async def update_subscription(
        self,
        subscription_id: str,
        value: Optional[Decimal] = None,
        next_due_date: Optional[date] = None,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update subscription.
        
        Args:
            subscription_id: Asaas subscription ID
            value: Updated value
            next_due_date: Updated due date
            description: Updated description
            
        Returns:
            Updated subscription data
        """
        data = {}
        
        if value is not None:
            data["value"] = float(value)
        if next_due_date:
            data["nextDueDate"] = next_due_date.isoformat()
        if description:
            data["description"] = description
        
        return await self._make_request("PUT", f"/subscriptions/{subscription_id}", data)

    async def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel subscription.
        
        Args:
            subscription_id: Asaas subscription ID
            
        Returns:
            Cancellation response
        """
        return await self._make_request("DELETE", f"/subscriptions/{subscription_id}")

    async def get_subscription_payments(
        self,
        subscription_id: str,
        limit: int = 20,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get payments for a subscription.
        
        Args:
            subscription_id: Asaas subscription ID
            limit: Number of payments to return
            offset: Offset for pagination
            
        Returns:
            Payments data
        """
        params = {
            "subscription": subscription_id,
            "limit": limit,
            "offset": offset
        }
        
        return await self._make_request("GET", "/payments", params=params)

    def validate_webhook_signature(
        self,
        payload: str,
        signature: str,
        webhook_secret: Optional[str] = None
    ) -> bool:
        """
        Validate webhook signature from Asaas.
        
        Args:
            payload: Raw webhook payload
            signature: Signature from webhook headers
            webhook_secret: Webhook secret (defaults to settings)
            
        Returns:
            True if signature is valid, False otherwise
        """
        secret = webhook_secret or settings.ASAAS_WEBHOOK_SECRET
        
        if not secret:
            logger.warning("Webhook secret not configured, skipping validation")
            return True
        
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)
