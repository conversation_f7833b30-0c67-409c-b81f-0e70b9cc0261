"""
FastAPI dependencies for authentication and authorization.
"""
from typing import <PERSON><PERSON>, Generator
from fastapi import Depends, HTTPException, status, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, APIKeyHeader
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User, UserRole
from app.services.auth import AuthService
from app.schemas.auth import CurrentUser

# Security schemes
bearer_scheme = HTTPBearer(auto_error=False)
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """
    Get authentication service instance.
    
    Args:
        db: Database session
        
    Returns:
        AuthService instance
    """
    return AuthService(db)


def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Security(bearer_scheme),
    api_key: Optional[str] = Security(api_key_header),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """
    Get current authenticated user from JWT token or API key.
    
    Args:
        credentials: HTTP Bearer credentials
        api_key: API key from header
        auth_service: Authentication service
        
    Returns:
        Current user instance
        
    Raises:
        HTTPException: If authentication fails
    """
    user = None
    
    # Try JWT token authentication first
    if credentials and credentials.credentials:
        user = auth_service.get_current_user(credentials.credentials)
    
    # Try API key authentication if JWT failed
    if not user and api_key:
        user = auth_service.get_user_by_api_key(api_key)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user.
    
    Args:
        current_user: Current user from authentication
        
    Returns:
        Active user instance
        
    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return current_user


def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Get current admin user.
    
    Args:
        current_user: Current active user
        
    Returns:
        Admin user instance
        
    Raises:
        HTTPException: If user is not admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return current_user


def get_current_dealership_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Get current dealership user.
    
    Args:
        current_user: Current active user
        
    Returns:
        Dealership user instance
        
    Raises:
        HTTPException: If user is not associated with a dealership
    """
    if not current_user.can_access_dealership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: dealership access required"
        )
    
    return current_user


def require_role(required_role: UserRole):
    """
    Create a dependency that requires a specific user role.
    
    Args:
        required_role: Required user role
        
    Returns:
        Dependency function
    """
    def role_checker(current_user: User = Depends(get_current_active_user)) -> User:
        if current_user.role != required_role and not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: {required_role.value} role required"
            )
        return current_user
    
    return role_checker


def require_dealership_access(dealership_id: str):
    """
    Create a dependency that requires access to a specific dealership.
    
    Args:
        dealership_id: Dealership UUID string
        
    Returns:
        Dependency function
    """
    def dealership_access_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        # Admin users can access any dealership
        if current_user.is_admin:
            return current_user
        
        # Dealership users can only access their own dealership
        if (current_user.role == UserRole.DEALERSHIP and 
            str(current_user.dealership_id) == dealership_id):
            return current_user
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: insufficient permissions for this dealership"
        )
    
    return dealership_access_checker


def optional_authentication(
    credentials: Optional[HTTPAuthorizationCredentials] = Security(bearer_scheme),
    api_key: Optional[str] = Security(api_key_header),
    auth_service: AuthService = Depends(get_auth_service)
) -> Optional[User]:
    """
    Optional authentication - returns user if authenticated, None otherwise.
    
    Args:
        credentials: HTTP Bearer credentials
        api_key: API key from header
        auth_service: Authentication service
        
    Returns:
        User instance if authenticated, None otherwise
    """
    user = None
    
    # Try JWT token authentication first
    if credentials and credentials.credentials:
        user = auth_service.get_current_user(credentials.credentials)
    
    # Try API key authentication if JWT failed
    if not user and api_key:
        user = auth_service.get_user_by_api_key(api_key)
    
    return user


# Role-specific dependencies
require_admin = require_role(UserRole.ADMIN)
require_dealership = require_role(UserRole.DEALERSHIP)
require_customer = require_role(UserRole.CUSTOMER)
