"""
Webhook endpoints for external service integrations.
"""
import json
import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, status, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.subscription import SubscriptionService
from app.services.asaas_client import AsaasClient
from app.schemas.subscription import (
    AsaasWebhookPayload,
    PaymentWebhookData,
    SubscriptionWebhookData,
    WebhookResponse
)
from app.models.subscription import SubscriptionStatus
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter()


def get_subscription_service(db: Session = Depends(get_db)) -> SubscriptionService:
    """Get subscription service instance."""
    return SubscriptionService(db)


@router.post("/asaas", response_model=WebhookResponse)
async def asaas_webhook(
    request: Request,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Handle Asaas webhook notifications.
    
    This endpoint receives payment and subscription status updates from <PERSON><PERSON><PERSON>
    and processes them to update local subscription data.
    
    Args:
        request: FastAPI request object
        subscription_service: Subscription service instance
        
    Returns:
        Webhook response confirmation
        
    Raises:
        HTTPException: If webhook processing fails
    """
    try:
        # Get raw payload and signature
        payload = await request.body()
        payload_str = payload.decode('utf-8')
        signature = request.headers.get('asaas-signature', '')
        
        # Validate webhook signature
        asaas_client = AsaasClient()
        if not asaas_client.validate_webhook_signature(payload_str, signature):
            logger.warning("Invalid webhook signature received")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid webhook signature"
            )
        
        # Parse webhook payload
        webhook_data = json.loads(payload_str)
        logger.info(f"Received Asaas webhook: {webhook_data.get('event')}")
        
        # Process webhook based on event type
        event = webhook_data.get('event')
        
        if event in ['PAYMENT_RECEIVED', 'PAYMENT_CONFIRMED']:
            await _process_payment_webhook(webhook_data, subscription_service)
        elif event in ['PAYMENT_OVERDUE', 'PAYMENT_DELETED']:
            await _process_payment_overdue_webhook(webhook_data, subscription_service)
        elif event in ['SUBSCRIPTION_CREATED', 'SUBSCRIPTION_UPDATED']:
            await _process_subscription_webhook(webhook_data, subscription_service)
        elif event == 'SUBSCRIPTION_CANCELLED':
            await _process_subscription_cancelled_webhook(webhook_data, subscription_service)
        else:
            logger.info(f"Unhandled webhook event: {event}")
        
        return WebhookResponse(
            success=True,
            message=f"Webhook {event} processed successfully",
            processed_at=datetime.utcnow()
        )
        
    except json.JSONDecodeError:
        logger.error("Invalid JSON in webhook payload")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON payload"
        )
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Webhook processing failed: {str(e)}"
        )


async def _process_payment_webhook(
    webhook_data: Dict[str, Any],
    subscription_service: SubscriptionService
):
    """
    Process payment received/confirmed webhook.
    
    Args:
        webhook_data: Webhook payload
        subscription_service: Subscription service instance
    """
    payment_data = webhook_data.get('payment', {})
    
    if not payment_data:
        logger.warning("No payment data in webhook")
        return
    
    subscription_id = payment_data.get('subscription')
    if not subscription_id:
        logger.info("Payment not associated with subscription")
        return
    
    # Find subscription by Asaas ID
    subscription_repo = subscription_service.subscription_repo
    subscription = subscription_repo.get_by_asaas_subscription_id(subscription_id)
    
    if not subscription:
        logger.warning(f"Subscription not found for Asaas ID: {subscription_id}")
        return
    
    # Update subscription status to active
    if subscription.status != SubscriptionStatus.ACTIVE:
        subscription_repo.update_status(subscription.id, SubscriptionStatus.ACTIVE)
        logger.info(f"Activated subscription {subscription.id} after payment")
    
    # Sync subscription data with Asaas
    await subscription_service.sync_subscription_status(subscription.id)


async def _process_payment_overdue_webhook(
    webhook_data: Dict[str, Any],
    subscription_service: SubscriptionService
):
    """
    Process payment overdue webhook.
    
    Args:
        webhook_data: Webhook payload
        subscription_service: Subscription service instance
    """
    payment_data = webhook_data.get('payment', {})
    
    if not payment_data:
        logger.warning("No payment data in webhook")
        return
    
    subscription_id = payment_data.get('subscription')
    if not subscription_id:
        logger.info("Payment not associated with subscription")
        return
    
    # Find subscription by Asaas ID
    subscription_repo = subscription_service.subscription_repo
    subscription = subscription_repo.get_by_asaas_subscription_id(subscription_id)
    
    if not subscription:
        logger.warning(f"Subscription not found for Asaas ID: {subscription_id}")
        return
    
    # Update subscription status to overdue
    if subscription.status == SubscriptionStatus.ACTIVE:
        subscription_repo.update_status(subscription.id, SubscriptionStatus.OVERDUE)
        logger.info(f"Marked subscription {subscription.id} as overdue")


async def _process_subscription_webhook(
    webhook_data: Dict[str, Any],
    subscription_service: SubscriptionService
):
    """
    Process subscription created/updated webhook.
    
    Args:
        webhook_data: Webhook payload
        subscription_service: Subscription service instance
    """
    subscription_data = webhook_data.get('subscription', {})
    
    if not subscription_data:
        logger.warning("No subscription data in webhook")
        return
    
    asaas_subscription_id = subscription_data.get('id')
    if not asaas_subscription_id:
        logger.warning("No subscription ID in webhook")
        return
    
    # Find subscription by Asaas ID
    subscription_repo = subscription_service.subscription_repo
    subscription = subscription_repo.get_by_asaas_subscription_id(asaas_subscription_id)
    
    if subscription:
        # Sync existing subscription
        await subscription_service.sync_subscription_status(subscription.id)
        logger.info(f"Synced subscription {subscription.id} from webhook")
    else:
        logger.info(f"Subscription {asaas_subscription_id} not found locally")


async def _process_subscription_cancelled_webhook(
    webhook_data: Dict[str, Any],
    subscription_service: SubscriptionService
):
    """
    Process subscription cancelled webhook.
    
    Args:
        webhook_data: Webhook payload
        subscription_service: Subscription service instance
    """
    subscription_data = webhook_data.get('subscription', {})
    
    if not subscription_data:
        logger.warning("No subscription data in webhook")
        return
    
    asaas_subscription_id = subscription_data.get('id')
    if not asaas_subscription_id:
        logger.warning("No subscription ID in webhook")
        return
    
    # Find subscription by Asaas ID
    subscription_repo = subscription_service.subscription_repo
    subscription = subscription_repo.get_by_asaas_subscription_id(asaas_subscription_id)
    
    if not subscription:
        logger.warning(f"Subscription not found for Asaas ID: {asaas_subscription_id}")
        return
    
    # Update subscription status to cancelled
    subscription_repo.update_status(subscription.id, SubscriptionStatus.CANCELLED)
    logger.info(f"Cancelled subscription {subscription.id} from webhook")


@router.get("/test")
async def test_webhook():
    """
    Test endpoint for webhook functionality.
    
    Returns:
        Test response
    """
    return {
        "message": "Webhook endpoint is working",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.post("/test-asaas")
async def test_asaas_webhook(payload: Dict[str, Any]):
    """
    Test endpoint for Asaas webhook simulation.
    
    Args:
        payload: Test webhook payload
        
    Returns:
        Test response
    """
    logger.info(f"Test webhook received: {payload}")
    
    return {
        "message": "Test webhook processed",
        "received_payload": payload,
        "timestamp": datetime.utcnow().isoformat()
    }
