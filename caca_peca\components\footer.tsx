import Link from "next/link"
import { ShoppingCart, Facebook, Instagram, Twitter, Youtube } from "lucide-react"

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <Link href="/" className="mb-4 flex items-center space-x-2">
              <ShoppingCart className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">AutoParts</span>
            </Link>
            <p className="text-sm text-muted-foreground">
              A maior plataforma de busca de peças automotivas do Brasil. Encontre a peça ideal para seu veículo com os
              melhores preços.
            </p>
            <div className="mt-4 flex space-x-4">
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Youtube className="h-5 w-5" />
                <span className="sr-only">YouTube</span>
              </a>
            </div>
            <div className="mt-4 pt-4 border-t">
              <h4 className="text-sm font-semibold mb-2">É uma concessionária?</h4>
              <Link href="/cadastro-concessionaria" className="inline-flex items-center text-primary hover:underline">
                Anuncie seu estoque e venda mais rápido!
              </Link>
            </div>
          </div>

          <div>
            <h3 className="mb-4 text-sm font-semibold uppercase">Navegação</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/" className="text-muted-foreground hover:text-primary">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/catalogo" className="text-muted-foreground hover:text-primary">
                  Catálogo
                </Link>
              </li>
              <li>
                <Link href="/#como-funciona" className="text-muted-foreground hover:text-primary">
                  Como Funciona
                </Link>
              </li>
              <li>
                <Link href="/concessionarias" className="text-muted-foreground hover:text-primary">
                  Concessionárias
                </Link>
              </li>
              <li>
                <Link href="/contato" className="text-muted-foreground hover:text-primary">
                  Contato
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-4 text-sm font-semibold uppercase">Suporte</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/faq" className="text-muted-foreground hover:text-primary">
                  Perguntas Frequentes
                </Link>
              </li>
              <li>
                <Link href="/termos" className="text-muted-foreground hover:text-primary">
                  Termos de Uso
                </Link>
              </li>
              <li>
                <Link href="/privacidade" className="text-muted-foreground hover:text-primary">
                  Política de Privacidade
                </Link>
              </li>
              <li>
                <Link href="/devolucao" className="text-muted-foreground hover:text-primary">
                  Política de Devolução
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-4 text-sm font-semibold uppercase">Contato</h3>
            <address className="not-italic">
              <p className="mb-2 text-sm text-muted-foreground">Av. Paulista, 1000 - Bela Vista</p>
              <p className="mb-2 text-sm text-muted-foreground">São Paulo - SP, 01310-100</p>
              <p className="mb-2 text-sm">
                <a href="tel:+551199999999" className="text-muted-foreground hover:text-primary">
                  +55 11 9999-9999
                </a>
              </p>
              <p className="text-sm">
                <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary">
                  <EMAIL>
                </a>
              </p>
            </address>
          </div>
        </div>

        <div className="mt-12 border-t pt-6">
          <p className="text-center text-sm text-muted-foreground">
            © {new Date().getFullYear()} AutoParts. Todos os direitos reservados.
          </p>
        </div>
      </div>
    </footer>
  )
}

