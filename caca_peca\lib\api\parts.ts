/**
 * Parts API Service
 * Handles all parts catalog related API calls
 */

import { apiClient } from './client';
import {
  ApiResponse,
  Part,
  PartCreate,
  PartSearchRequest,
  PaginatedResponse,
  PartSearchFilters,
  PartCompatibilityCheck,
  PartComparison,
  PartSpecifications,
  VehicleCompatibility
} from '../types/api';

export class PartsService {
  /**
   * Search parts with advanced filters and pagination
   */
  async searchParts(searchParams: PartSearchRequest): Promise<ApiResponse<PaginatedResponse<Part>>> {
    const params = new URLSearchParams();

    if (searchParams.query) params.append('search', searchParams.query);
    if (searchParams.part_number) params.append('code', searchParams.part_number);
    if (searchParams.brand) params.append('brand', searchParams.brand);
    if (searchParams.category) params.append('category', searchParams.category);
    if (searchParams.make) params.append('model', searchParams.make);
    if (searchParams.model) params.append('model', searchParams.model);
    if (searchParams.year) params.append('year', searchParams.year.toString());
    if (searchParams.year_start) params.append('year_start', searchParams.year_start.toString());
    if (searchParams.year_end) params.append('year_end', searchParams.year_end.toString());
    if (searchParams.subcategory) params.append('subcategory', searchParams.subcategory);
    if (searchParams.has_inventory !== undefined) params.append('has_inventory', searchParams.has_inventory.toString());
    if (searchParams.page) params.append('page', searchParams.page.toString());
    if (searchParams.size) params.append('per_page', searchParams.size.toString());
    if (searchParams.sort_by) params.append('sort_by', searchParams.sort_by);
    if (searchParams.sort_order) params.append('sort_order', searchParams.sort_order);

    return apiClient.request<PaginatedResponse<Part>>({
      method: 'GET',
      url: `/parts/search?${params.toString()}`
    });
  }

  /**
   * Advanced search with complex filters
   */
  async advancedSearch(filters: PartSearchFilters): Promise<ApiResponse<PaginatedResponse<Part>>> {
    return apiClient.request<PaginatedResponse<Part>>({
      method: 'POST',
      url: '/parts/search/advanced',
      data: filters
    });
  }

  /**
   * Get part by ID
   */
  async getPartById(partId: string): Promise<ApiResponse<Part>> {
    return apiClient.request<Part>({
      method: 'GET',
      url: `/parts/${partId}`
    });
  }

  /**
   * Get part by part number
   */
  async getPartByNumber(partNumber: string): Promise<ApiResponse<Part>> {
    return apiClient.request<Part>({
      method: 'GET',
      url: `/parts/number/${encodeURIComponent(partNumber)}`
    });
  }

  /**
   * Create new part (admin/dealership only)
   */
  async createPart(partData: PartCreate): Promise<ApiResponse<Part>> {
    return apiClient.request<Part>({
      method: 'POST',
      url: '/parts',
      data: partData
    });
  }

  /**
   * Update part (admin/dealership only)
   */
  async updatePart(partId: string, partData: Partial<PartCreate>): Promise<ApiResponse<Part>> {
    return apiClient.request<Part>({
      method: 'PUT',
      url: `/parts/${partId}`,
      data: partData
    });
  }

  /**
   * Delete part (admin only)
   */
  async deletePart(partId: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.request<{ message: string }>({
      method: 'DELETE',
      url: `/parts/${partId}`
    });
  }

  /**
   * Get all parts with pagination
   */
  async getAllParts(page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Part>>> {
    return apiClient.request<PaginatedResponse<Part>>({
      method: 'GET',
      url: `/parts?page=${page}&size=${size}`
    });
  }

  /**
   * Get parts by category
   */
  async getPartsByCategory(category: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Part>>> {
    return apiClient.request<PaginatedResponse<Part>>({
      method: 'GET',
      url: `/parts/category/${encodeURIComponent(category)}?page=${page}&size=${size}`
    });
  }

  /**
   * Get parts by brand
   */
  async getPartsByBrand(brand: string, page = 1, size = 20): Promise<ApiResponse<PaginatedResponse<Part>>> {
    return apiClient.request<PaginatedResponse<Part>>({
      method: 'GET',
      url: `/parts/brand/${encodeURIComponent(brand)}?page=${page}&size=${size}`
    });
  }

  /**
   * Get compatible parts for vehicle
   */
  async getCompatibleParts(
    make: string, 
    model: string, 
    year: number, 
    page = 1, 
    size = 20
  ): Promise<ApiResponse<PaginatedResponse<Part>>> {
    return apiClient.request<PaginatedResponse<Part>>({
      method: 'GET',
      url: `/parts/compatible?make=${encodeURIComponent(make)}&model=${encodeURIComponent(model)}&year=${year}&page=${page}&size=${size}`
    });
  }

  /**
   * Get part suggestions based on query
   */
  async getPartSuggestions(query: string, limit = 10): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: `/parts/suggestions?query=${encodeURIComponent(query)}&limit=${limit}`
    });
  }

  /**
   * Get all categories
   */
  async getCategories(): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: '/parts/categories'
    });
  }

  /**
   * Get all brands
   */
  async getBrands(): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: '/parts/brands'
    });
  }

  /**
   * Get vehicle makes
   */
  async getVehicleMakes(): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: '/parts/vehicles/makes'
    });
  }

  /**
   * Get vehicle models for a make
   */
  async getVehicleModels(make: string): Promise<ApiResponse<string[]>> {
    return apiClient.request<string[]>({
      method: 'GET',
      url: `/parts/vehicles/models?make=${encodeURIComponent(make)}`
    });
  }

  /**
   * Get vehicle years for make and model
   */
  async getVehicleYears(make: string, model: string): Promise<ApiResponse<number[]>> {
    return apiClient.request<number[]>({
      method: 'GET',
      url: `/parts/vehicles/years?make=${encodeURIComponent(make)}&model=${encodeURIComponent(model)}`
    });
  }

  /**
   * Bulk create parts (admin/dealership only)
   */
  async bulkCreateParts(parts: PartCreate[]): Promise<ApiResponse<{ created: number; errors: any[] }>> {
    return apiClient.request<{ created: number; errors: any[] }>({
      method: 'POST',
      url: '/parts/bulk',
      data: { parts }
    });
  }

  /**
   * Check part compatibility with vehicle
   */
  async checkCompatibility(
    partId: string,
    vehicle: { make: string; model: string; year: number }
  ): Promise<ApiResponse<PartCompatibilityCheck>> {
    return apiClient.request<PartCompatibilityCheck>({
      method: 'POST',
      url: `/parts/${partId}/compatibility`,
      data: vehicle
    });
  }

  /**
   * Get related parts for a specific part
   */
  async getRelatedParts(partId: string, limit = 10): Promise<ApiResponse<Part[]>> {
    return apiClient.request<Part[]>({
      method: 'GET',
      url: `/parts/${partId}/related?limit=${limit}`
    });
  }

  /**
   * Get alternative parts (cross-references)
   */
  async getAlternativeParts(partId: string): Promise<ApiResponse<Part[]>> {
    return apiClient.request<Part[]>({
      method: 'GET',
      url: `/parts/${partId}/alternatives`
    });
  }

  /**
   * Compare multiple parts
   */
  async compareParts(partIds: string[]): Promise<ApiResponse<PartComparison>> {
    return apiClient.request<PartComparison>({
      method: 'POST',
      url: '/parts/compare',
      data: { part_ids: partIds }
    });
  }

  /**
   * Get part specifications and technical details
   */
  async getPartSpecifications(partId: string): Promise<ApiResponse<PartSpecifications>> {
    return apiClient.request<PartSpecifications>({
      method: 'GET',
      url: `/parts/${partId}/specifications`
    });
  }

  /**
   * Get parts with similar specifications
   */
  async getSimilarParts(partId: string, limit = 10): Promise<ApiResponse<Part[]>> {
    return apiClient.request<Part[]>({
      method: 'GET',
      url: `/parts/${partId}/similar?limit=${limit}`
    });
  }

  /**
   * Get popular parts by category
   */
  async getPopularParts(category?: string, limit = 20): Promise<ApiResponse<Part[]>> {
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    params.append('limit', limit.toString());

    return apiClient.request<Part[]>({
      method: 'GET',
      url: `/parts/popular?${params.toString()}`
    });
  }

  /**
   * Get recently viewed parts (requires authentication)
   */
  async getRecentlyViewed(limit = 10): Promise<ApiResponse<Part[]>> {
    return apiClient.request<Part[]>({
      method: 'GET',
      url: `/parts/recent?limit=${limit}`
    });
  }

  /**
   * Track part view (for analytics and recommendations)
   */
  async trackPartView(partId: string): Promise<ApiResponse<{ success: boolean }>> {
    return apiClient.request<{ success: boolean }>({
      method: 'POST',
      url: `/parts/${partId}/view`
    });
  }

  /**
   * Get part availability across dealerships
   */
  async getPartAvailability(partId: string, location?: { state?: string; city?: string }): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams();
    if (location?.state) params.append('state', location.state);
    if (location?.city) params.append('city', location.city);

    return apiClient.request<any[]>({
      method: 'GET',
      url: `/parts/${partId}/availability?${params.toString()}`
    });
  }

  /**
   * Get part price history
   */
  async getPartPriceHistory(partId: string, days = 30): Promise<ApiResponse<any[]>> {
    return apiClient.request<any[]>({
      method: 'GET',
      url: `/parts/${partId}/price-history?days=${days}`
    });
  }

  /**
   * Search parts by OEM number
   */
  async searchByOEM(oemNumber: string): Promise<ApiResponse<Part[]>> {
    return apiClient.request<Part[]>({
      method: 'GET',
      url: `/parts/oem/${encodeURIComponent(oemNumber)}`
    });
  }

  /**
   * Get part installation guides/manuals
   */
  async getInstallationGuides(partId: string): Promise<ApiResponse<any[]>> {
    return apiClient.request<any[]>({
      method: 'GET',
      url: `/parts/${partId}/guides`
    });
  }

  /**
   * Get part reviews and ratings
   */
  async getPartReviews(partId: string, page = 1, size = 10): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiClient.request<PaginatedResponse<any>>({
      method: 'GET',
      url: `/parts/${partId}/reviews?page=${page}&size=${size}`
    });
  }

  /**
   * Add part review (requires authentication)
   */
  async addPartReview(partId: string, review: { rating: number; comment: string }): Promise<ApiResponse<any>> {
    return apiClient.request<any>({
      method: 'POST',
      url: `/parts/${partId}/reviews`,
      data: review
    });
  }
}

// Export singleton instance
export const partsService = new PartsService();
export default partsService;
