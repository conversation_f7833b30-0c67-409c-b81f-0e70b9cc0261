#!/usr/bin/env python3
"""
Database initialization script for AutoParts API.

This script creates all database tables and initial data.
"""
import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine, Base, SessionLocal
from app.models import User, Dealership, Part, Inventory, Subscription
from app.models.user import UserRole, UserStatus
from app.core.security import get_password_hash
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_tables():
    """Create all database tables."""
    try:
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully!")
        return True
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        return False


def create_indexes():
    """Create additional database indexes for performance."""
    try:
        logger.info("Creating additional indexes...")
        with engine.connect() as conn:
            # Full-text search index for parts
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_parts_search 
                ON parts USING gin(to_tsvector('portuguese', name || ' ' || COALESCE(description, '')))
            """))
            
            # Composite indexes for common queries
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_inventory_dealership_part 
                ON inventories(dealership_id, part_id)
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_parts_brand_model_year 
                ON parts(brand, model, year)
            """))
            
            conn.commit()
        
        logger.info("Additional indexes created successfully!")
        return True
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")
        return False


def create_sample_data():
    """Create sample data for testing."""
    try:
        logger.info("Creating sample data...")
        db = SessionLocal()
        
        # Check if data already exists
        if db.query(User).first():
            logger.info("Sample data already exists, skipping...")
            db.close()
            return True

        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            password_hash=get_password_hash("admin123"),
            full_name="Administrador do Sistema",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            is_superuser=True,
            email_verified=True
        )

        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # Create sample dealership
        sample_dealership = Dealership(
            name="AutoPeças Premium",
            trading_name="AutoPeças Premium LTDA",
            cnpj="12.345.678/0001-90",
            email="<EMAIL>",
            phone="(11) 3333-4444",
            whatsapp="+5511999999999",
            address="Av. Paulista, 1000",
            neighborhood="Bela Vista",
            city="São Paulo",
            state="SP",
            zip_code="01310-100",
            description="Concessionária especializada em peças originais Toyota, Honda e Nissan.",
            responsible="João Silva",
            is_active=True
        )
        
        db.add(sample_dealership)
        db.commit()
        db.refresh(sample_dealership)

        # Create dealership user
        dealership_user = User(
            email="<EMAIL>",
            password_hash=get_password_hash("dealership123"),
            full_name="João Silva",
            role=UserRole.DEALERSHIP,
            status=UserStatus.ACTIVE,
            dealership_id=sample_dealership.id,
            email_verified=True
        )

        db.add(dealership_user)
        db.commit()
        
        # Create sample parts
        sample_parts = [
            Part(
                code="46751179",
                name="Filtro de Óleo",
                description="Filtro de óleo para motor 1.6 16V",
                category="Motor",
                subcategory="Filtros",
                brand="Toyota",
                model="Corolla",
                year=2020,
                oem_code="90915-YZZD4"
            ),
            Part(
                code="15208-65F0C",
                name="Filtro de Combustível",
                description="Filtro de combustível original",
                category="Motor",
                subcategory="Filtros",
                brand="Nissan",
                model="Sentra",
                year=2019,
                oem_code="15208-65F0C"
            )
        ]
        
        for part in sample_parts:
            db.add(part)
        
        db.commit()
        
        # Create sample inventory
        for part in sample_parts:
            inventory = Inventory(
                dealership_id=sample_dealership.id,
                part_id=part.id,
                quantity=10,
                price=45.90,
                cost=30.00,
                location="A1-B2",
                condition="new",
                warranty_months=12,
                is_available=True
            )
            db.add(inventory)
        
        db.commit()
        db.close()
        
        logger.info("Sample data created successfully!")
        return True
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        return False


def main():
    """Main function to initialize the database."""
    logger.info("Starting database initialization...")
    
    # Create tables
    if not create_tables():
        sys.exit(1)
    
    # Create indexes
    if not create_indexes():
        logger.warning("Failed to create some indexes, but continuing...")
    
    # Create sample data
    if not create_sample_data():
        logger.warning("Failed to create sample data, but continuing...")
    
    logger.info("Database initialization completed!")


if __name__ == "__main__":
    main()
