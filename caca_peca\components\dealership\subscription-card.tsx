"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Calendar, 
  CreditCard, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Settings,
  TrendingUp,
  Package,
  Users,
  Database,
  Zap
} from "lucide-react"
import { 
  Subscription, 
  SubscriptionStatus, 
  SubscriptionPlan, 
  BillingType 
} from "@/lib/types/api"
import { 
  useSubscriptionUsage, 
  useCancelSubscription, 
  useReactivateSubscription,
  useSyncSubscription 
} from "@/lib/hooks/useSubscription"
import { formatCurrency, formatDate } from "@/lib/utils"

interface SubscriptionCardProps {
  subscription: Subscription
  onManage?: () => void
  onUpgrade?: () => void
}

export function SubscriptionCard({ subscription, onManage, onUpgrade }: SubscriptionCardProps) {
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)

  const { data: usageResponse } = useSubscriptionUsage()
  const cancelMutation = useCancelSubscription()
  const reactivateMutation = useReactivateSubscription()
  const syncMutation = useSyncSubscription()

  const usage = usageResponse?.data?.success ? usageResponse.data.data : null

  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return "bg-green-100 text-green-800"
      case SubscriptionStatus.PENDING:
        return "bg-yellow-100 text-yellow-800"
      case SubscriptionStatus.CANCELLED:
        return "bg-red-100 text-red-800"
      case SubscriptionStatus.INACTIVE:
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return <CheckCircle className="h-4 w-4" />
      case SubscriptionStatus.PENDING:
        return <AlertTriangle className="h-4 w-4" />
      case SubscriptionStatus.CANCELLED:
      case SubscriptionStatus.INACTIVE:
        return <XCircle className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getPlanName = (planType: SubscriptionPlan) => {
    switch (planType) {
      case SubscriptionPlan.BASIC:
        return "Básico"
      case SubscriptionPlan.PREMIUM:
        return "Premium"
      case SubscriptionPlan.ENTERPRISE:
        return "Enterprise"
      default:
        return planType
    }
  }

  const getBillingTypeName = (billingType: BillingType) => {
    switch (billingType) {
      case BillingType.CREDIT_CARD:
        return "Cartão de Crédito"
      case BillingType.BOLETO:
        return "Boleto"
      case BillingType.PIX:
        return "PIX"
      default:
        return billingType
    }
  }

  const handleCancel = async () => {
    if (showCancelConfirm) {
      await cancelMutation.mutateAsync(subscription.id)
      setShowCancelConfirm(false)
    } else {
      setShowCancelConfirm(true)
    }
  }

  const handleReactivate = async () => {
    await reactivateMutation.mutateAsync(subscription.id)
  }

  const handleSync = async () => {
    await syncMutation.mutateAsync(subscription.id)
  }

  const isActive = subscription.status === SubscriptionStatus.ACTIVE
  const isCancelled = subscription.status === SubscriptionStatus.CANCELLED || 
                     subscription.status === SubscriptionStatus.INACTIVE

  return (
    <div className="space-y-6">
      {/* Main Subscription Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Assinatura Atual
            </CardTitle>
            <Badge className={getStatusColor(subscription.status)}>
              {getStatusIcon(subscription.status)}
              <span className="ml-1">{subscription.status}</span>
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Plan Details */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-semibold text-lg">Plano {getPlanName(subscription.plan_type)}</h4>
              <p className="text-2xl font-bold text-primary">
                {formatCurrency(subscription.value)}
                <span className="text-sm font-normal text-muted-foreground">
                  /{subscription.cycle === 'YEARLY' ? 'ano' : 'mês'}
                </span>
              </p>
              <p className="text-sm text-muted-foreground">
                Método: {getBillingTypeName(subscription.billing_type)}
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4" />
                <span>Próximo vencimento:</span>
              </div>
              <p className="font-semibold">
                {subscription.next_due_date 
                  ? formatDate(subscription.next_due_date)
                  : 'Não definido'
                }
              </p>
              <p className="text-sm text-muted-foreground">
                Ativa desde {formatDate(subscription.created_at)}
              </p>
            </div>
          </div>

          <Separator />

          {/* Usage Information */}
          {usage && (
            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Uso do Plano
              </h4>
              
              <div className="grid gap-4 md:grid-cols-3">
                {/* Inventory Items */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-1">
                      <Package className="h-3 w-3" />
                      Itens no Estoque
                    </span>
                    <span>{usage.inventory_items_count}/{usage.inventory_items_limit}</span>
                  </div>
                  <Progress 
                    value={(usage.inventory_items_count / usage.inventory_items_limit) * 100} 
                    className="h-2"
                  />
                </div>

                {/* API Calls */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-1">
                      <Zap className="h-3 w-3" />
                      Chamadas API
                    </span>
                    <span>{usage.api_calls_count}/{usage.api_calls_limit}</span>
                  </div>
                  <Progress 
                    value={(usage.api_calls_count / usage.api_calls_limit) * 100} 
                    className="h-2"
                  />
                </div>

                {/* Storage */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-1">
                      <Database className="h-3 w-3" />
                      Armazenamento
                    </span>
                    <span>{usage.storage_used_mb}MB/{usage.storage_limit_mb}MB</span>
                  </div>
                  <Progress 
                    value={(usage.storage_used_mb / usage.storage_limit_mb) * 100} 
                    className="h-2"
                  />
                </div>
              </div>
            </div>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            {isActive && (
              <>
                <Button variant="outline" onClick={onManage}>
                  <Settings className="h-4 w-4 mr-2" />
                  Gerenciar
                </Button>
                
                <Button variant="outline" onClick={onUpgrade}>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Fazer Upgrade
                </Button>

                <Button 
                  variant="outline" 
                  onClick={handleSync}
                  disabled={syncMutation.isPending}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${syncMutation.isPending ? 'animate-spin' : ''}`} />
                  Sincronizar
                </Button>

                <Button 
                  variant="destructive" 
                  onClick={handleCancel}
                  disabled={cancelMutation.isPending}
                >
                  {showCancelConfirm ? 'Confirmar Cancelamento' : 'Cancelar Assinatura'}
                </Button>
              </>
            )}

            {isCancelled && (
              <Button 
                onClick={handleReactivate}
                disabled={reactivateMutation.isPending}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Reativar Assinatura
              </Button>
            )}
          </div>

          {showCancelConfirm && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-semibold text-red-800">Confirmar Cancelamento</h4>
                  <p className="text-sm text-red-700">
                    Tem certeza que deseja cancelar sua assinatura? Você perderá acesso a todas as 
                    funcionalidades premium no final do período atual.
                  </p>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => setShowCancelConfirm(false)}
                    >
                      Manter Assinatura
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      onClick={handleCancel}
                      disabled={cancelMutation.isPending}
                    >
                      Sim, Cancelar
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Subscription Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Benefícios do seu Plano</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            {subscription.plan_type === SubscriptionPlan.BASIC && (
              <>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Até 1.000 peças no catálogo</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Busca básica</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Suporte por email</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">1 usuário</span>
                </div>
              </>
            )}

            {subscription.plan_type === SubscriptionPlan.PREMIUM && (
              <>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Até 10.000 peças no catálogo</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Busca avançada com filtros</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Comparação de peças</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Verificador de compatibilidade</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Até 5 usuários</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">API de integração</span>
                </div>
              </>
            )}

            {subscription.plan_type === SubscriptionPlan.ENTERPRISE && (
              <>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Peças ilimitadas</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Todas as funcionalidades Premium</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Integração personalizada</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Suporte 24/7</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Usuários ilimitados</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">SLA garantido</span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
