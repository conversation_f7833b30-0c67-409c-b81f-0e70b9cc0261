"use client"

import { useState, useEffect } from "react"
import { Progress } from "@/components/ui/progress"
import { CheckCircle2, XCircle } from "lucide-react"

interface PasswordStrengthProps {
  password: string
  onStrengthChange?: (strength: number) => void
}

export function PasswordStrength({ password, onStrengthChange }: PasswordStrengthProps) {
  const [strength, setStrength] = useState(0)
  const [checks, setChecks] = useState({
    minLength: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false,
    hasSpecialChar: false,
  })

  useEffect(() => {
    const newChecks = {
      minLength: password.length >= 8,
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[^A-Za-z0-9]/.test(password),
    }

    setChecks(newChecks)

    // Calculate strength (0-100)
    const passedChecks = Object.values(newChecks).filter(Boolean).length
    const newStrength = Math.min(100, (passedChecks / 5) * 100)

    setStrength(newStrength)

    if (onStrengthChange) {
      onStrengthChange(newStrength)
    }
  }, [password, onStrengthChange])

  const getStrengthLabel = () => {
    if (strength === 0) return "Vazia"
    if (strength < 40) return "Fraca"
    if (strength < 80) return "Média"
    return "Forte"
  }

  const getStrengthColor = () => {
    if (strength === 0) return "bg-gray-200 dark:bg-gray-700"
    if (strength < 40) return "bg-red-500"
    if (strength < 80) return "bg-yellow-500"
    return "bg-green-500"
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-xs text-muted-foreground">Força da senha: {getStrengthLabel()}</span>
        <span
          className="text-xs font-medium"
          style={{ color: strength >= 80 ? "green" : strength >= 40 ? "orange" : "red" }}
        >
          {strength > 0 ? `${Math.round(strength)}%` : ""}
        </span>
      </div>

      <Progress value={strength} className={`h-1.5 ${getStrengthColor()}`} />

      {password.length > 0 && (
        <div className="mt-3 grid grid-cols-1 gap-1 sm:grid-cols-2">
          <div className="flex items-center gap-1 text-xs">
            {checks.minLength ? (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            ) : (
              <XCircle className="h-3.5 w-3.5 text-red-500" />
            )}
            <span>Mínimo de 8 caracteres</span>
          </div>

          <div className="flex items-center gap-1 text-xs">
            {checks.hasUpperCase ? (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            ) : (
              <XCircle className="h-3.5 w-3.5 text-red-500" />
            )}
            <span>Letra maiúscula</span>
          </div>

          <div className="flex items-center gap-1 text-xs">
            {checks.hasLowerCase ? (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            ) : (
              <XCircle className="h-3.5 w-3.5 text-red-500" />
            )}
            <span>Letra minúscula</span>
          </div>

          <div className="flex items-center gap-1 text-xs">
            {checks.hasNumber ? (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            ) : (
              <XCircle className="h-3.5 w-3.5 text-red-500" />
            )}
            <span>Número</span>
          </div>

          <div className="flex items-center gap-1 text-xs">
            {checks.hasSpecialChar ? (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            ) : (
              <XCircle className="h-3.5 w-3.5 text-red-500" />
            )}
            <span>Caractere especial</span>
          </div>
        </div>
      )}
    </div>
  )
}

