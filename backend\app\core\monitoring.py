"""
Monitoring and health check system for AutoParts API.
"""
import time
import psutil
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.core.database import SessionLocal, engine
from app.core.cache import cache_manager
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("app.monitoring")


@dataclass
class HealthStatus:
    """Health status data structure."""
    service: str
    status: str  # healthy, degraded, unhealthy
    response_time_ms: float
    details: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class SystemMetrics:
    """System metrics data structure."""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    active_connections: int
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class HealthChecker:
    """
    Comprehensive health checker for all system components.
    """
    
    def __init__(self):
        self.logger = get_logger("app.monitoring.health")
    
    async def check_all(self) -> Dict[str, HealthStatus]:
        """
        Check health of all system components.
        
        Returns:
            Dictionary of health statuses by component
        """
        health_checks = {
            "database": self.check_database,
            "cache": self.check_cache,
            "disk_space": self.check_disk_space,
            "memory": self.check_memory,
            "external_services": self.check_external_services
        }
        
        results = {}
        
        for component, check_func in health_checks.items():
            try:
                start_time = time.time()
                status = await check_func()
                response_time = (time.time() - start_time) * 1000
                
                results[component] = HealthStatus(
                    service=component,
                    status=status["status"],
                    response_time_ms=round(response_time, 2),
                    details=status.get("details", {}),
                    timestamp=datetime.utcnow()
                )
                
            except Exception as e:
                self.logger.error(f"Health check failed for {component}: {e}")
                results[component] = HealthStatus(
                    service=component,
                    status="unhealthy",
                    response_time_ms=0,
                    details={"error": str(e)},
                    timestamp=datetime.utcnow()
                )
        
        return results
    
    async def check_database(self) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        try:
            db: Session = SessionLocal()
            
            # Test basic connectivity
            start_time = time.time()
            result = db.execute(text("SELECT 1")).scalar()
            query_time = (time.time() - start_time) * 1000
            
            # Get connection pool status
            pool = engine.pool
            pool_status = {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
            
            # Test table access
            tables_accessible = True
            try:
                db.execute(text("SELECT COUNT(*) FROM users LIMIT 1")).scalar()
                db.execute(text("SELECT COUNT(*) FROM parts LIMIT 1")).scalar()
                db.execute(text("SELECT COUNT(*) FROM dealerships LIMIT 1")).scalar()
            except Exception as e:
                tables_accessible = False
                self.logger.warning(f"Table access test failed: {e}")
            
            db.close()
            
            # Determine status
            if result == 1 and tables_accessible and query_time < 1000:
                status = "healthy"
            elif result == 1 and query_time < 5000:
                status = "degraded"
            else:
                status = "unhealthy"
            
            return {
                "status": status,
                "details": {
                    "query_response_time_ms": round(query_time, 2),
                    "connection_pool": pool_status,
                    "tables_accessible": tables_accessible,
                    "database_url": settings.DATABASE_URL.split("@")[1] if "@" in settings.DATABASE_URL else "hidden"
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": {"error": str(e)}
            }
    
    async def check_cache(self) -> Dict[str, Any]:
        """Check Redis cache connectivity and performance."""
        try:
            if not cache_manager.is_available():
                return {
                    "status": "unhealthy",
                    "details": {"error": "Cache manager not available"}
                }
            
            # Test basic operations
            start_time = time.time()
            test_key = "health_check_test"
            test_value = "test_value"
            
            # Test write
            cache_manager.set(test_key, test_value, ttl=60)
            
            # Test read
            retrieved_value = cache_manager.get(test_key)
            
            # Test delete
            cache_manager.delete(test_key)
            
            operation_time = (time.time() - start_time) * 1000
            
            # Get cache info
            cache_info = cache_manager.get_cache_info()
            
            # Determine status
            if retrieved_value == test_value and operation_time < 100:
                status = "healthy"
            elif retrieved_value == test_value and operation_time < 500:
                status = "degraded"
            else:
                status = "unhealthy"
            
            return {
                "status": status,
                "details": {
                    "operation_time_ms": round(operation_time, 2),
                    "cache_info": cache_info,
                    "test_successful": retrieved_value == test_value
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": {"error": str(e)}
            }
    
    async def check_disk_space(self) -> Dict[str, Any]:
        """Check disk space availability."""
        try:
            disk_usage = psutil.disk_usage('/')
            
            total_gb = disk_usage.total / (1024**3)
            used_gb = disk_usage.used / (1024**3)
            free_gb = disk_usage.free / (1024**3)
            percent_used = (used_gb / total_gb) * 100
            
            # Determine status based on disk usage
            if percent_used < 80:
                status = "healthy"
            elif percent_used < 90:
                status = "degraded"
            else:
                status = "unhealthy"
            
            return {
                "status": status,
                "details": {
                    "total_gb": round(total_gb, 2),
                    "used_gb": round(used_gb, 2),
                    "free_gb": round(free_gb, 2),
                    "percent_used": round(percent_used, 2)
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": {"error": str(e)}
            }
    
    async def check_memory(self) -> Dict[str, Any]:
        """Check memory usage."""
        try:
            memory = psutil.virtual_memory()
            
            total_mb = memory.total / (1024**2)
            used_mb = memory.used / (1024**2)
            available_mb = memory.available / (1024**2)
            percent_used = memory.percent
            
            # Determine status based on memory usage
            if percent_used < 80:
                status = "healthy"
            elif percent_used < 90:
                status = "degraded"
            else:
                status = "unhealthy"
            
            return {
                "status": status,
                "details": {
                    "total_mb": round(total_mb, 2),
                    "used_mb": round(used_mb, 2),
                    "available_mb": round(available_mb, 2),
                    "percent_used": round(percent_used, 2)
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": {"error": str(e)}
            }
    
    async def check_external_services(self) -> Dict[str, Any]:
        """Check external service connectivity."""
        try:
            # For now, just check if Asaas configuration is present
            asaas_configured = bool(settings.ASAAS_API_KEY)
            
            # In a real implementation, you would test actual connectivity
            # to external services like Asaas API
            
            return {
                "status": "healthy" if asaas_configured else "degraded",
                "details": {
                    "asaas_configured": asaas_configured,
                    "asaas_url": settings.ASAAS_API_URL
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": {"error": str(e)}
            }


class MetricsCollector:
    """
    System metrics collector for performance monitoring.
    """
    
    def __init__(self):
        self.logger = get_logger("app.monitoring.metrics")
        self.metrics_history: List[SystemMetrics] = []
        self.max_history = 1000  # Keep last 1000 metrics
    
    def collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024**2)
            memory_total_mb = memory.total / (1024**2)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            # Network connections (approximate active connections)
            connections = len(psutil.net_connections())
            
            metrics = SystemMetrics(
                cpu_percent=round(cpu_percent, 2),
                memory_percent=round(memory_percent, 2),
                memory_used_mb=round(memory_used_mb, 2),
                memory_total_mb=round(memory_total_mb, 2),
                disk_percent=round(disk_percent, 2),
                disk_used_gb=round(disk_used_gb, 2),
                disk_total_gb=round(disk_total_gb, 2),
                active_connections=connections,
                timestamp=datetime.utcnow()
            )
            
            # Add to history
            self.metrics_history.append(metrics)
            
            # Trim history if needed
            if len(self.metrics_history) > self.max_history:
                self.metrics_history = self.metrics_history[-self.max_history:]
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
            raise e
    
    def get_metrics_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """Get metrics summary for the last N minutes."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        recent_metrics = [
            m for m in self.metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"error": "No metrics available for the specified time period"}
        
        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_disk = sum(m.disk_percent for m in recent_metrics) / len(recent_metrics)
        avg_connections = sum(m.active_connections for m in recent_metrics) / len(recent_metrics)
        
        # Find peaks
        max_cpu = max(m.cpu_percent for m in recent_metrics)
        max_memory = max(m.memory_percent for m in recent_metrics)
        max_connections = max(m.active_connections for m in recent_metrics)
        
        return {
            "time_period_minutes": minutes,
            "sample_count": len(recent_metrics),
            "averages": {
                "cpu_percent": round(avg_cpu, 2),
                "memory_percent": round(avg_memory, 2),
                "disk_percent": round(avg_disk, 2),
                "active_connections": round(avg_connections, 2)
            },
            "peaks": {
                "max_cpu_percent": round(max_cpu, 2),
                "max_memory_percent": round(max_memory, 2),
                "max_active_connections": max_connections
            },
            "current": recent_metrics[-1].to_dict() if recent_metrics else None
        }


# Global instances
health_checker = HealthChecker()
metrics_collector = MetricsCollector()


class AlertManager:
    """
    Alert manager for monitoring thresholds and notifications.
    """
    
    def __init__(self):
        self.logger = get_logger("app.monitoring.alerts")
        self.alert_thresholds = {
            "cpu_percent": 85.0,
            "memory_percent": 85.0,
            "disk_percent": 90.0,
            "response_time_ms": 2000.0,
            "error_rate_percent": 5.0
        }
        self.alert_cooldown = 300  # 5 minutes cooldown between same alerts
        self.last_alerts = {}
    
    def check_thresholds(self, metrics: SystemMetrics) -> List[Dict[str, Any]]:
        """Check if any metrics exceed thresholds."""
        alerts = []
        current_time = time.time()
        
        # Check CPU threshold
        if metrics.cpu_percent > self.alert_thresholds["cpu_percent"]:
            alert_key = "high_cpu"
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    "type": "high_cpu",
                    "severity": "warning",
                    "message": f"High CPU usage: {metrics.cpu_percent}%",
                    "value": metrics.cpu_percent,
                    "threshold": self.alert_thresholds["cpu_percent"],
                    "timestamp": metrics.timestamp.isoformat()
                })
                self.last_alerts[alert_key] = current_time
        
        # Check memory threshold
        if metrics.memory_percent > self.alert_thresholds["memory_percent"]:
            alert_key = "high_memory"
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    "type": "high_memory",
                    "severity": "warning",
                    "message": f"High memory usage: {metrics.memory_percent}%",
                    "value": metrics.memory_percent,
                    "threshold": self.alert_thresholds["memory_percent"],
                    "timestamp": metrics.timestamp.isoformat()
                })
                self.last_alerts[alert_key] = current_time
        
        # Check disk threshold
        if metrics.disk_percent > self.alert_thresholds["disk_percent"]:
            alert_key = "high_disk"
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    "type": "high_disk",
                    "severity": "critical",
                    "message": f"High disk usage: {metrics.disk_percent}%",
                    "value": metrics.disk_percent,
                    "threshold": self.alert_thresholds["disk_percent"],
                    "timestamp": metrics.timestamp.isoformat()
                })
                self.last_alerts[alert_key] = current_time
        
        # Log alerts
        for alert in alerts:
            alert_copy = alert.copy()
            message = alert_copy.pop('message')
            self.logger.warning(f"ALERT: {message}", **alert_copy)
        
        return alerts
    
    def _should_send_alert(self, alert_key: str, current_time: float) -> bool:
        """Check if alert should be sent based on cooldown."""
        last_alert_time = self.last_alerts.get(alert_key, 0)
        return current_time - last_alert_time > self.alert_cooldown


# Global alert manager
alert_manager = AlertManager()
