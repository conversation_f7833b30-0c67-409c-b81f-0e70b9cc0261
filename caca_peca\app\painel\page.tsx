"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowUpRight, Package, Search, Phone, PhoneIcon as WhatsappIcon, DollarSign, Calendar } from "lucide-react"
import { getEventCountByType } from "@/lib/analytics"
import { useRouter } from "next/navigation"

// Importar componentes de gráfico do recharts
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

export default function DealerDashboard() {
  const [phoneClicks, setPhoneClicks] = useState(0)
  const [whatsappClicks, setWhatsappClicks] = useState(0)
  const [searchesToday, setSearchesToday] = useState(0)
  const [searchesLastWeek, setSearchesLastWeek] = useState(0)
  const [totalStock, setTotalStock] = useState(0)
  const [stockValue, setStockValue] = useState(0)

  // Dados de gráfico atualizados para os últimos 6 meses
  const [chartData] = useState([
    { name: "Jan", buscas: 120 },
    { name: "Fev", buscas: 185 },
    { name: "Mar", buscas: 150 },
    { name: "Abr", buscas: 240 },
    { name: "Mai", buscas: 280 },
    { name: "Jun", buscas: 320 },
  ])

  // Resto do componente permanece o mesmo...
  const [topSearchedItems] = useState([
    { name: "Kit de Embreagem", code: "KEC-1001", searches: 145 },
    { name: "Filtro de Óleo", code: "FO-2022", searches: 132 },
    { name: "Pastilha de Freio", code: "PF-3033", searches: 98 },
    { name: "Amortecedor Dianteiro", code: "AD-4044", searches: 87 },
    { name: "Vela de Ignição", code: "VI-5055", searches: 76 },
  ])

  const router = useRouter()

  const handleLogout = () => {
    router.push("/auth")
  }

  useEffect(() => {
    const updateData = () => {
      setPhoneClicks(getEventCountByType("phone_click"))
      setWhatsappClicks(getEventCountByType("whatsapp_click"))
      setSearchesToday(Math.floor(Math.random() * 50) + 30)
      setSearchesLastWeek(Math.floor(Math.random() * 300) + 150)
      setTotalStock(1248)
      setStockValue(1248 * 150)
    }

    updateData()
    const interval = setInterval(updateData, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Bem-vindo ao seu painel de controle, Concessionária ABC</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleLogout} variant="outline">
            Sair
          </Button>
        </div>
      </div>

      {/* Primeira linha de cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Itens no Estoque</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStock.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+12 nas últimas 24h</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor em Estoque</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {stockValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Valor médio: R$ 150,00/item</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Buscas Hoje</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{searchesToday}</div>
            <p className="text-xs text-muted-foreground">+{Math.floor(searchesToday * 0.15)} em relação a ontem</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Buscas nos Últimos 7 Dias</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{searchesLastWeek}</div>
            <p className="text-xs text-muted-foreground">Média: {Math.floor(searchesLastWeek / 7)}/dia</p>
          </CardContent>
        </Card>
      </div>

      {/* Segunda linha de cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Buscas por Mês</CardTitle>
            <CardDescription>Evolução das buscas nos últimos 6 meses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis
                    stroke="#888888"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => `${value}`}
                  />
                  <Tooltip
                    cursor={{ fill: "rgba(0, 0, 0, 0.05)" }}
                    contentStyle={{
                      backgroundColor: "white",
                      borderRadius: "8px",
                      border: "1px solid #e2e8f0",
                      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    }}
                    formatter={(value) => [`${value} buscas`, "Quantidade"]}
                    labelFormatter={(label) => `Mês: ${label}`}
                  />
                  <Bar dataKey="buscas" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} name="Buscas" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cliques no Telefone</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{phoneClicks}</div>
            <p className="text-xs text-muted-foreground">Cliques no botão de telefone</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contatos WhatsApp</CardTitle>
            <WhatsappIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{whatsappClicks}</div>
            <p className="text-xs text-muted-foreground">Cliques no botão de WhatsApp</p>
          </CardContent>
        </Card>
      </div>

      {/* Itens mais buscados */}
      <Card>
        <CardHeader>
          <CardTitle>Itens Mais Buscados</CardTitle>
          <CardDescription>Os produtos da sua marca mais procurados</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topSearchedItems.map((item, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="rounded-full bg-primary/10 p-2">
                    <Search className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{item.name}</p>
                    <p className="text-xs text-muted-foreground">Código: {item.code}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">{item.searches} buscas</p>
                    <p className="text-xs text-muted-foreground">Últimos 30 dias</p>
                  </div>
                  <Button variant="ghost" size="icon">
                    <ArrowUpRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

