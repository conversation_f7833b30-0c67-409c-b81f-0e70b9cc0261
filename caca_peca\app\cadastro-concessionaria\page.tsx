"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { AlertCircle, CheckCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PasswordStrength } from "@/components/password-strength"

// Common automotive brands
const AUTO_BRANDS = [
  { value: "toyota", label: "Toyota" },
  { value: "volkswagen", label: "Volkswagen" },
  { value: "ford", label: "Ford" },
  { value: "chevrolet", label: "Chevrolet" },
  { value: "honda", label: "Honda" },
  { value: "hyundai", label: "Hyundai" },
  { value: "nissan", label: "Nissan" },
  { value: "fiat", label: "Fiat" },
  { value: "bmw", label: "BMW" },
  { value: "mercedes", label: "Mercedes-Benz" },
  { value: "audi", label: "Audi" },
  { value: "kia", label: "Kia" },
  { value: "renault", label: "Renault" },
  { value: "peugeot", label: "Peugeot" },
  { value: "citroen", label: "Citroën" },
  { value: "jeep", label: "Jeep" },
]

// Brazilian states
const BRAZILIAN_STATES = [
  { value: "AC", label: "Acre" },
  { value: "AL", label: "Alagoas" },
  { value: "AP", label: "Amapá" },
  { value: "AM", label: "Amazonas" },
  { value: "BA", label: "Bahia" },
  { value: "CE", label: "Ceará" },
  { value: "DF", label: "Distrito Federal" },
  { value: "ES", label: "Espírito Santo" },
  { value: "GO", label: "Goiás" },
  { value: "MA", label: "Maranhão" },
  { value: "MT", label: "Mato Grosso" },
  { value: "MS", label: "Mato Grosso do Sul" },
  { value: "MG", label: "Minas Gerais" },
  { value: "PA", label: "Pará" },
  { value: "PB", label: "Paraíba" },
  { value: "PR", label: "Paraná" },
  { value: "PE", label: "Pernambuco" },
  { value: "PI", label: "Piauí" },
  { value: "RJ", label: "Rio de Janeiro" },
  { value: "RN", label: "Rio Grande do Norte" },
  { value: "RS", label: "Rio Grande do Sul" },
  { value: "RO", label: "Rondônia" },
  { value: "RR", label: "Roraima" },
  { value: "SC", label: "Santa Catarina" },
  { value: "SP", label: "São Paulo" },
  { value: "SE", label: "Sergipe" },
  { value: "TO", label: "Tocantins" },
]

// Subscription plans
const SUBSCRIPTION_PLANS = [
  {
    id: "basic",
    name: "Básico",
    price: "R$ 199,90/mês",
    features: ["Até 500 peças no catálogo", "Suporte por email", "Relatórios mensais"],
  },
  {
    id: "standard",
    name: "Padrão",
    price: "R$ 399,90/mês",
    features: ["Até 2.000 peças no catálogo", "Suporte prioritário", "Relatórios semanais", "Destaque nas buscas"],
  },
  {
    id: "premium",
    name: "Premium",
    price: "R$ 699,90/mês",
    features: [
      "Peças ilimitadas no catálogo",
      "Suporte 24/7",
      "Relatórios em tempo real",
      "Destaque máximo nas buscas",
      "API para integração com seu sistema",
    ],
  },
]

export default function DealerRegistrationPage() {
  const [formState, setFormState] = useState<"idle" | "submitting" | "success" | "error">("idle")
  const [selectedPlan, setSelectedPlan] = useState<string>("standard")
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    companyName: "",
    tradingName: "",
    cnpj: "",
    ie: "",
    responsible: "",
    email: "",
    phone: "",
    whatsapp: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    zipCode: "",
    password: "",
    confirmPassword: "",
    description: "",
    termsAccepted: false,
  })

  const handleBrandToggle = (brand: string) => {
    setSelectedBrands((prev) => (prev.includes(brand) ? prev.filter((b) => b !== brand) : [...prev, brand]))
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when user types
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, termsAccepted: checked }))

    // Clear error when user checks
    if (errors.termsAccepted) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.termsAccepted
        return newErrors
      })
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Required fields
    const requiredFields = [
      { key: "companyName", label: "Razão Social" },
      { key: "tradingName", label: "Nome Fantasia" },
      { key: "cnpj", label: "CNPJ" },
      { key: "responsible", label: "Nome do Responsável" },
      { key: "email", label: "Email" },
      { key: "phone", label: "Telefone" },
      { key: "address", label: "Endereço" },
      { key: "city", label: "Cidade" },
      { key: "state", label: "Estado" },
      { key: "zipCode", label: "CEP" },
      { key: "password", label: "Senha" },
      { key: "confirmPassword", label: "Confirmar Senha" },
    ]

    requiredFields.forEach(({ key, label }) => {
      if (!formData[key as keyof typeof formData]?.trim()) {
        newErrors[key] = `${label} é obrigatório`
      }
    })

    // Email validation
    if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) {
      newErrors.email = "Email inválido"
    }

    // CNPJ validation (simplified)
    if (formData.cnpj && !/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/.test(formData.cnpj)) {
      newErrors.cnpj = "CNPJ inválido (formato: XX.XXX.XXX/XXXX-XX)"
    }

    // Password validation
    if (formData.password) {
      const hasUpperCase = /[A-Z]/.test(formData.password)
      const hasLowerCase = /[a-z]/.test(formData.password)
      const hasNumber = /[0-9]/.test(formData.password)
      const hasSpecialChar = /[^A-Za-z0-9]/.test(formData.password)

      if (formData.password.length < 8) {
        newErrors.password = "A senha deve ter pelo menos 8 caracteres"
      } else if (!(hasUpperCase && hasLowerCase && hasNumber && hasSpecialChar)) {
        newErrors.password = "A senha deve conter letras maiúsculas, minúsculas, números e caracteres especiais"
      }
    }

    // Confirm password
    if (formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "As senhas não coincidem"
    }

    // Brand selection
    if (selectedBrands.length === 0) {
      newErrors.brands = "Selecione pelo menos uma marca"
    }

    // Terms acceptance
    if (!formData.termsAccepted) {
      newErrors.termsAccepted = "Você deve aceitar os termos e condições"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setFormState("submitting")

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // In a real application, you would send the form data to your backend
      // const response = await fetch('/api/register-dealer', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     ...formData,
      //     selectedBrands,
      //     selectedPlan,
      //   }),
      // })

      // if (!response.ok) throw new Error('Failed to register')

      setFormState("success")
    } catch (error) {
      console.error("Error registering dealership:", error)
      setFormState("error")
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1 py-12">
        <div className="container">
          <div className="mx-auto max-w-5xl">
            <div className="mb-8 text-center">
              <h1 className="mb-2 text-3xl font-bold tracking-tight md:text-4xl">Cadastro de Concessionária</h1>
              <p className="text-lg text-muted-foreground">Anuncie seu estoque de peças e aumente suas vendas</p>
            </div>

            {formState === "success" ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <div className="mb-4 rounded-full bg-green-100 p-3 text-green-600 dark:bg-green-900/20">
                      <CheckCircle className="h-12 w-12" />
                    </div>
                    <h2 className="mb-2 text-2xl font-bold">Cadastro Realizado com Sucesso!</h2>
                    <p className="mb-6 max-w-md text-muted-foreground">
                      Obrigado por se cadastrar na plataforma AutoParts. Nossa equipe entrará em contato em breve para
                      finalizar o processo de ativação da sua conta.
                    </p>
                    <div className="flex gap-4">
                      <Button asChild variant="outline">
                        <Link href="/">Voltar para Home</Link>
                      </Button>
                      <Button asChild>
                        <Link href="/painel">Acessar Painel</Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <form onSubmit={handleSubmit}>
                {formState === "error" && (
                  <Alert className="mb-6 bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Erro no cadastro</AlertTitle>
                    <AlertDescription>
                      Ocorreu um erro ao processar seu cadastro. Por favor, tente novamente mais tarde.
                    </AlertDescription>
                  </Alert>
                )}

                <Tabs defaultValue="company" className="mb-8">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="company">Dados da Empresa</TabsTrigger>
                    <TabsTrigger value="address">Endereço</TabsTrigger>
                    <TabsTrigger value="plan">Plano e Marcas</TabsTrigger>
                  </TabsList>

                  {/* Company Information */}
                  <TabsContent value="company">
                    <Card>
                      <CardHeader>
                        <CardTitle>Dados da Empresa</CardTitle>
                        <CardDescription>Informe os dados da sua concessionária</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="companyName">
                              Razão Social <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="companyName"
                              name="companyName"
                              value={formData.companyName}
                              onChange={handleChange}
                              className={errors.companyName ? "border-red-500" : ""}
                            />
                            {errors.companyName && <p className="text-xs text-red-500">{errors.companyName}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="tradingName">
                              Nome Fantasia <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="tradingName"
                              name="tradingName"
                              value={formData.tradingName}
                              onChange={handleChange}
                              className={errors.tradingName ? "border-red-500" : ""}
                            />
                            {errors.tradingName && <p className="text-xs text-red-500">{errors.tradingName}</p>}
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="cnpj">
                              CNPJ <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="cnpj"
                              name="cnpj"
                              placeholder="XX.XXX.XXX/XXXX-XX"
                              value={formData.cnpj}
                              onChange={handleChange}
                              className={errors.cnpj ? "border-red-500" : ""}
                            />
                            {errors.cnpj && <p className="text-xs text-red-500">{errors.cnpj}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="ie">Inscrição Estadual</Label>
                            <Input
                              id="ie"
                              name="ie"
                              value={formData.ie}
                              onChange={handleChange}
                              className={errors.ie ? "border-red-500" : ""}
                            />
                            {errors.ie && <p className="text-xs text-red-500">{errors.ie}</p>}
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="responsible">
                              Nome do Responsável <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="responsible"
                              name="responsible"
                              value={formData.responsible}
                              onChange={handleChange}
                              className={errors.responsible ? "border-red-500" : ""}
                            />
                            {errors.responsible && <p className="text-xs text-red-500">{errors.responsible}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="email">
                              Email <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="email"
                              name="email"
                              type="email"
                              value={formData.email}
                              onChange={handleChange}
                              className={errors.email ? "border-red-500" : ""}
                            />
                            {errors.email && <p className="text-xs text-red-500">{errors.email}</p>}
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="phone">
                              Telefone <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="phone"
                              name="phone"
                              placeholder="(XX) XXXX-XXXX"
                              value={formData.phone}
                              onChange={handleChange}
                              className={errors.phone ? "border-red-500" : ""}
                            />
                            {errors.phone && <p className="text-xs text-red-500">{errors.phone}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="whatsapp">WhatsApp</Label>
                            <Input
                              id="whatsapp"
                              name="whatsapp"
                              placeholder="(XX) XXXXX-XXXX"
                              value={formData.whatsapp}
                              onChange={handleChange}
                              className={errors.whatsapp ? "border-red-500" : ""}
                            />
                            {errors.whatsapp && <p className="text-xs text-red-500">{errors.whatsapp}</p>}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="description">Descrição da Concessionária</Label>
                          <Textarea
                            id="description"
                            name="description"
                            placeholder="Descreva sua concessionária, especialidades, diferenciais, etc."
                            value={formData.description}
                            onChange={handleChange}
                            rows={4}
                            className={errors.description ? "border-red-500" : ""}
                          />
                          {errors.description && <p className="text-xs text-red-500">{errors.description}</p>}
                        </div>

                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="password">
                              Senha <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="password"
                              name="password"
                              type="password"
                              value={formData.password}
                              onChange={handleChange}
                              className={errors.password ? "border-red-500" : ""}
                            />
                            {errors.password && <p className="text-xs text-red-500">{errors.password}</p>}

                            {/* Password strength indicator */}
                            <PasswordStrength password={formData.password} />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="confirmPassword">
                              Confirmar Senha <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="confirmPassword"
                              name="confirmPassword"
                              type="password"
                              value={formData.confirmPassword}
                              onChange={handleChange}
                              className={errors.confirmPassword ? "border-red-500" : ""}
                            />
                            {errors.confirmPassword && <p className="text-xs text-red-500">{errors.confirmPassword}</p>}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Address Information */}
                  <TabsContent value="address">
                    <Card>
                      <CardHeader>
                        <CardTitle>Endereço</CardTitle>
                        <CardDescription>Informe o endereço da sua concessionária</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="address">
                            Endereço <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="address"
                            name="address"
                            placeholder="Rua, Número"
                            value={formData.address}
                            onChange={handleChange}
                            className={errors.address ? "border-red-500" : ""}
                          />
                          {errors.address && <p className="text-xs text-red-500">{errors.address}</p>}
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="neighborhood">Bairro</Label>
                            <Input
                              id="neighborhood"
                              name="neighborhood"
                              value={formData.neighborhood}
                              onChange={handleChange}
                              className={errors.neighborhood ? "border-red-500" : ""}
                            />
                            {errors.neighborhood && <p className="text-xs text-red-500">{errors.neighborhood}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="zipCode">
                              CEP <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="zipCode"
                              name="zipCode"
                              placeholder="XXXXX-XXX"
                              value={formData.zipCode}
                              onChange={handleChange}
                              className={errors.zipCode ? "border-red-500" : ""}
                            />
                            {errors.zipCode && <p className="text-xs text-red-500">{errors.zipCode}</p>}
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="city">
                              Cidade <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="city"
                              name="city"
                              value={formData.city}
                              onChange={handleChange}
                              className={errors.city ? "border-red-500" : ""}
                            />
                            {errors.city && <p className="text-xs text-red-500">{errors.city}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="state">
                              Estado <span className="text-red-500">*</span>
                            </Label>
                            <Select
                              value={formData.state}
                              onValueChange={(value) => setFormData((prev) => ({ ...prev, state: value }))}
                            >
                              <SelectTrigger id="state" className={errors.state ? "border-red-500" : ""}>
                                <SelectValue placeholder="Selecione um estado" />
                              </SelectTrigger>
                              <SelectContent>
                                {BRAZILIAN_STATES.map((state) => (
                                  <SelectItem key={state.value} value={state.value}>
                                    {state.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {errors.state && <p className="text-xs text-red-500">{errors.state}</p>}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Plan and Brands */}
                  <TabsContent value="plan">
                    <Card>
                      <CardHeader>
                        <CardTitle>Plano e Marcas</CardTitle>
                        <CardDescription>Escolha seu plano e as marcas que você trabalha</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div>
                          <h3 className="mb-4 text-lg font-medium">Escolha seu plano</h3>
                          <div className="grid gap-4 md:grid-cols-3">
                            {SUBSCRIPTION_PLANS.map((plan) => (
                              <div
                                key={plan.id}
                                className={`cursor-pointer rounded-lg border p-4 transition-all hover:border-primary ${
                                  selectedPlan === plan.id ? "border-2 border-primary bg-primary/5" : "border-border"
                                }`}
                                onClick={() => setSelectedPlan(plan.id)}
                              >
                                <div className="mb-2 flex items-center justify-between">
                                  <h4 className="font-medium">{plan.name}</h4>
                                  <div
                                    className={`flex h-5 w-5 items-center justify-center rounded-full border ${
                                      selectedPlan === plan.id
                                        ? "border-primary bg-primary text-white"
                                        : "border-gray-300"
                                    }`}
                                  >
                                    {selectedPlan === plan.id && <div className="h-2 w-2 rounded-full bg-white" />}
                                  </div>
                                </div>
                                <p className="mb-3 text-lg font-bold text-primary">{plan.price}</p>
                                <ul className="space-y-1 text-sm">
                                  {plan.features.map((feature, index) => (
                                    <li key={index} className="flex items-start">
                                      <CheckCircle className="mr-2 mt-0.5 h-3.5 w-3.5 text-green-500" />
                                      <span>{feature}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h3 className="mb-4 text-lg font-medium">
                            Marcas que você trabalha <span className="text-red-500">*</span>
                          </h3>
                          {errors.brands && <p className="mb-2 text-xs text-red-500">{errors.brands}</p>}
                          <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
                            {AUTO_BRANDS.map((brand) => (
                              <div key={brand.value} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`brand-${brand.value}`}
                                  checked={selectedBrands.includes(brand.value)}
                                  onCheckedChange={() => handleBrandToggle(brand.value)}
                                />
                                <Label htmlFor={`brand-${brand.value}`} className="cursor-pointer">
                                  {brand.label}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-start space-x-2 pt-4">
                          <Checkbox
                            id="terms"
                            checked={formData.termsAccepted}
                            onCheckedChange={handleCheckboxChange}
                          />
                          <div className="grid gap-1.5 leading-none">
                            <Label
                              htmlFor="terms"
                              className={`cursor-pointer ${errors.termsAccepted ? "text-red-500" : ""}`}
                            >
                              Aceito os termos e condições <span className="text-red-500">*</span>
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              Ao marcar esta caixa, você concorda com nossos{" "}
                              <Link href="/termos" className="text-primary hover:underline">
                                Termos de Serviço
                              </Link>{" "}
                              e{" "}
                              <Link href="/privacidade" className="text-primary hover:underline">
                                Política de Privacidade
                              </Link>
                              .
                            </p>
                            {errors.termsAccepted && <p className="text-xs text-red-500">{errors.termsAccepted}</p>}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>

                <div className="flex justify-end space-x-4">
                  <Button variant="outline" type="button" asChild>
                    <Link href="/">Cancelar</Link>
                  </Button>
                  <Button type="submit" disabled={formState === "submitting"}>
                    {formState === "submitting" ? "Processando..." : "Finalizar Cadastro"}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

