"""
File import endpoints for the AutoParts API.
"""
import os
import shutil
from typing import List, Optional
from uuid import UUID
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.config import settings
from app.services.file_processor import FileProcessorService
from app.api.deps import (
    get_current_active_user,
    get_current_admin_user
)
from app.schemas.file_import import (
    FileImport,
    FileImportSummary,
    FileImportListResponse,
    FileUploadResponse,
    ImportValidationResult,
    ImportProgressResponse,
    BulkImportRequest,
    BulkImportResponse,
    ImportMappingConfig,
    ImportStats
)
from app.models.user import User
from app.models.file_import import ImportType, ImportStatus

router = APIRouter()


def get_file_processor_service(db: Session = Depends(get_db)) -> FileProcessorService:
    """Get file processor service instance."""
    return FileProcessorService(db)


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    import_type: ImportType = Form(ImportType.INVENTORY),
    current_user: User = Depends(get_current_active_user),
    file_processor: FileProcessorService = Depends(get_file_processor_service)
):
    """
    Upload a file for import processing.
    
    Args:
        file: Uploaded file
        import_type: Type of import
        current_user: Current authenticated user
        file_processor: File processor service instance
        
    Returns:
        Upload response with file information
        
    Raises:
        HTTPException: If upload fails
    """
    # Validate file type
    allowed_extensions = ['.xlsx', '.xls', '.csv']
    file_extension = os.path.splitext(file.filename)[1].lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not supported. Allowed types: {', '.join(allowed_extensions)}"
        )
    
    # Validate file size (max 50MB)
    max_size = 50 * 1024 * 1024  # 50MB
    if file.size > max_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size too large. Maximum size is 50MB"
        )
    
    try:
        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(settings.UPLOAD_DIR, "imports")
        os.makedirs(upload_dir, exist_ok=True)
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = f"{timestamp}_{file.filename}"
        file_path = os.path.join(upload_dir, safe_filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Create file import record
        file_import = file_processor.create_file_import_record(
            file_path=file_path,
            filename=file.filename,
            import_type=import_type,
            created_by=current_user.id
        )
        
        return FileUploadResponse(
            success=True,
            message="File uploaded successfully",
            file_id=file_import.id,
            filename=file.filename,
            file_size=file.size,
            upload_time=file_import.created_at
        )
        
    except Exception as e:
        # Clean up file if it was created
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )


@router.get("/validate/{file_id}", response_model=ImportValidationResult)
async def validate_import_file(
    file_id: UUID,
    current_user: User = Depends(get_current_active_user),
    file_processor: FileProcessorService = Depends(get_file_processor_service)
):
    """
    Validate an uploaded file for import.
    
    Args:
        file_id: File import UUID
        current_user: Current authenticated user
        file_processor: File processor service instance
        
    Returns:
        Validation results
        
    Raises:
        HTTPException: If file not found or validation fails
    """
    # Get file import record
    file_import = file_processor.get_import_progress(file_id)
    
    if not file_import:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File import not found"
        )
    
    # Check permissions (admin or file owner)
    if not current_user.is_admin and file_import.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    try:
        validation_result = file_processor.validate_excel_file(file_import.file_path)
        return validation_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate file: {str(e)}"
        )


@router.post("/process/{file_id}", response_model=BulkImportResponse)
async def process_import_file(
    file_id: UUID,
    dry_run: bool = False,
    current_user: User = Depends(get_current_active_user),
    file_processor: FileProcessorService = Depends(get_file_processor_service)
):
    """
    Process an uploaded file for import.
    
    Args:
        file_id: File import UUID
        dry_run: Whether to perform a dry run
        current_user: Current authenticated user
        file_processor: File processor service instance
        
    Returns:
        Import processing results
        
    Raises:
        HTTPException: If file not found or processing fails
    """
    # Get file import record
    file_import = file_processor.get_import_progress(file_id)
    
    if not file_import:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File import not found"
        )
    
    # Check permissions (admin or file owner)
    if not current_user.is_admin and file_import.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    # Check if file is already being processed
    if file_import.status == ImportStatus.PROCESSING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File is already being processed"
        )
    
    try:
        # Create default mapping configuration
        mapping_config = ImportMappingConfig(
            dealership_mapping={},
            part_mapping={},
            inventory_mapping={},
            default_values={},
            import_options={"dry_run": dry_run}
        )
        
        # Process the import
        start_time = datetime.now()
        result = file_processor.process_inventory_import(
            file_import, mapping_config, dry_run
        )
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        return BulkImportResponse(
            import_id=file_import.id,
            status=file_import.status,
            message="Import processed successfully" if result['status'] == 'completed' else "Import failed",
            total_rows=result['total_rows'],
            processed_rows=result['total_rows'],
            successful_rows=result['successful_rows'],
            failed_rows=result['failed_rows'],
            skipped_rows=result['skipped_rows'],
            errors=[],  # Simplified for now
            warnings=[],
            processing_time_seconds=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process import: {str(e)}"
        )


@router.get("/progress/{file_id}", response_model=ImportProgressResponse)
async def get_import_progress(
    file_id: UUID,
    current_user: User = Depends(get_current_active_user),
    file_processor: FileProcessorService = Depends(get_file_processor_service)
):
    """
    Get import progress for a file.
    
    Args:
        file_id: File import UUID
        current_user: Current authenticated user
        file_processor: File processor service instance
        
    Returns:
        Import progress information
        
    Raises:
        HTTPException: If file not found
    """
    # Get file import record
    file_import = file_processor.get_import_progress(file_id)
    
    if not file_import:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File import not found"
        )
    
    # Check permissions (admin or file owner)
    if not current_user.is_admin and file_import.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return ImportProgressResponse(
        import_id=file_import.id,
        status=file_import.status,
        progress_percentage=file_import.progress_percentage,
        total_rows=file_import.total_rows,
        processed_rows=file_import.processed_rows,
        successful_rows=file_import.successful_rows,
        failed_rows=file_import.failed_rows,
        skipped_rows=file_import.skipped_rows,
        success_rate=file_import.success_rate,
        started_at=file_import.started_at,
        estimated_completion=None,  # Could be calculated based on processing speed
        current_operation=None
    )


@router.get("/", response_model=FileImportListResponse)
async def list_file_imports(
    page: int = 1,
    per_page: int = 20,
    status_filter: Optional[ImportStatus] = None,
    import_type: Optional[ImportType] = None,
    current_user: User = Depends(get_current_active_user),
    file_processor: FileProcessorService = Depends(get_file_processor_service)
):
    """
    List file imports with filtering and pagination.
    
    Args:
        page: Page number
        per_page: Items per page
        status_filter: Filter by import status
        import_type: Filter by import type
        current_user: Current authenticated user
        file_processor: File processor service instance
        
    Returns:
        Paginated list of file imports
    """
    skip = (page - 1) * per_page
    
    # Build query
    query = file_processor.db.query(FileImport)
    
    # Apply filters
    if not current_user.is_admin:
        query = query.filter(FileImport.created_by == current_user.id)
    
    if status_filter:
        query = query.filter(FileImport.status == status_filter)
    
    if import_type:
        query = query.filter(FileImport.import_type == import_type)
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    imports = query.order_by(FileImport.created_at.desc()).offset(skip).limit(per_page).all()
    
    # Convert to summary format
    import_summaries = [
        FileImportSummary(
            id=imp.id,
            filename=imp.filename,
            import_type=imp.import_type,
            status=imp.status,
            total_rows=imp.total_rows,
            processed_rows=imp.processed_rows,
            successful_rows=imp.successful_rows,
            failed_rows=imp.failed_rows,
            progress_percentage=imp.progress_percentage,
            success_rate=imp.success_rate,
            started_at=imp.started_at,
            completed_at=imp.completed_at,
            created_at=imp.created_at
        )
        for imp in imports
    ]
    
    pages = (total + per_page - 1) // per_page
    
    return FileImportListResponse(
        imports=import_summaries,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )
