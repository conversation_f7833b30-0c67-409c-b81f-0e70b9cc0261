"use client"

import { useState } from "react"
import Link from "next/link"
import { MapPin, Phone, MessageCircle } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export function FeaturedProducts() {
  // Estado para controlar quais números de telefone estão visíveis
  const [visiblePhones, setVisiblePhones] = useState<Record<string, boolean>>({})

  // Função para alternar a visibilidade do número de telefone
  const togglePhoneVisibility = (dealershipId: string) => {
    setVisiblePhones((prev) => ({
      ...prev,
      [dealershipId]: !prev[dealershipId],
    }))
  }

  // Função para formatar o número do WhatsApp corretamente
  const formatWhatsAppNumber = (number: string) => {
    // Remove todos os caracteres não numéricos
    const cleanNumber = number.replace(/\D/g, "")

    // Se o número não começar com +55, adiciona o código do país
    if (!cleanNumber.startsWith("55")) {
      return `55${cleanNumber}`
    }

    return cleanNumber
  }

  // Função para criar o link do WhatsApp
  const createWhatsAppLink = (number: string, message?: string) => {
    const formattedNumber = formatWhatsAppNumber(number)
    const baseUrl = "https://wa.me"
    const defaultMessage = "Olá! Gostaria de mais informações sobre peças."

    const encodedMessage = encodeURIComponent(message || defaultMessage)
    return `${baseUrl}/${formattedNumber}?text=${encodedMessage}`
  }

  // Função para abrir o WhatsApp
  const handleWhatsAppClick = (number: string) => {
    const whatsappUrl = createWhatsAppLink(number)
    window.open(whatsappUrl, "_blank")
  }

  // Dados simulados de concessionárias em destaque
  const dealerships = [
    {
      id: "1",
      name: "AutoPeças Premium",
      brands: ["Toyota", "Honda", "Nissan"],
      address: "Av. Paulista, 1000",
      city: "São Paulo",
      state: "SP",
      phone: "(11) 3333-4444",
      whatsapp: "11999999999",
      image: "/placeholder.svg?height=200&width=400",
      description: "Concessionária especializada em peças originais Toyota, Honda e Nissan.",
    },
    {
      id: "2",
      name: "Center Parts",
      brands: ["Volkswagen", "Fiat", "Chevrolet"],
      address: "Rua das Flores, 500",
      city: "Campinas",
      state: "SP",
      phone: "(19) 3333-4444",
      whatsapp: "19999999999",
      image: "/placeholder.svg?height=200&width=400",
      description: "Amplo estoque de peças para veículos nacionais e importados.",
    },
    {
      id: "3",
      name: "Freios & Cia",
      brands: ["Ford", "Chevrolet", "Fiat"],
      address: "Av. Rio Branco, 150",
      city: "Rio de Janeiro",
      state: "RJ",
      phone: "(21) 3333-4444",
      whatsapp: "21999999999",
      image: "/placeholder.svg?height=200&width=400",
      description: "Especializada em sistemas de freios e suspensão para todas as marcas.",
    },
    {
      id: "4",
      name: "Amortex",
      brands: ["Hyundai", "Kia", "Toyota"],
      address: "Av. Afonso Pena, 1500",
      city: "Belo Horizonte",
      state: "MG",
      phone: "(31) 3333-4444",
      whatsapp: "31999999999",
      image: "/placeholder.svg?height=200&width=400",
      description: "Amortecedores e suspensão para veículos nacionais e importados.",
    },
  ]

  return (
    <section className="bg-gray-50 py-20 dark:bg-gray-900">
      <div className="container">
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl">Concessionárias em Destaque</h2>
          <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
            Confira algumas das lojas cadastradas na plataforma
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {dealerships.map((dealership) => (
            <Card key={dealership.id} className="overflow-hidden product-card-hover">
              <div className="relative h-48 w-full">
                <img
                  src={dealership.image || "/placeholder.svg"}
                  alt={dealership.name}
                  className="h-full w-full object-cover"
                />
              </div>
              <CardContent className="p-4">
                <h3 className="mb-2 text-xl font-bold">
                  <Link href={`/concessionarias/${dealership.id}`} className="hover:text-primary hover:underline">
                    {dealership.name}
                  </Link>
                </h3>
                <p className="mb-4 text-sm text-muted-foreground line-clamp-2">{dealership.description}</p>

                <div className="mb-3 flex items-start gap-2 text-sm">
                  <MapPin className="mt-0.5 h-4 w-4 shrink-0 text-muted-foreground" />
                  <div>
                    <p>{dealership.address}</p>
                    <p>
                      {dealership.city}, {dealership.state}
                    </p>
                  </div>
                </div>

                <div className="mb-4 flex flex-wrap gap-1">
                  {dealership.brands.map((brand) => (
                    <Badge key={brand} variant="outline" className="bg-primary/10">
                      {brand}
                    </Badge>
                  ))}
                </div>

                <div className="flex flex-col gap-2">
                  {/* Substituído o botão por um div não clicável */}
                  <div className="flex h-9 w-full items-center justify-center gap-2 rounded-md border border-input bg-background px-3 text-sm font-medium text-foreground">
                    <Phone className="h-4 w-4" />
                    {dealership.phone}
                  </div>

                  <Button
                    size="sm"
                    className="w-full bg-green-500 hover:bg-green-600"
                    onClick={() => handleWhatsAppClick(dealership.whatsapp)}
                  >
                    <MessageCircle className="mr-2 h-4 w-4" />
                    WhatsApp
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link
            href="/concessionarias"
            className="inline-flex items-center text-primary underline underline-offset-4 hover:text-primary/80"
          >
            Ver todas as concessionárias
          </Link>
        </div>
      </div>
    </section>
  )
}

