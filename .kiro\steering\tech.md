# Technology Stack

## Framework & Runtime
- **Next.js 15.2.4** with App Router
- **React 19** with TypeScript
- **Node.js** runtime environment

## Styling & UI
- **Tailwind CSS 4** for styling with custom design system
- **shadcn/ui** component library (New York style)
- **Radix UI** primitives for accessible components
- **Lucide React** for icons
- **next-themes** for dark/light mode support

## Key Libraries
- **class-variance-authority** & **clsx** for conditional styling
- **tailwind-merge** for className optimization
- **date-fns** for date manipulation
- **recharts** for data visualization
- **react-day-picker** for date selection

## Development Tools
- **TypeScript 5** for type safety
- **ESLint 9** with Next.js config
- **PostCSS** for CSS processing

## Build & Development Commands

```bash
# Development server with Turbopack
npm run dev

# Production build
npm run build

# Start production server
npm run start

# Lint code
npm run lint
```

## Path Aliases
- `@/*` maps to project root
- `@/components` for UI components
- `@/lib` for utilities
- `@/hooks` for custom React hooks
- `@/components/ui` for shadcn/ui components

## Performance Features
- Turbopack for faster development builds
- Next.js App Router for optimized routing
- Font optimization with Inter font
- Image optimization built-in