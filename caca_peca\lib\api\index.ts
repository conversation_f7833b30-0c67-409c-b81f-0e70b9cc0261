/**
 * AutoParts API Services
 * Central export for all API services and types
 */

// Export main client
export { default as apiClient } from './client';

// Export services
export { authService } from './auth';
export { partsService } from './parts';
export { inventoryService } from './inventory';
export { dealershipsService } from './dealerships';

// Export types
export * from '../types/api';

// Re-export service classes for direct instantiation if needed
export { AuthService } from './auth';
export { PartsService } from './parts';
export { InventoryService } from './inventory';
export { DealershipsService } from './dealerships';
