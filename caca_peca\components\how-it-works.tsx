import { Search, ShoppingCart, CheckCircle, Smartphone } from "lucide-react"

export function HowItWorks() {
  const steps = [
    {
      icon: Search,
      title: "Pesquise pelo código",
      description: "Digite a referência genuína da peça e clique em Buscar",
    },
    {
      icon: ShoppingCart,
      title: "Compare ofertas",
      description: "Veja preços e disponibilidade",
    },
    {
      icon: Smartphone,
      title: "Negocie direto com a loja",
      description: "Fale diretamente com a concessionária via WhatsApp",
    },
    {
      icon: CheckCircle,
      title: "<PERSON><PERSON><PERSON> ou retire",
      description: "Combine a entrega com a concessionária",
    },
  ]

  return (
    <section id="como-funciona" className="py-20">
      <div className="container">
        <div className="relative mb-12 text-center">
          <span className="absolute left-1/2 -translate-x-1/2 -top-12 inline-block rounded-full bg-blue-100 px-4 py-1.5 text-sm font-medium text-blue-600">
            Busca gratuita
          </span>
          <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl">Como funciona</h2>
          <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
            Busque pelo código da peça e conecte-se diretamente às concessionárias
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary">
                <step.icon className="h-8 w-8" />
              </div>
              <h3 className="mb-2 text-xl font-semibold">{step.title}</h3>
              <p className="text-muted-foreground">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

