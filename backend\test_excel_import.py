"""
Test script for Excel import functionality.
"""
import sys
import os
import asyncio
from pathlib import Path

# Add the backend directory to the Python path
sys.path.append(str(Path(__file__).parent))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.file_processor import FileProcessorService
from app.models.file_import import ImportType
from app.schemas.file_import import ImportMappingConfig


async def test_excel_import():
    """Test the Excel import functionality with the specific file."""
    
    # File path
    excel_file_path = r"C:\Users\<USER>\Documents\Cursor\caca-pecas\RPR073_ANALISEITENSEMESTOQUE240725140649.xlsx"
    
    if not os.path.exists(excel_file_path):
        print(f"❌ File not found: {excel_file_path}")
        return
    
    print(f"📁 Testing import of: {excel_file_path}")
    
    # Create database session
    db: Session = SessionLocal()
    
    try:
        # Initialize file processor service
        file_processor = FileProcessorService(db)
        
        print("🔍 Step 1: Validating Excel file...")
        
        # Validate the Excel file
        validation_result = file_processor.validate_excel_file(excel_file_path)
        
        print(f"✅ Validation completed:")
        print(f"   - Valid: {validation_result.is_valid}")
        print(f"   - Total rows: {validation_result.total_rows}")
        print(f"   - Detected columns: {len(validation_result.detected_columns)}")
        
        if validation_result.detected_columns:
            print("📋 Detected columns:")
            for i, col in enumerate(validation_result.detected_columns, 1):
                print(f"   {i:2d}. {col}")
        
        if validation_result.mapping_suggestions:
            print("\n💡 Mapping suggestions:")
            for excel_col, db_field in validation_result.mapping_suggestions.items():
                print(f"   {excel_col} -> {db_field}")
        
        if validation_result.validation_errors:
            print("\n❌ Validation errors:")
            for error in validation_result.validation_errors:
                print(f"   - {error}")
        
        if validation_result.warnings:
            print("\n⚠️  Warnings:")
            for warning in validation_result.warnings:
                print(f"   - {warning}")
        
        if validation_result.sample_data:
            print("\n📊 Sample data (first row):")
            sample_row = validation_result.sample_data[0]
            for key, value in sample_row.items():
                print(f"   {key}: {value}")
        
        if not validation_result.is_valid:
            print("\n❌ File validation failed. Cannot proceed with import.")
            return
        
        print("\n🚀 Step 2: Creating file import record...")
        
        # Create file import record
        file_import = file_processor.create_file_import_record(
            file_path=excel_file_path,
            filename=os.path.basename(excel_file_path),
            import_type=ImportType.INVENTORY,
            created_by=None  # No user for test
        )
        
        print(f"✅ File import record created: {file_import.id}")
        
        print("\n🔄 Step 3: Processing import (DRY RUN)...")
        
        # Create mapping configuration
        mapping_config = ImportMappingConfig(
            dealership_mapping={},
            part_mapping={},
            inventory_mapping={},
            default_values={},
            import_options={"dry_run": True}
        )
        
        # Process the import (dry run first)
        result = file_processor.process_inventory_import(
            file_import, mapping_config, dry_run=True
        )
        
        print(f"✅ Dry run completed:")
        print(f"   - Status: {result['status']}")
        print(f"   - Total rows: {result['total_rows']}")
        print(f"   - Successful: {result['successful_rows']}")
        print(f"   - Failed: {result['failed_rows']}")
        print(f"   - Skipped: {result['skipped_rows']}")
        
        if result['errors']:
            print(f"\n❌ Errors found ({len(result['errors'])}):")
            for i, error in enumerate(result['errors'][:5], 1):  # Show first 5 errors
                print(f"   {i}. Row {error.get('row_number', 'N/A')}: {error.get('error_message', 'Unknown error')}")
            if len(result['errors']) > 5:
                print(f"   ... and {len(result['errors']) - 5} more errors")
        
        # Ask if user wants to proceed with actual import
        if result['successful_rows'] > 0:
            print(f"\n🎯 Dry run successful! Found {result['successful_rows']} valid rows.")
            
            # For testing, we'll skip the actual import to avoid modifying the database
            print("⏭️  Skipping actual import for safety. To perform actual import, set dry_run=False")
            
            # Uncomment the following lines to perform actual import:
            # print("\n💾 Step 4: Processing actual import...")
            # result = file_processor.process_inventory_import(
            #     file_import, mapping_config, dry_run=False
            # )
            # print(f"✅ Actual import completed: {result['status']}")
        
        print("\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during import test: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()


if __name__ == "__main__":
    print("🧪 AutoParts Excel Import Test")
    print("=" * 50)
    
    # Run the test
    asyncio.run(test_excel_import())
