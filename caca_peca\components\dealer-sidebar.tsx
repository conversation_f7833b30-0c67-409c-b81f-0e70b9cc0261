"use client"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ShoppingCart, Home, Package, Settings, LogOut, FileText, Tag, Upload, User, CreditCard } from "lucide-react"

export function AppSidebar() {
  const pathname = usePathname()
  const router = useRouter()

  const menuItems = [
    {
      title: "Dashboard",
      icon: Home,
      href: "/painel",
    },
    {
      title: "Estoque",
      icon: Package,
      href: "/painel/estoque",
    },
    {
      title: "Importar Estoque",
      icon: Upload,
      href: "/painel/importar-estoque",
    },
    {
      title: "Promoções",
      icon: Tag,
      href: "/painel/promocoes",
    },
    {
      title: "Assinatura",
      icon: CreditCard,
      href: "/painel/assinatura",
    },
    {
      title: "Faturas",
      icon: FileText,
      href: "/painel/faturas",
    },
    {
      title: "Perfil",
      icon: User,
      href: "/painel/perfil",
    },
    {
      title: "Configurações",
      icon: Settings,
      href: "/painel/configuracoes",
    },
  ]

  const handleLogout = async () => {
    try {
      // Import auth service dynamically to avoid circular dependencies
      const { authService } = await import("@/lib/api/auth");
      await authService.logout();
      router.push("/auth");
    } catch (error) {
      console.error('Logout error:', error);
      router.push("/auth");
    }
  }

  return (
    <aside className="flex h-full w-64 flex-col border-r bg-background">
      <div className="flex h-16 items-center border-b px-4">
        <Link href="/" className="flex items-center space-x-2">
          <ShoppingCart className="h-6 w-6 text-primary" />
          <span className="text-xl font-bold">AutoParts</span>
        </Link>
      </div>

      <nav className="flex-1 space-y-1 p-2">
        {menuItems.map((item) => (
          <Link
            key={item.title}
            href={item.href}
            className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
              pathname === item.href
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            }`}
          >
            <item.icon className="h-5 w-5" />
            <span>{item.title}</span>
          </Link>
        ))}
      </nav>

      <div className="border-t p-2">
        <Button variant="ghost" className="w-full justify-start space-x-2" onClick={handleLogout}>
          <LogOut className="h-5 w-5" />
          <span>Sair</span>
        </Button>
      </div>
    </aside>
  )
}

