# AutoParts API Makefile
# Simplifies common development and deployment tasks

.PHONY: help install dev prod staging test clean backup monitor logs shell

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

# Configuration
COMPOSE_FILE_DEV := docker-compose.dev.yml
COMPOSE_FILE_PROD := docker-compose.yml
ENV_FILE := .env

help: ## Show this help message
	@echo "$(BLUE)AutoParts API - Available Commands$(NC)"
	@echo "=================================="
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

# Installation and Setup
install: ## Install dependencies and setup environment
	@echo "$(BLUE)Setting up AutoParts API...$(NC)"
	@if [ ! -f "$(ENV_FILE)" ]; then \
		echo "$(YELLOW)Creating .env file from template...$(NC)"; \
		cp .env.example .env; \
		echo "$(YELLOW)Please edit .env file with your configuration$(NC)"; \
	fi
	@echo "$(GREEN)Setup completed!$(NC)"
	@echo "$(YELLOW)Next steps:$(NC)"
	@echo "  1. Edit .env file with your configuration"
	@echo "  2. Run 'make dev' to start development environment"
	@echo "  3. Run 'make test' to run tests"

# Development Environment
dev: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(NC)"
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh development

dev-build: ## Build and start development environment (force rebuild)
	@echo "$(BLUE)Building and starting development environment...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) build --no-cache
	@docker-compose -f $(COMPOSE_FILE_DEV) up -d

dev-stop: ## Stop development environment
	@echo "$(BLUE)Stopping development environment...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) down

dev-restart: ## Restart development environment
	@echo "$(BLUE)Restarting development environment...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_DEV) restart

# Production Environment
prod: ## Deploy to production
	@echo "$(BLUE)Deploying to production...$(NC)"
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh production

prod-build: ## Build and deploy production (force rebuild)
	@echo "$(BLUE)Building and deploying production...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_PROD) build --no-cache
	@docker-compose -f $(COMPOSE_FILE_PROD) up -d

prod-stop: ## Stop production environment
	@echo "$(BLUE)Stopping production environment...$(NC)"
	@docker-compose -f $(COMPOSE_FILE_PROD) down

# Staging Environment
staging: ## Deploy to staging
	@echo "$(BLUE)Deploying to staging...$(NC)"
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh staging

# Database Management
db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	@docker-compose exec api alembic upgrade head

db-rollback: ## Rollback last database migration
	@echo "$(BLUE)Rolling back database migration...$(NC)"
	@docker-compose exec api alembic downgrade -1

db-reset: ## Reset database (WARNING: This will delete all data!)
	@echo "$(RED)WARNING: This will delete all data!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose exec api alembic downgrade base
	@docker-compose exec api alembic upgrade head

db-shell: ## Open database shell
	@echo "$(BLUE)Opening database shell...$(NC)"
	@docker-compose exec postgres psql -U autoparts -d autoparts

# Testing
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(NC)"
	@cd backend && python -m pytest tests/ -v

test-unit: ## Run unit tests only
	@echo "$(BLUE)Running unit tests...$(NC)"
	@cd backend && python -m pytest tests/unit/ -v

test-integration: ## Run integration tests only
	@echo "$(BLUE)Running integration tests...$(NC)"
	@cd backend && python -m pytest tests/integration/ -v

test-coverage: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@cd backend && python -m pytest tests/ --cov=app --cov-report=html --cov-report=term

test-watch: ## Run tests in watch mode
	@echo "$(BLUE)Running tests in watch mode...$(NC)"
	@cd backend && python -m pytest tests/ -f

# Code Quality
lint: ## Run code linting
	@echo "$(BLUE)Running code linting...$(NC)"
	@cd backend && python -m flake8 app/
	@cd backend && python -m black --check app/
	@cd backend && python -m isort --check-only app/

format: ## Format code
	@echo "$(BLUE)Formatting code...$(NC)"
	@cd backend && python -m black app/
	@cd backend && python -m isort app/

type-check: ## Run type checking
	@echo "$(BLUE)Running type checking...$(NC)"
	@cd backend && python -m mypy app/

# Monitoring and Maintenance
monitor: ## Run health monitoring
	@echo "$(BLUE)Running health monitoring...$(NC)"
	@chmod +x scripts/monitor.sh
	@./scripts/monitor.sh

monitor-report: ## Generate detailed health report
	@echo "$(BLUE)Generating health report...$(NC)"
	@chmod +x scripts/monitor.sh
	@./scripts/monitor.sh report

# Backup and Restore
backup: ## Create backup
	@echo "$(BLUE)Creating backup...$(NC)"
	@chmod +x scripts/backup.sh
	@./scripts/backup.sh

backup-verify: ## Verify backup integrity
	@echo "$(BLUE)Verifying backup integrity...$(NC)"
	@chmod +x scripts/backup.sh
	@./scripts/backup.sh verify

# Logs and Debugging
logs: ## Show application logs
	@docker-compose logs -f api

logs-all: ## Show all service logs
	@docker-compose logs -f

logs-db: ## Show database logs
	@docker-compose logs -f postgres

logs-redis: ## Show Redis logs
	@docker-compose logs -f redis

logs-nginx: ## Show Nginx logs
	@docker-compose logs -f nginx

# Shell Access
shell: ## Open shell in API container
	@echo "$(BLUE)Opening shell in API container...$(NC)"
	@docker-compose exec api bash

shell-db: ## Open shell in database container
	@echo "$(BLUE)Opening shell in database container...$(NC)"
	@docker-compose exec postgres bash

shell-redis: ## Open Redis CLI
	@echo "$(BLUE)Opening Redis CLI...$(NC)"
	@docker-compose exec redis redis-cli

# Status and Information
status: ## Show service status
	@echo "$(BLUE)Service Status:$(NC)"
	@docker-compose ps

ps: ## Show running containers
	@docker ps --filter "name=autoparts"

stats: ## Show container resource usage
	@docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# Cleanup
clean: ## Clean up containers and volumes
	@echo "$(BLUE)Cleaning up...$(NC)"
	@docker-compose down -v
	@docker system prune -f

clean-all: ## Clean up everything (containers, volumes, images)
	@echo "$(RED)WARNING: This will remove all containers, volumes, and images!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose down -v --rmi all
	@docker system prune -a -f

# Security
security-scan: ## Run security scan on dependencies
	@echo "$(BLUE)Running security scan...$(NC)"
	@cd backend && python -m pip-audit

# Documentation
docs: ## Generate API documentation
	@echo "$(BLUE)API Documentation available at:$(NC)"
	@echo "  Development: http://localhost:8000/docs"
	@echo "  Production: https://api.autoparts.com/docs"

# Quick Start
quick-start: install dev ## Quick start for new developers
	@echo "$(GREEN)AutoParts API is now running!$(NC)"
	@echo "$(BLUE)Available URLs:$(NC)"
	@echo "  API: http://localhost:8000"
	@echo "  API Docs: http://localhost:8000/docs"
	@echo "  pgAdmin: http://localhost:8082"
	@echo "  Redis Commander: http://localhost:8081"
	@echo ""
	@echo "$(YELLOW)Useful commands:$(NC)"
	@echo "  make logs     - View application logs"
	@echo "  make test     - Run tests"
	@echo "  make monitor  - Check system health"
	@echo "  make backup   - Create backup"

# CI/CD
ci-test: ## Run tests for CI/CD
	@echo "$(BLUE)Running CI tests...$(NC)"
	@cd backend && python -m pytest tests/ --junitxml=test-results.xml --cov=app --cov-report=xml

ci-build: ## Build for CI/CD
	@echo "$(BLUE)Building for CI/CD...$(NC)"
	@docker build -t autoparts-api:latest ./backend

ci-deploy: ## Deploy for CI/CD
	@echo "$(BLUE)Deploying for CI/CD...$(NC)"
	@./scripts/deploy.sh production
