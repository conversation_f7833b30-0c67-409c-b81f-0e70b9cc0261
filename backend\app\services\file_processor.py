"""
File processing service for Excel/CSV imports.
"""
import pandas as pd
import hashlib
import os
from typing import Dict, List, Any, Optional, Tuple
from uuid import UUID
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.models.file_import import FileImport, ImportRowError, ImportStatus, ImportType
from app.models.part import Part
from app.models.inventory import Inventory
from app.models.dealership import Dealership
from app.repositories.part import PartRepository
from app.repositories.inventory import InventoryRepository
from app.repositories.dealership import DealershipRepository
from app.schemas.file_import import (
    FileImportCreate,
    ImportValidationResult,
    ColumnMappingConfig,
    ImportMappingConfig
)

logger = logging.getLogger(__name__)


class FileProcessorService:
    """
    Service for processing Excel/CSV files for inventory imports.
    """

    def __init__(self, db: Session):
        self.db = db
        self.part_repo = PartRepository(db)
        self.inventory_repo = InventoryRepository(db)
        self.dealership_repo = DealershipRepository(db)

    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of a file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    def validate_excel_file(self, file_path: str) -> ImportValidationResult:
        """
        Validate Excel file and return validation results.
        
        Args:
            file_path: Path to the Excel file
            
        Returns:
            Validation results with detected columns and suggestions
        """
        try:
            # Read Excel file
            df = pd.read_excel(file_path)
            
            # Get basic information
            total_rows = len(df)
            detected_columns = df.columns.tolist()
            
            # Get sample data (first 5 rows)
            sample_data = df.head(5).to_dict('records')
            
            # Clean sample data (convert NaN to None)
            for row in sample_data:
                for key, value in row.items():
                    if pd.isna(value):
                        row[key] = None
            
            # Generate mapping suggestions based on column names
            mapping_suggestions = self._generate_mapping_suggestions(detected_columns)
            
            # Validate data types and content
            validation_errors = []
            warnings = []
            
            # Check for empty file
            if total_rows == 0:
                validation_errors.append("File is empty")
            
            # Check for required columns
            required_columns = ['CodigoReferenciaProduto', 'Produto_Descricao', 'QtdeSaldo']
            missing_required = [col for col in required_columns if col not in detected_columns]
            if missing_required:
                validation_errors.append(f"Missing required columns: {', '.join(missing_required)}")
            
            # Check for duplicate columns
            if len(detected_columns) != len(set(detected_columns)):
                warnings.append("Duplicate column names detected")
            
            # Validate data types in sample
            self._validate_sample_data_types(sample_data, warnings)
            
            return ImportValidationResult(
                is_valid=len(validation_errors) == 0,
                total_rows=total_rows,
                sample_data=sample_data,
                detected_columns=detected_columns,
                mapping_suggestions=mapping_suggestions,
                validation_errors=validation_errors,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"Error validating Excel file: {e}")
            return ImportValidationResult(
                is_valid=False,
                total_rows=0,
                sample_data=[],
                detected_columns=[],
                mapping_suggestions={},
                validation_errors=[f"Error reading file: {str(e)}"],
                warnings=[]
            )

    def _generate_mapping_suggestions(self, columns: List[str]) -> Dict[str, str]:
        """Generate mapping suggestions based on column names."""
        # Mapping from Excel columns to database fields
        column_mappings = {
            # Dealership fields
            'EmpresaNome': 'dealership.name',
            
            # Part fields
            'CodigoReferenciaProduto': 'part.code',
            'Produto_Descricao': 'part.name',
            'Marca_Descricao': 'part.brand',
            
            # Inventory fields
            'QtdeSaldo': 'inventory.quantity',
            'ValorCustoMedio': 'inventory.cost',
            'ValorPublico': 'inventory.price',
            'ValorPublicoSugerido': 'inventory.suggested_price',
            'ValorReposicao': 'inventory.replacement_value',
            'ValorGarantia': 'inventory.warranty_value',
            'ClasABCLetraPopularidade': 'inventory.abc_classification',
            'LocalizacaoProdutoId': 'inventory.location'
        }
        
        suggestions = {}
        for col in columns:
            if col in column_mappings:
                suggestions[col] = column_mappings[col]
            else:
                # Try to find partial matches
                col_lower = col.lower()
                for excel_col, db_field in column_mappings.items():
                    if excel_col.lower() in col_lower or col_lower in excel_col.lower():
                        suggestions[col] = db_field
                        break
        
        return suggestions

    def _validate_sample_data_types(self, sample_data: List[Dict], warnings: List[str]):
        """Validate data types in sample data."""
        if not sample_data:
            return
        
        # Check for numeric fields
        numeric_fields = ['QtdeSaldo', 'ValorCustoMedio', 'ValorPublico', 'ValorPublicoSugerido', 
                         'ValorReposicao', 'ValorGarantia']
        
        for field in numeric_fields:
            if field in sample_data[0]:
                for row in sample_data:
                    value = row.get(field)
                    if value is not None and not isinstance(value, (int, float)):
                        try:
                            float(str(value).replace(',', '.'))
                        except (ValueError, TypeError):
                            warnings.append(f"Non-numeric value found in {field}: {value}")
                            break

    def create_file_import_record(
        self,
        file_path: str,
        filename: str,
        import_type: ImportType,
        created_by: Optional[UUID] = None
    ) -> FileImport:
        """
        Create a file import record in the database.
        
        Args:
            file_path: Path to the uploaded file
            filename: Original filename
            import_type: Type of import
            created_by: User who initiated the import
            
        Returns:
            Created FileImport instance
        """
        file_size = os.path.getsize(file_path)
        file_hash = self.calculate_file_hash(file_path)
        
        import_data = FileImportCreate(
            filename=filename,
            file_path=file_path,
            file_size=file_size,
            file_hash=file_hash,
            import_type=import_type
        )
        
        file_import = FileImport(
            **import_data.dict(),
            created_by=created_by,
            status=ImportStatus.PENDING
        )
        
        self.db.add(file_import)
        self.db.commit()
        self.db.refresh(file_import)
        
        logger.info(f"Created file import record: {file_import.id}")
        return file_import

    def process_inventory_import(
        self,
        file_import: FileImport,
        mapping_config: ImportMappingConfig,
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        Process inventory import from Excel file.
        
        Args:
            file_import: FileImport instance
            mapping_config: Column mapping configuration
            dry_run: Whether to perform a dry run without saving
            
        Returns:
            Import results
        """
        try:
            # Mark import as started
            file_import.mark_started()
            self.db.commit()
            
            # Read Excel file
            df = pd.read_excel(file_import.file_path)
            file_import.total_rows = len(df)
            self.db.commit()
            
            # Process rows
            processed = 0
            successful = 0
            failed = 0
            skipped = 0
            errors = []
            
            for index, row in df.iterrows():
                try:
                    row_number = index + 2  # Excel row number (1-indexed + header)
                    
                    # Process the row
                    result = self._process_inventory_row(
                        row, row_number, mapping_config, dry_run
                    )
                    
                    if result['status'] == 'success':
                        successful += 1
                    elif result['status'] == 'skipped':
                        skipped += 1
                    else:
                        failed += 1
                        errors.append(result['error'])
                    
                    processed += 1
                    
                    # Update progress every 100 rows
                    if processed % 100 == 0:
                        file_import.update_progress(processed, successful, failed, skipped)
                        self.db.commit()
                        
                except Exception as e:
                    failed += 1
                    error = ImportRowError(
                        import_id=file_import.id,
                        row_number=index + 2,
                        error_type="processing_error",
                        error_message=str(e),
                        row_data=row.to_dict()
                    )
                    errors.append(error)
                    logger.error(f"Error processing row {index + 2}: {e}")
            
            # Final progress update
            file_import.update_progress(processed, successful, failed, skipped)
            
            # Save errors to database
            if errors and not dry_run:
                for error in errors:
                    self.db.add(error)
            
            # Mark as completed
            file_import.mark_completed()
            self.db.commit()
            
            return {
                'status': 'completed',
                'total_rows': processed,
                'successful_rows': successful,
                'failed_rows': failed,
                'skipped_rows': skipped,
                'errors': [error.__dict__ for error in errors]
            }
            
        except Exception as e:
            logger.error(f"Error processing import {file_import.id}: {e}")
            file_import.mark_failed(str(e))
            self.db.commit()
            raise e

    def _process_inventory_row(
        self,
        row: pd.Series,
        row_number: int,
        mapping_config: ImportMappingConfig,
        dry_run: bool
    ) -> Dict[str, Any]:
        """
        Process a single inventory row.
        
        Args:
            row: Pandas Series representing the row
            row_number: Row number in the Excel file
            mapping_config: Column mapping configuration
            dry_run: Whether this is a dry run
            
        Returns:
            Processing result
        """
        try:
            # Extract data based on mapping
            dealership_name = self._get_mapped_value(row, 'EmpresaNome', mapping_config)
            part_code = self._get_mapped_value(row, 'CodigoReferenciaProduto', mapping_config)
            part_name = self._get_mapped_value(row, 'Produto_Descricao', mapping_config)
            quantity = self._get_mapped_value(row, 'QtdeSaldo', mapping_config)
            
            # Validate required fields
            if not part_code:
                return {
                    'status': 'failed',
                    'error': ImportRowError(
                        row_number=row_number,
                        error_type="validation_error",
                        error_message="Missing part code",
                        field_name="CodigoReferenciaProduto"
                    )
                }
            
            if not dealership_name:
                return {
                    'status': 'failed',
                    'error': ImportRowError(
                        row_number=row_number,
                        error_type="validation_error",
                        error_message="Missing dealership name",
                        field_name="EmpresaNome"
                    )
                }
            
            # Skip rows with zero or negative quantity
            if not quantity or quantity <= 0:
                return {'status': 'skipped'}
            
            if dry_run:
                return {'status': 'success'}
            
            # Find or create dealership
            dealership = self._find_or_create_dealership(dealership_name)
            
            # Find or create part
            part = self._find_or_create_part(row, mapping_config)
            
            # Create or update inventory
            self._create_or_update_inventory(dealership, part, row, mapping_config)
            
            return {'status': 'success'}
            
        except Exception as e:
            return {
                'status': 'failed',
                'error': ImportRowError(
                    row_number=row_number,
                    error_type="processing_error",
                    error_message=str(e),
                    row_data=row.to_dict()
                )
            }

    def _get_mapped_value(self, row: pd.Series, excel_column: str, mapping_config: ImportMappingConfig) -> Any:
        """Get value from row based on mapping configuration."""
        # For now, use direct column mapping
        # In the future, this will use the mapping_config
        if excel_column in row.index:
            value = row[excel_column]
            return None if pd.isna(value) else value
        return None

    def _find_or_create_dealership(self, dealership_name: str) -> Dealership:
        """Find existing dealership or create a new one."""
        # Try to find existing dealership by name
        dealership = self.db.query(Dealership).filter(
            Dealership.name.ilike(f"%{dealership_name}%")
        ).first()

        if not dealership:
            # Create new dealership with minimal information
            dealership = Dealership(
                name=dealership_name,
                email=f"contato@{dealership_name.lower().replace(' ', '')}.com",
                cnpj="00000000000000",  # Placeholder CNPJ
                phone="(00) 0000-0000",  # Placeholder phone
                address="Endereço não informado",
                city="Cidade não informada",
                state="SP",  # Default state
                zip_code="00000-000",  # Placeholder ZIP
                is_active=True
            )
            self.db.add(dealership)
            self.db.flush()  # Get the ID without committing
            logger.info(f"Created new dealership: {dealership_name}")

        return dealership

    def _find_or_create_part(self, row: pd.Series, mapping_config: ImportMappingConfig) -> Part:
        """Find existing part or create a new one."""
        part_code = self._get_mapped_value(row, 'CodigoReferenciaProduto', mapping_config)
        part_name = self._get_mapped_value(row, 'Produto_Descricao', mapping_config)
        brand = self._get_mapped_value(row, 'Marca_Descricao', mapping_config)

        # Try to find existing part by code
        part = self.part_repo.get_by_code(part_code)

        if not part:
            # Create new part
            part = Part(
                code=part_code,
                name=part_name or f"Peça {part_code}",
                description=part_name,
                brand=brand,
                category="Importado"  # Default category for imported parts
            )
            self.db.add(part)
            self.db.flush()  # Get the ID without committing
            logger.info(f"Created new part: {part_code}")
        else:
            # Update part information if we have more details
            if part_name and not part.description:
                part.description = part_name
            if brand and not part.brand:
                part.brand = brand

        return part

    def _create_or_update_inventory(
        self,
        dealership: Dealership,
        part: Part,
        row: pd.Series,
        mapping_config: ImportMappingConfig
    ):
        """Create or update inventory item."""
        # Get inventory data from row
        quantity = self._get_mapped_value(row, 'QtdeSaldo', mapping_config) or 0
        cost = self._get_mapped_value(row, 'ValorCustoMedio', mapping_config)
        price = self._get_mapped_value(row, 'ValorPublico', mapping_config)
        location = self._get_mapped_value(row, 'LocalizacaoProdutoId', mapping_config)

        # Convert string numbers to float if needed
        if isinstance(quantity, str):
            quantity = float(quantity.replace(',', '.')) if quantity else 0
        if isinstance(cost, str):
            cost = float(cost.replace(',', '.')) if cost else None
        if isinstance(price, str):
            price = float(price.replace(',', '.')) if price else None

        # Ensure we have a valid price
        if not price and cost:
            price = cost * 1.3  # 30% markup if no price specified
        elif not price:
            price = 10.0  # Default price

        # Try to find existing inventory item
        existing_inventory = self.inventory_repo.get_by_dealership_and_part(
            dealership.id, part.id
        )

        if existing_inventory:
            # Update existing inventory
            existing_inventory.quantity = int(quantity)
            existing_inventory.price = price
            if cost:
                existing_inventory.cost = cost
            if location:
                existing_inventory.location = str(location)
            existing_inventory.last_updated = datetime.utcnow()
            logger.info(f"Updated inventory for part {part.code} at dealership {dealership.name}")
        else:
            # Create new inventory item
            inventory = Inventory(
                dealership_id=dealership.id,
                part_id=part.id,
                quantity=int(quantity),
                price=price,
                cost=cost,
                location=str(location) if location else None,
                condition="new",
                is_available=True,
                last_updated=datetime.utcnow()
            )
            self.db.add(inventory)
            logger.info(f"Created inventory for part {part.code} at dealership {dealership.name}")

    def get_import_progress(self, import_id: UUID) -> Optional[FileImport]:
        """Get import progress by ID."""
        return self.db.query(FileImport).filter(FileImport.id == import_id).first()

    def get_import_errors(self, import_id: UUID) -> List[ImportRowError]:
        """Get import errors by import ID."""
        return self.db.query(ImportRowError).filter(
            ImportRowError.import_id == import_id
        ).all()
