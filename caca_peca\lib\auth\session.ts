/**
 * Session Persistence Utilities
 * Handles session persistence across browser refreshes and tabs
 */

import { jwtService } from './jwt-service';
import { useAuthStore } from '../stores/auth-store';

interface SessionData {
  lastActivity: number;
  userAgent: string;
  sessionId: string;
}

const SESSION_KEY = 'autoparts_session';
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
const ACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes of inactivity

export class SessionManager {
  private sessionId: string;
  private activityTimer: NodeJS.Timeout | null = null;
  private storageListener: ((e: StorageEvent) => void) | null = null;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeSession();
  }

  /**
   * Initialize session management
   */
  private initializeSession(): void {
    if (typeof window === 'undefined') return;

    // Load existing session
    this.loadSession();

    // Set up activity tracking
    this.setupActivityTracking();

    // Set up cross-tab synchronization
    this.setupCrossTabSync();

    // Set up beforeunload handler
    this.setupBeforeUnload();
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Load session from storage
   */
  private loadSession(): void {
    try {
      const sessionData = localStorage.getItem(SESSION_KEY);
      if (sessionData) {
        const session: SessionData = JSON.parse(sessionData);
        
        // Check if session is still valid
        if (this.isSessionValid(session)) {
          this.sessionId = session.sessionId;
          this.updateLastActivity();
        } else {
          this.clearSession();
        }
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      this.clearSession();
    }
  }

  /**
   * Check if session is valid
   */
  private isSessionValid(session: SessionData): boolean {
    const now = Date.now();
    const sessionAge = now - (session.lastActivity || 0);
    
    // Check session timeout
    if (sessionAge > SESSION_TIMEOUT) {
      return false;
    }

    // Check activity timeout
    if (sessionAge > ACTIVITY_TIMEOUT) {
      return false;
    }

    // Check user agent (basic security check)
    if (session.userAgent !== navigator.userAgent) {
      return false;
    }

    return true;
  }

  /**
   * Save session to storage
   */
  private saveSession(): void {
    try {
      const sessionData: SessionData = {
        lastActivity: Date.now(),
        userAgent: navigator.userAgent,
        sessionId: this.sessionId,
      };

      localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }

  /**
   * Update last activity timestamp
   */
  private updateLastActivity(): void {
    this.saveSession();
    this.resetActivityTimer();
  }

  /**
   * Set up activity tracking
   */
  private setupActivityTracking(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const activityHandler = () => {
      this.updateLastActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, activityHandler, { passive: true });
    });

    // Initial activity timer
    this.resetActivityTimer();
  }

  /**
   * Reset activity timer
   */
  private resetActivityTimer(): void {
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
    }

    this.activityTimer = setTimeout(() => {
      this.handleInactivity();
    }, ACTIVITY_TIMEOUT);
  }

  /**
   * Handle user inactivity
   */
  private handleInactivity(): void {
    console.log('User inactive, clearing session');
    this.clearSession();
    
    // Logout user
    const { logout } = useAuthStore.getState();
    logout();
  }

  /**
   * Set up cross-tab synchronization
   */
  private setupCrossTabSync(): void {
    this.storageListener = (e: StorageEvent) => {
      if (e.key === SESSION_KEY) {
        if (e.newValue === null) {
          // Session was cleared in another tab
          this.handleSessionCleared();
        } else {
          // Session was updated in another tab
          this.handleSessionUpdated();
        }
      }
    };

    window.addEventListener('storage', this.storageListener);
  }

  /**
   * Handle session cleared in another tab
   */
  private handleSessionCleared(): void {
    console.log('Session cleared in another tab');
    jwtService.clearTokens();
    
    const { logout } = useAuthStore.getState();
    logout();
  }

  /**
   * Handle session updated in another tab
   */
  private handleSessionUpdated(): void {
    // Reload session data
    this.loadSession();
  }

  /**
   * Set up beforeunload handler
   */
  private setupBeforeUnload(): void {
    window.addEventListener('beforeunload', () => {
      this.updateLastActivity();
    });
  }

  /**
   * Clear session
   */
  clearSession(): void {
    try {
      localStorage.removeItem(SESSION_KEY);
      
      if (this.activityTimer) {
        clearTimeout(this.activityTimer);
        this.activityTimer = null;
      }
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  }

  /**
   * Get current session ID
   */
  getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Check if session is active
   */
  isSessionActive(): boolean {
    try {
      const sessionData = localStorage.getItem(SESSION_KEY);
      if (!sessionData) return false;

      const session: SessionData = JSON.parse(sessionData);
      return this.isSessionValid(session);
    } catch (error) {
      return false;
    }
  }

  /**
   * Extend session (reset timers)
   */
  extendSession(): void {
    this.updateLastActivity();
  }

  /**
   * Get session info
   */
  getSessionInfo(): {
    sessionId: string;
    lastActivity: number;
    timeUntilExpiry: number;
    isActive: boolean;
  } | null {
    try {
      const sessionData = localStorage.getItem(SESSION_KEY);
      if (!sessionData) return null;

      const session: SessionData = JSON.parse(sessionData);
      const now = Date.now();
      const timeUntilExpiry = ACTIVITY_TIMEOUT - (now - session.lastActivity);

      return {
        sessionId: session.sessionId,
        lastActivity: session.lastActivity,
        timeUntilExpiry: Math.max(0, timeUntilExpiry),
        isActive: this.isSessionValid(session),
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Cleanup session manager
   */
  cleanup(): void {
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
      this.activityTimer = null;
    }

    if (this.storageListener) {
      window.removeEventListener('storage', this.storageListener);
      this.storageListener = null;
    }
  }
}

// Export singleton instance
export const sessionManager = new SessionManager();

/**
 * Hook to use session manager
 */
export const useSession = () => {
  return {
    sessionId: sessionManager.getSessionId(),
    isActive: sessionManager.isSessionActive(),
    extend: () => sessionManager.extendSession(),
    clear: () => sessionManager.clearSession(),
    getInfo: () => sessionManager.getSessionInfo(),
  };
};

/**
 * Initialize session persistence
 */
export const initializeSessionPersistence = () => {
  // Session manager is initialized automatically
  console.log('Session persistence initialized');
};

/**
 * Cleanup session persistence
 */
export const cleanupSessionPersistence = () => {
  sessionManager.cleanup();
};