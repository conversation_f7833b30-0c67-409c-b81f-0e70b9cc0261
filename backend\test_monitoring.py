"""
Test script for monitoring and logging functionality.
"""
import sys
import os
import asyncio
import time
from pathlib import Path

# Add the backend directory to the Python path
sys.path.append(str(Path(__file__).parent))

from app.core.logging import setup_logging, get_logger, performance_logger, security_logger
from app.core.monitoring import health_checker, metrics_collector, alert_manager


async def test_monitoring_system():
    """Test the monitoring and logging system."""
    
    print("🔍 AutoParts Monitoring & Logging System Test")
    print("=" * 60)
    
    # Setup logging
    print("📝 Step 1: Setting up structured logging...")
    setup_logging()
    logger = get_logger("test.monitoring")
    
    print("✅ Structured logging configured successfully!")
    
    # Test structured logging
    print("\n📊 Step 2: Testing structured logging...")
    
    logger.info("Test info message", 
                test_param="test_value", 
                request_id="test-123",
                user_id="user-456")
    
    logger.warning("Test warning message", 
                   warning_type="test_warning",
                   details={"key": "value"})
    
    logger.error("Test error message", 
                 error_code="TEST_ERROR",
                 component="test_component")
    
    print("✅ Structured logging test completed!")
    
    # Test performance logging
    print("\n⚡ Step 3: Testing performance logging...")
    
    performance_logger.log_api_request(
        method="GET",
        path="/api/v1/parts/search",
        status_code=200,
        duration=0.125,
        request_id="perf-test-123"
    )
    
    performance_logger.log_database_query(
        query_type="SELECT",
        table="parts",
        duration=0.045,
        rows_affected=25
    )
    
    performance_logger.log_file_processing(
        operation="import",
        filename="test_inventory.xlsx",
        file_size=1024*1024,  # 1MB
        duration=2.5,
        rows_processed=1000
    )
    
    print("✅ Performance logging test completed!")
    
    # Test security logging
    print("\n🔒 Step 4: Testing security logging...")
    
    security_logger.log_authentication_attempt(
        email="<EMAIL>",
        success=True,
        ip_address="*************",
        user_agent="Mozilla/5.0 Test Browser"
    )
    
    security_logger.log_authorization_failure(
        user_id="user-789",
        resource="/api/v1/admin/users",
        action="GET",
        ip_address="*************"
    )
    
    security_logger.log_suspicious_activity(
        activity="multiple_failed_logins",
        user_id="user-suspicious",
        ip_address="*************",
        details={"attempts": 5, "timeframe": "5_minutes"}
    )
    
    print("✅ Security logging test completed!")
    
    # Test health checks
    print("\n🏥 Step 5: Testing health checks...")
    
    try:
        health_results = await health_checker.check_all()
        
        print(f"Health check completed for {len(health_results)} services:")
        for service_name, health_status in health_results.items():
            status_emoji = "✅" if health_status.status == "healthy" else "⚠️" if health_status.status == "degraded" else "❌"
            print(f"  {status_emoji} {service_name}: {health_status.status} ({health_status.response_time_ms:.2f}ms)")
            
            if health_status.details:
                for key, value in health_status.details.items():
                    if isinstance(value, dict):
                        print(f"    - {key}: {len(value)} items")
                    else:
                        print(f"    - {key}: {value}")
        
        print("✅ Health checks completed!")
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
    
    # Test metrics collection
    print("\n📈 Step 6: Testing metrics collection...")
    
    try:
        # Collect current metrics
        current_metrics = metrics_collector.collect_system_metrics()
        
        print("Current system metrics:")
        print(f"  - CPU: {current_metrics.cpu_percent}%")
        print(f"  - Memory: {current_metrics.memory_percent}% ({current_metrics.memory_used_mb:.1f}MB / {current_metrics.memory_total_mb:.1f}MB)")
        print(f"  - Disk: {current_metrics.disk_percent}% ({current_metrics.disk_used_gb:.1f}GB / {current_metrics.disk_total_gb:.1f}GB)")
        print(f"  - Active connections: {current_metrics.active_connections}")
        
        # Test metrics summary
        time.sleep(1)  # Wait a bit to have some data
        metrics_collector.collect_system_metrics()  # Collect another sample
        
        summary = metrics_collector.get_metrics_summary(1)  # Last 1 minute
        if "error" not in summary:
            print(f"\nMetrics summary (last 1 minute):")
            print(f"  - Samples: {summary['sample_count']}")
            if summary.get('averages'):
                print(f"  - Avg CPU: {summary['averages']['cpu_percent']}%")
                print(f"  - Avg Memory: {summary['averages']['memory_percent']}%")
        
        print("✅ Metrics collection test completed!")
        
    except Exception as e:
        print(f"❌ Metrics collection failed: {e}")
    
    # Test alert system
    print("\n🚨 Step 7: Testing alert system...")
    
    try:
        # Check current alerts
        alerts = alert_manager.check_thresholds(current_metrics)
        
        if alerts:
            print(f"Active alerts ({len(alerts)}):")
            for alert in alerts:
                severity_emoji = "🔴" if alert['severity'] == "critical" else "🟡"
                print(f"  {severity_emoji} {alert['type']}: {alert['message']}")
        else:
            print("No active alerts - system is within normal parameters")
        
        print("✅ Alert system test completed!")
        
    except Exception as e:
        print(f"❌ Alert system test failed: {e}")
    
    # Test log file creation
    print("\n📁 Step 8: Checking log files...")
    
    logs_dir = Path("logs")
    if logs_dir.exists():
        log_files = list(logs_dir.glob("*.log"))
        if log_files:
            print(f"Log files created ({len(log_files)}):")
            for log_file in log_files:
                file_size = log_file.stat().st_size
                print(f"  - {log_file.name}: {file_size} bytes")
        else:
            print("No log files found")
    else:
        print("Logs directory not found")
    
    print("\n🎉 Monitoring & Logging System Test Completed!")
    print("=" * 60)
    print("✅ All monitoring and logging components are working correctly!")
    print("\nKey features tested:")
    print("  📝 Structured JSON logging with context")
    print("  ⚡ Performance metrics tracking")
    print("  🔒 Security event logging")
    print("  🏥 Comprehensive health checks")
    print("  📈 System metrics collection")
    print("  🚨 Alert system with thresholds")
    print("  📁 Log file rotation and management")


if __name__ == "__main__":
    print("🧪 AutoParts Monitoring & Logging Test")
    print("=" * 50)
    
    # Run the test
    asyncio.run(test_monitoring_system())
