"""
Structured logging configuration for AutoParts API.
"""
import json
import logging
import logging.config
import sys
import traceback
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import uuid4

from app.core.config import settings


class StructuredFormatter(logging.Formatter):
    """
    Custom formatter for structured JSON logging.
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as structured JSON.
        
        Args:
            record: Log record to format
            
        Returns:
            JSON formatted log string
        """
        # Base log structure
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process,
        }
        
        # Add request ID if available
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        
        # Add user ID if available
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        
        # Add extra fields
        if hasattr(record, 'extra'):
            log_entry["extra"] = record.extra
        
        # Add exception information
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add stack trace for errors
        if record.levelno >= logging.ERROR and not record.exc_info:
            log_entry["stack_trace"] = traceback.format_stack()
        
        return json.dumps(log_entry, ensure_ascii=False, default=str)


class RequestContextFilter(logging.Filter):
    """
    Filter to add request context to log records.
    """
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Add request context to log record.
        
        Args:
            record: Log record to filter
            
        Returns:
            True to include the record
        """
        # Add default request ID if not present
        if not hasattr(record, 'request_id'):
            record.request_id = getattr(self, '_request_id', None)
        
        # Add default user ID if not present
        if not hasattr(record, 'user_id'):
            record.user_id = getattr(self, '_user_id', None)
        
        return True
    
    def set_request_context(self, request_id: str, user_id: Optional[str] = None):
        """Set request context for logging."""
        self._request_id = request_id
        self._user_id = user_id


# Global request context filter
request_context_filter = RequestContextFilter()


def setup_logging():
    """
    Setup structured logging configuration.
    """
    # Determine log level
    log_level = "DEBUG" if settings.DEBUG else "INFO"
    
    # Logging configuration
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "structured": {
                "()": StructuredFormatter,
            },
            "simple": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "filters": {
            "request_context": {
                "()": RequestContextFilter,
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": log_level,
                "formatter": "structured" if not settings.DEBUG else "simple",
                "filters": ["request_context"],
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "structured",
                "filters": ["request_context"],
                "filename": "logs/autoparts.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "structured",
                "filters": ["request_context"],
                "filename": "logs/autoparts_errors.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8",
            },
        },
        "loggers": {
            "app": {
                "level": log_level,
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "alembic": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
        },
        "root": {
            "level": log_level,
            "handlers": ["console"],
        },
    }
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs("logs", exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)


class StructuredLogger:
    """
    Wrapper for structured logging with additional context.
    """
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def _log_with_context(
        self,
        level: int,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ):
        """Log with additional context."""
        record_extra = {}
        
        if extra:
            record_extra.update(extra)
        
        if kwargs:
            record_extra.update(kwargs)
        
        # Create log record with extra context
        self.logger.log(
            level,
            message,
            extra={
                "extra": record_extra,
                "request_id": request_id,
                "user_id": user_id,
            }
        )
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self._log_with_context(logging.CRITICAL, message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        self.logger.exception(message, extra={"extra": kwargs})


def get_logger(name: str) -> StructuredLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        StructuredLogger instance
    """
    return StructuredLogger(name)


# Performance logging utilities
class PerformanceLogger:
    """
    Logger for performance metrics and timing.
    """
    
    def __init__(self):
        self.logger = get_logger("app.performance")
    
    def log_api_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        request_id: str,
        user_id: Optional[str] = None,
        **extra
    ):
        """Log API request performance."""
        self.logger.info(
            f"API Request: {method} {path}",
            method=method,
            path=path,
            status_code=status_code,
            duration_ms=round(duration * 1000, 2),
            request_id=request_id,
            user_id=user_id,
            **extra
        )
    
    def log_database_query(
        self,
        query_type: str,
        table: str,
        duration: float,
        rows_affected: Optional[int] = None,
        **extra
    ):
        """Log database query performance."""
        self.logger.info(
            f"Database Query: {query_type} on {table}",
            query_type=query_type,
            table=table,
            duration_ms=round(duration * 1000, 2),
            rows_affected=rows_affected,
            **extra
        )
    
    def log_file_processing(
        self,
        operation: str,
        filename: str,
        file_size: int,
        duration: float,
        rows_processed: Optional[int] = None,
        **extra
    ):
        """Log file processing performance."""
        self.logger.info(
            f"File Processing: {operation} - {filename}",
            operation=operation,
            filename=filename,
            file_size_mb=round(file_size / 1024 / 1024, 2),
            duration_seconds=round(duration, 2),
            rows_processed=rows_processed,
            **extra
        )
    
    def log_cache_operation(
        self,
        operation: str,
        key: str,
        hit: bool,
        duration: float,
        **extra
    ):
        """Log cache operation performance."""
        self.logger.debug(
            f"Cache {operation}: {'HIT' if hit else 'MISS'} - {key}",
            operation=operation,
            cache_key=key,
            cache_hit=hit,
            duration_ms=round(duration * 1000, 2),
            **extra
        )


# Global performance logger
performance_logger = PerformanceLogger()


# Security logging utilities
class SecurityLogger:
    """
    Logger for security events and audit trails.
    """
    
    def __init__(self):
        self.logger = get_logger("app.security")
    
    def log_authentication_attempt(
        self,
        email: str,
        success: bool,
        ip_address: str,
        user_agent: str,
        **extra
    ):
        """Log authentication attempt."""
        self.logger.info(
            f"Authentication {'SUCCESS' if success else 'FAILED'}: {email}",
            email=email,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            **extra
        )
    
    def log_authorization_failure(
        self,
        user_id: str,
        resource: str,
        action: str,
        ip_address: str,
        **extra
    ):
        """Log authorization failure."""
        self.logger.warning(
            f"Authorization DENIED: User {user_id} attempted {action} on {resource}",
            user_id=user_id,
            resource=resource,
            action=action,
            ip_address=ip_address,
            **extra
        )
    
    def log_suspicious_activity(
        self,
        activity: str,
        user_id: Optional[str],
        ip_address: str,
        details: Dict[str, Any],
        **extra
    ):
        """Log suspicious activity."""
        self.logger.warning(
            f"Suspicious Activity: {activity}",
            activity=activity,
            user_id=user_id,
            ip_address=ip_address,
            details=details,
            **extra
        )


# Global security logger
security_logger = SecurityLogger()
