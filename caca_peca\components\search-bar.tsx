"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { X, Filter, Barcode, Search, Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
// import { usePartSuggestions, usePartBrands, useDealershipStates } from "@/lib/hooks/useApi"
// import { useDebounce } from "@/hooks/use-debounce"

// Brazilian states
const BRAZILIAN_STATES = [
  { value: "AC", label: "Acre" },
  { value: "AL", label: "Alagoas" },
  { value: "AP", label: "Amapá" },
  { value: "AM", label: "Amazonas" },
  { value: "BA", label: "Bahia" },
  { value: "CE", label: "Cear<PERSON>" },
  { value: "DF", label: "Distrito Federal" },
  { value: "ES", label: "Espírito Santo" },
  { value: "GO", label: "Goiás" },
  { value: "MA", label: "Maranhão" },
  { value: "MT", label: "Mato Grosso" },
  { value: "MS", label: "Mato Grosso do Sul" },
  { value: "MG", label: "Minas Gerais" },
  { value: "PA", label: "Pará" },
  { value: "PB", label: "Paraíba" },
  { value: "PR", label: "Paraná" },
  { value: "PE", label: "Pernambuco" },
  { value: "PI", label: "Piauí" },
  { value: "RJ", label: "Rio de Janeiro" },
  { value: "RN", label: "Rio Grande do Norte" },
  { value: "RS", label: "Rio Grande do Sul" },
  { value: "RO", label: "Rondônia" },
  { value: "RR", label: "Roraima" },
  { value: "SC", label: "Santa Catarina" },
  { value: "SP", label: "São Paulo" },
  { value: "SE", label: "Sergipe" },
  { value: "TO", label: "Tocantins" },
]

// Common automotive brands
const AUTO_BRANDS = [
  { value: "toyota", label: "Toyota" },
  { value: "volkswagen", label: "Volkswagen" },
  { value: "ford", label: "Ford" },
  { value: "chevrolet", label: "Chevrolet" },
  { value: "honda", label: "Honda" },
  { value: "hyundai", label: "Hyundai" },
  { value: "nissan", label: "Nissan" },
  { value: "fiat", label: "Fiat" },
  { value: "bmw", label: "BMW" },
  { value: "mercedes", label: "Mercedes-Benz" },
  { value: "audi", label: "Audi" },
  { value: "kia", label: "Kia" },
  { value: "mazda", label: "Mazda" },
  { value: "renault", label: "Renault" },
  { value: "peugeot", label: "Peugeot" },
  { value: "citroen", label: "Citroën" },
  { value: "jeep", label: "Jeep" },
  { value: "subaru", label: "Subaru" },
  { value: "mitsubishi", label: "Mitsubishi" },
  { value: "volvo", label: "Volvo" },
]

export function SearchBar() {
  const [query, setQuery] = useState("")
  const [isFocused, setIsFocused] = useState(false)
  const [selectedState, setSelectedState] = useState<string>("all")
  const [selectedBrand, setSelectedBrand] = useState<string>("all")
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const router = useRouter()

  // Debounce search query for suggestions
  // const debouncedQuery = useDebounce(query, 300)

  // API hooks - temporarily disabled for build
  // const { data: suggestions, isLoading: suggestionsLoading } = usePartSuggestions(debouncedQuery)
  // const { data: brandsResponse } = usePartBrands()
  // const { data: statesResponse } = useDealershipStates()

  // Use static data for now
  const brands = AUTO_BRANDS
  const states = BRAZILIAN_STATES
  const suggestionsList: string[] = []
  const suggestionsLoading = false

  const handleSearch = (e?: React.FormEvent, searchQuery?: string) => {
    e?.preventDefault()

    const searchTerm = searchQuery || query.trim()
    if (!searchTerm) return

    // Build query parameters
    const params = new URLSearchParams()
    params.append("q", searchTerm)

    if (selectedState && selectedState !== "all") {
      params.append("state", selectedState)
    }

    if (selectedBrand && selectedBrand !== "all") {
      params.append("brand", selectedBrand)
    }

    // Hide suggestions and navigate
    setShowSuggestions(false)
    router.push(`/busca?${params.toString()}`)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion)
    handleSearch(undefined, suggestion)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setShowSuggestions(value.length >= 2)
  }

  const handleInputFocus = () => {
    setIsFocused(true)
    if (query.length >= 2) {
      setShowSuggestions(true)
    }
  }

  const handleInputBlur = () => {
    setIsFocused(false)
    // Delay hiding suggestions to allow clicking
    setTimeout(() => setShowSuggestions(false), 200)
  }

  const clearFilters = () => {
    setSelectedState("all")
    setSelectedBrand("all")
  }

  const hasFilters = selectedState !== "all" || selectedBrand !== "all"

  return (
    <div className="w-full">
      <form
        onSubmit={handleSearch}
        className={`search-animation relative flex w-full rounded-full border bg-background shadow-sm ${
          isFocused ? "ring-2 ring-primary" : ""
        }`}
      >
        <Input
          type="text"
          placeholder="Digite o código da peça genuína..."
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          className="h-14 flex-1 rounded-l-full border-0 bg-transparent px-6 text-base focus-visible:ring-0 focus-visible:ring-offset-0"
        />

        {query && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-[5.5rem] top-1/2 -translate-y-1/2 md:right-[8.5rem]"
            onClick={() => setQuery("")}
          >
            <X className="h-5 w-5 text-muted-foreground" />
            <span className="sr-only">Limpar busca</span>
          </Button>
        )}

        <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <PopoverTrigger asChild>
            <Button type="button" variant="ghost" className="relative h-14 px-3 md:px-4">
              <Filter className="h-5 w-5" />
              <span className="sr-only md:not-sr-only md:ml-2">Filtros</span>
              {hasFilters && (
                <Badge className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-[10px]">
                  {(selectedState !== "all" ? 1 : 0) + (selectedBrand !== "all" ? 1 : 0)}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="grid gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Filtros de busca</h4>
                <p className="text-sm text-muted-foreground">Refine sua busca por estado e marca</p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="state">Estado (UF)</Label>
                <Select value={selectedState} onValueChange={setSelectedState}>
                  <SelectTrigger id="state">
                    <SelectValue placeholder="Selecione um estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os estados</SelectItem>
                    {states.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="brand">Marca</Label>
                <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                  <SelectTrigger id="brand">
                    <SelectValue placeholder="Selecione uma marca" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as marcas</SelectItem>
                    {brands.map((brand) => (
                      <SelectItem key={brand.value} value={brand.value}>
                        {brand.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-between">
                <Button type="button" variant="outline" size="sm" onClick={clearFilters} disabled={!hasFilters}>
                  Limpar filtros
                </Button>
                <Button type="button" size="sm" onClick={() => setIsFilterOpen(false)}>
                  Aplicar filtros
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Button type="submit" className="h-14 rounded-r-full px-6 text-base">
          <Barcode className="mr-2 h-5 w-5" />
          Buscar
        </Button>
      </form>

      {selectedState !== "all" && (
        <Badge variant="secondary" className="mt-2 mr-2 inline-flex items-center gap-1">
          Estado: {BRAZILIAN_STATES.find((s) => s.value === selectedState)?.label || selectedState}
          <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={() => setSelectedState("all")}>
            <X className="h-3 w-3" />
            <span className="sr-only">Remover filtro de estado</span>
          </Button>
        </Badge>
      )}

      {selectedBrand !== "all" && (
        <Badge variant="secondary" className="mt-2 inline-flex items-center gap-1">
          Marca: {brands.find((b) => b.value === selectedBrand)?.label || selectedBrand}
          <Button variant="ghost" size="icon" className="h-4 w-4 p-0" onClick={() => setSelectedBrand("all")}>
            <X className="h-3 w-3" />
            <span className="sr-only">Remover filtro de marca</span>
          </Button>
        </Badge>
      )}

      {/* Search Suggestions */}
      {showSuggestions && suggestionsList.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {suggestionsLoading && (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">Buscando sugestões...</span>
            </div>
          )}
          {!suggestionsLoading && suggestionsList.map((suggestion, index) => (
            <button
              key={index}
              className="w-full text-left px-4 py-3 hover:bg-accent border-b border-border last:border-b-0 flex items-center gap-3"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <Search className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{suggestion}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

