"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { MoreHorizontal, Search, Download, Mail, CheckCircle, XCircle } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Dados de exemplo para usuários
const mockUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(11) 98765-4321",
    status: "active",
    createdAt: "2023-01-15T10:30:00",
    lastLogin: "2023-05-10T14:22:00",
    emailVerified: true,
    newsletterSubscribed: true,
    promotionsSubscribed: true,
  },
  {
    id: 2,
    name: "Maria Oliveira",
    email: "<EMAIL>",
    phone: "(21) 97654-3210",
    status: "active",
    createdAt: "2023-02-20T09:15:00",
    lastLogin: "2023-05-09T11:45:00",
    emailVerified: true,
    newsletterSubscribed: true,
    promotionsSubscribed: false,
  },
  {
    id: 3,
    name: "Pedro Santos",
    email: "<EMAIL>",
    phone: "(31) 96543-2109",
    status: "inactive",
    createdAt: "2023-03-05T14:20:00",
    lastLogin: "2023-04-20T16:30:00",
    emailVerified: false,
    newsletterSubscribed: false,
    promotionsSubscribed: false,
  },
  {
    id: 4,
    name: "Ana Souza",
    email: "<EMAIL>",
    phone: "(41) 95432-1098",
    status: "active",
    createdAt: "2023-03-10T11:45:00",
    lastLogin: "2023-05-11T09:15:00",
    emailVerified: true,
    newsletterSubscribed: true,
    promotionsSubscribed: true,
  },
  {
    id: 5,
    name: "Carlos Ferreira",
    email: "<EMAIL>",
    phone: "(51) 94321-0987",
    status: "blocked",
    createdAt: "2023-03-15T16:30:00",
    lastLogin: "2023-04-15T10:30:00",
    emailVerified: true,
    newsletterSubscribed: false,
    promotionsSubscribed: false,
  },
  {
    id: 6,
    name: "Fernanda Lima",
    email: "<EMAIL>",
    phone: "(61) 93210-9876",
    status: "active",
    createdAt: "2023-03-20T13:15:00",
    lastLogin: "2023-05-08T15:45:00",
    emailVerified: true,
    newsletterSubscribed: true,
    promotionsSubscribed: true,
  },
  {
    id: 7,
    name: "Ricardo Alves",
    email: "<EMAIL>",
    phone: "(71) 92109-8765",
    status: "inactive",
    createdAt: "2023-03-25T08:45:00",
    lastLogin: "2023-04-10T12:00:00",
    emailVerified: false,
    newsletterSubscribed: true,
    promotionsSubscribed: false,
  },
  {
    id: 8,
    name: "Juliana Costa",
    email: "<EMAIL>",
    phone: "(81) 91098-7654",
    status: "active",
    createdAt: "2023-04-01T15:00:00",
    lastLogin: "2023-05-07T14:30:00",
    emailVerified: true,
    newsletterSubscribed: true,
    promotionsSubscribed: true,
  },
  {
    id: 9,
    name: "Marcos Ribeiro",
    email: "<EMAIL>",
    phone: "(91) 90987-6543",
    status: "active",
    createdAt: "2023-04-05T10:15:00",
    lastLogin: "2023-05-06T11:00:00",
    emailVerified: true,
    newsletterSubscribed: false,
    promotionsSubscribed: true,
  },
  {
    id: 10,
    name: "Patrícia Gomes",
    email: "<EMAIL>",
    phone: "(12) 98765-4321",
    status: "blocked",
    createdAt: "2023-04-10T09:30:00",
    lastLogin: "2023-04-25T16:15:00",
    emailVerified: true,
    newsletterSubscribed: true,
    promotionsSubscribed: false,
  },
]

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [subscriptionFilter, setSubscriptionFilter] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [isUserDetailsOpen, setIsUserDetailsOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isSendEmailDialogOpen, setIsSendEmailDialogOpen] = useState(false)
  const [emailSubject, setEmailSubject] = useState("")
  const [emailContent, setEmailContent] = useState("")
  const [activeMenu, setActiveMenu] = useState<string | null>(null)

  // Limpar qualquer menu ativo quando o componente é desmontado
  useEffect(() => {
    return () => {
      const menu = document.getElementById("context-menu")
      if (menu && document.body.contains(menu)) {
        document.body.removeChild(menu)
      }
    }
  }, [])

  // Função para remover qualquer menu existente
  const removeExistingMenu = () => {
    const existingMenu = document.getElementById("context-menu")
    if (existingMenu && document.body.contains(existingMenu)) {
      document.body.removeChild(existingMenu)
    }

    // Remover todos os event listeners antigos
    document.removeEventListener("click", handleDocumentClick)
    setActiveMenu(null)
  }

  // Manipulador de clique global para fechar o menu
  const handleDocumentClick = (e: MouseEvent) => {
    const menuElement = document.getElementById("context-menu")
    if (menuElement && !menuElement.contains(e.target as Node)) {
      removeExistingMenu()
    }
  }

  // Função auxiliar para criar um menu de contexto
  const createContextMenu = (user: any, x: number, y: number) => {
    // Primeiro, remover qualquer menu existente
    removeExistingMenu()

    // Criar novo menu
    const menu = document.createElement("div")
    menu.id = "context-menu"
    menu.className =
      "fixed rounded-md border border-gray-200 bg-white p-1 shadow-lg z-[100] dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100"
    menu.style.left = `${x}px`
    menu.style.top = `${y}px`

    menu.innerHTML = `
      <div class="px-2 py-1.5 text-sm font-semibold">Ações</div>
      <div class="-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700"></div>
      <button id="view-user" class="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">
        <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
        Ver detalhes
      </button>
      <button id="edit-user" class="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">
        <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"/></svg>
        Editar
      </button>
      <button id="send-email" class="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">
        <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
        Enviar email
      </button>
      <div class="-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700"></div>
      <button id="delete-user" class="flex w-full items-center rounded-sm px-2 py-1.5 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-red-400">
        <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
        Excluir
      </button>
    `

    document.body.appendChild(menu)
    setActiveMenu(user.id.toString())

    // Adicionar event listeners
    document.getElementById("view-user")?.addEventListener("click", () => {
      handleViewUser(user)
      removeExistingMenu()
    })

    document.getElementById("edit-user")?.addEventListener("click", () => {
      handleEditUser(user)
      removeExistingMenu()
    })

    document.getElementById("send-email")?.addEventListener("click", () => {
      handleSendEmail(user)
      removeExistingMenu()
    })

    document.getElementById("delete-user")?.addEventListener("click", () => {
      handleDeleteUser(user)
      removeExistingMenu()
    })

    // Adicionar event listener global com um pequeno atraso
    setTimeout(() => {
      document.addEventListener("click", handleDocumentClick)
    }, 100)

    return menu
  }

  const itemsPerPage = 8

  // Filtragem de usuários
  const filteredUsers = mockUsers.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.phone.includes(searchTerm)

    const matchesStatus = statusFilter === "all" || user.status === statusFilter

    const matchesSubscription =
      subscriptionFilter === "all" ||
      (subscriptionFilter === "newsletter" && user.newsletterSubscribed) ||
      (subscriptionFilter === "promotions" && user.promotionsSubscribed) ||
      (subscriptionFilter === "both" && user.newsletterSubscribed && user.promotionsSubscribed) ||
      (subscriptionFilter === "none" && !user.newsletterSubscribed && !user.promotionsSubscribed)

    return matchesSearch && matchesStatus && matchesSubscription
  })

  // Paginação
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)
  const paginatedUsers = filteredUsers.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // Formatação de data relativa
  const formatRelativeDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return "agora mesmo"
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes} ${minutes === 1 ? "minuto" : "minutos"} atrás`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours} ${hours === 1 ? "hora" : "horas"} atrás`
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days} ${days === 1 ? "dia" : "dias"} atrás`
    } else if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000)
      return `${months} ${months === 1 ? "mês" : "meses"} atrás`
    } else {
      const years = Math.floor(diffInSeconds / 31536000)
      return `${years} ${years === 1 ? "ano" : "anos"} atrás`
    }
  }

  // Formatação de data completa
  const formatFullDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Renderização do status do usuário
  const renderUserStatus = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 hover:bg-green-50 dark:bg-green-900/20 dark:text-green-400"
          >
            Ativo
          </Badge>
        )
      case "inactive":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400"
          >
            Inativo
          </Badge>
        )
      case "blocked":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 hover:bg-red-50 dark:bg-red-900/20 dark:text-red-400"
          >
            Bloqueado
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Manipuladores de eventos
  const handleViewUser = (user: any) => {
    setSelectedUser(user)
    setIsUserDetailsOpen(true)
  }

  const handleEditUser = (user: any) => {
    setSelectedUser(user)
    setIsEditDialogOpen(true)
  }

  const handleDeleteUser = (user: any) => {
    setSelectedUser(user)
    setIsDeleteDialogOpen(true)
  }

  const handleSendEmail = (user: any) => {
    setSelectedUser(user)
    setIsSendEmailDialogOpen(true)
  }

  const confirmDeleteUser = () => {
    // Aqui você implementaria a lógica real de exclusão
    console.log(`Usuário ${selectedUser.id} excluído`)
    setIsDeleteDialogOpen(false)
  }

  const confirmEditUser = () => {
    // Aqui você implementaria a lógica real de edição
    console.log(`Usuário ${selectedUser.id} editado`)
    setIsEditDialogOpen(false)
  }

  const confirmSendEmail = () => {
    // Aqui você implementaria a lógica real de envio de email
    console.log(`Email enviado para ${selectedUser.email}`)
    console.log(`Assunto: ${emailSubject}`)
    console.log(`Conteúdo: ${emailContent}`)
    setIsSendEmailDialogOpen(false)
    setEmailSubject("")
    setEmailContent("")
  }

  // Limpar o menu quando um modal é aberto ou fechado
  useEffect(() => {
    removeExistingMenu()
  }, [isUserDetailsOpen, isEditDialogOpen, isDeleteDialogOpen, isSendEmailDialogOpen])

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestão de Usuários</h1>
          <p className="text-muted-foreground">Gerencie os usuários da plataforma</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Exportar
          </Button>
          <Button>
            <Mail className="mr-2 h-4 w-4" />
            Enviar Email em Massa
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usuários</CardTitle>
          <CardDescription>
            Lista de usuários cadastrados na plataforma para receber notícias e promoções.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar por nome, email ou telefone..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  <SelectItem value="active">Ativos</SelectItem>
                  <SelectItem value="inactive">Inativos</SelectItem>
                  <SelectItem value="blocked">Bloqueados</SelectItem>
                </SelectContent>
              </Select>

              <Select value={subscriptionFilter} onValueChange={setSubscriptionFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filtrar por inscrição" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as inscrições</SelectItem>
                  <SelectItem value="newsletter">Newsletter</SelectItem>
                  <SelectItem value="promotions">Promoções</SelectItem>
                  <SelectItem value="both">Ambos</SelectItem>
                  <SelectItem value="none">Nenhum</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Inscrições</TableHead>
                  <TableHead>Último Login</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedUsers.length > 0 ? (
                  paginatedUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{renderUserStatus(user.status)}</TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center gap-1">
                            {user.newsletterSubscribed ? (
                              <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                            ) : (
                              <XCircle className="h-3.5 w-3.5 text-red-500" />
                            )}
                            <span className="text-xs">Newsletter</span>
                          </div>
                          <div className="flex items-center gap-1">
                            {user.promotionsSubscribed ? (
                              <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                            ) : (
                              <XCircle className="h-3.5 w-3.5 text-red-500" />
                            )}
                            <span className="text-xs">Promoções</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatRelativeDate(user.lastLogin)}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="relative"
                          onClick={(e) => {
                            e.stopPropagation()
                            const rect = e.currentTarget.getBoundingClientRect()
                            createContextMenu(user, rect.right - 200, rect.bottom + 5)
                          }}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      Nenhum usuário encontrado.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Mostrando <strong>{paginatedUsers.length}</strong> de <strong>{filteredUsers.length}</strong> usuários
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink isActive={page === currentPage} onClick={() => setCurrentPage(page)}>
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Detalhes do Usuário */}
      <Dialog open={isUserDetailsOpen} onOpenChange={setIsUserDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes do Usuário</DialogTitle>
            <DialogDescription>Informações completas do usuário selecionado.</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="mb-2 text-sm font-medium text-muted-foreground">Informações Pessoais</h3>
                  <div className="space-y-2">
                    <div>
                      <Label className="text-xs">Nome</Label>
                      <p className="text-sm font-medium">{selectedUser.name}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Email</Label>
                      <p className="text-sm font-medium">{selectedUser.email}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Telefone</Label>
                      <p className="text-sm font-medium">{selectedUser.phone}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Status</Label>
                      <div className="mt-1">{renderUserStatus(selectedUser.status)}</div>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="mb-2 text-sm font-medium text-muted-foreground">Datas</h3>
                  <div className="space-y-2">
                    <div>
                      <Label className="text-xs">Data de Cadastro</Label>
                      <p className="text-sm font-medium">{formatFullDate(selectedUser.createdAt)}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Último Login</Label>
                      <p className="text-sm font-medium">{formatFullDate(selectedUser.lastLogin)}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="mb-2 text-sm font-medium text-muted-foreground">Preferências de Comunicação</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email-verified">Email Verificado</Label>
                    </div>
                    <div className="flex items-center">
                      {selectedUser.emailVerified ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="newsletter">Inscrição na Newsletter</Label>
                    </div>
                    <div className="flex items-center">
                      {selectedUser.newsletterSubscribed ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="promotions">Receber Promoções</Label>
                    </div>
                    <div className="flex items-center">
                      {selectedUser.promotionsSubscribed ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUserDetailsOpen(false)}>
              Fechar
            </Button>
            <Button
              onClick={() => {
                setIsUserDetailsOpen(false)
                handleEditUser(selectedUser)
              }}
            >
              Editar Usuário
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Edição de Usuário */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Usuário</DialogTitle>
            <DialogDescription>Altere as informações do usuário selecionado.</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">Nome</Label>
                  <Input id="edit-name" defaultValue={selectedUser.name} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-email">Email</Label>
                  <Input id="edit-email" defaultValue={selectedUser.email} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-phone">Telefone</Label>
                  <Input id="edit-phone" defaultValue={selectedUser.phone} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-status">Status</Label>
                  <Select defaultValue={selectedUser.status}>
                    <SelectTrigger id="edit-status">
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Ativo</SelectItem>
                      <SelectItem value="inactive">Inativo</SelectItem>
                      <SelectItem value="blocked">Bloqueado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Preferências de Comunicação</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="edit-newsletter">Inscrição na Newsletter</Label>
                    <Switch id="edit-newsletter" defaultChecked={selectedUser.newsletterSubscribed} />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="edit-promotions">Receber Promoções</Label>
                    <Switch id="edit-promotions" defaultChecked={selectedUser.promotionsSubscribed} />
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={confirmEditUser}>Salvar Alterações</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Exclusão de Usuário */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Excluir Usuário</DialogTitle>
            <DialogDescription>
              Esta ação não pode ser desfeita. Isso excluirá permanentemente o usuário e todos os dados associados.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <p>
                Você tem certeza que deseja excluir o usuário <strong>{selectedUser.name}</strong> ({selectedUser.email}
                )?
              </p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDeleteUser}>
              Excluir Usuário
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Envio de Email */}
      <Dialog open={isSendEmailDialogOpen} onOpenChange={setIsSendEmailDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Enviar Email</DialogTitle>
            <DialogDescription>Envie um email personalizado para o usuário selecionado.</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-to">Para</Label>
                <Input id="email-to" value={selectedUser.email} disabled />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email-subject">Assunto</Label>
                <Input
                  id="email-subject"
                  placeholder="Digite o assunto do email"
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email-content">Conteúdo</Label>
                <textarea
                  id="email-content"
                  className="h-32 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Digite o conteúdo do email"
                  value={emailContent}
                  onChange={(e) => setEmailContent(e.target.value)}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSendEmailDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={confirmSendEmail} disabled={!emailSubject.trim() || !emailContent.trim()}>
              Enviar Email
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

