"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ThemeToggleButtonProps {
  className?: string
  variant?: "icon" | "button" | "minimal"
}

export function ThemeToggleButton({ className, variant = "icon" }: ThemeToggleButtonProps) {
  const { setTheme, theme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Only render the toggle on the client to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return variant === "icon" ? <Button variant="ghost" size="icon" className={cn("w-10 h-10", className)} /> : null
  }

  const isDark = resolvedTheme === "dark"

  const toggleTheme = () => {
    console.log("Current theme:", theme)
    console.log("Resolved theme:", resolvedTheme)
    setTheme(isDark ? "light" : "dark")
    console.log("Setting theme to:", isDark ? "light" : "dark")
  }

  if (variant === "minimal") {
    return (
      <button
        onClick={toggleTheme}
        className={cn("p-2 rounded-md hover:bg-accent", className)}
        aria-label={isDark ? "Mudar para modo claro" : "Mudar para modo escuro"}
      >
        {isDark ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
      </button>
    )
  }

  if (variant === "button") {
    return (
      <Button variant="outline" className={cn("flex items-center gap-2", className)} onClick={toggleTheme}>
        {isDark ? (
          <>
            <Sun className="h-4 w-4" />
            <span>Modo Claro</span>
          </>
        ) : (
          <>
            <Moon className="h-4 w-4" />
            <span>Modo Escuro</span>
          </>
        )}
      </Button>
    )
  }

  // Default icon variant
  return (
    <Button
      variant="ghost"
      size="icon"
      className={cn("w-10 h-10 rounded-full", isDark ? "hover:bg-slate-800" : "hover:bg-slate-200", className)}
      onClick={toggleTheme}
      aria-label={isDark ? "Mudar para modo claro" : "Mudar para modo escuro"}
    >
      {isDark ? <Sun className="h-5 w-5 text-yellow-500" /> : <Moon className="h-5 w-5 text-slate-700" />}
    </Button>
  )
}

