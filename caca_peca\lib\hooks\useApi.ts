/**
 * React Query hooks for API calls
 * Provides caching, loading states, and error handling
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  partsService, 
  inventoryService, 
  dealershipsService,
  PartSearchRequest,
  InventorySearchParams,
  DealershipSearchParams,
  Part,
  Inventory,
  Dealership,
  InventoryCreate,
  InventoryUpdate,
  DealershipCreate,
  DealershipUpdate
} from '../api';

// Query keys
export const queryKeys = {
  parts: {
    all: ['parts'] as const,
    search: (params: PartSearchRequest) => ['parts', 'search', params] as const,
    detail: (id: string) => ['parts', 'detail', id] as const,
    byNumber: (partNumber: string) => ['parts', 'number', partNumber] as const,
    categories: ['parts', 'categories'] as const,
    brands: ['parts', 'brands'] as const,
    suggestions: (query: string) => ['parts', 'suggestions', query] as const,
  },
  inventory: {
    all: ['inventory'] as const,
    search: (params: InventorySearchParams) => ['inventory', 'search', params] as const,
    detail: (id: string) => ['inventory', 'detail', id] as const,
    my: (page: number, size: number) => ['inventory', 'my', page, size] as const,
    stats: (dealershipId?: string) => ['inventory', 'stats', dealershipId] as const,
    lowStock: (threshold: number) => ['inventory', 'low-stock', threshold] as const,
  },
  dealerships: {
    all: ['dealerships'] as const,
    search: (params: DealershipSearchParams) => ['dealerships', 'search', params] as const,
    detail: (id: string) => ['dealerships', 'detail', id] as const,
    profile: ['dealerships', 'profile'] as const,
    states: ['dealerships', 'states'] as const,
    cities: (state: string) => ['dealerships', 'cities', state] as const,
  },
};

// Parts hooks
export function usePartsSearch(params: PartSearchRequest) {
  return useQuery(
    queryKeys.parts.search(params),
    () => partsService.searchParts(params),
    {
      enabled: !!(params.query || params.part_number || params.brand),
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function usePart(partId: string) {
  return useQuery(
    queryKeys.parts.detail(partId),
    () => partsService.getPartById(partId),
    {
      enabled: !!partId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
}

export function usePartByNumber(partNumber: string) {
  return useQuery(
    queryKeys.parts.byNumber(partNumber),
    () => partsService.getPartByNumber(partNumber),
    {
      enabled: !!partNumber,
      staleTime: 10 * 60 * 1000,
    }
  );
}

export function usePartCategories() {
  return useQuery(
    queryKeys.parts.categories,
    () => partsService.getCategories(),
    {
      staleTime: 30 * 60 * 1000, // 30 minutes
    }
  );
}

export function usePartBrands() {
  return useQuery(
    queryKeys.parts.brands,
    () => partsService.getBrands(),
    {
      staleTime: 30 * 60 * 1000,
    }
  );
}

export function usePartSuggestions(query: string) {
  return useQuery(
    queryKeys.parts.suggestions(query),
    () => partsService.getPartSuggestions(query),
    {
      enabled: query.length >= 2,
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

// Inventory hooks
export function useInventorySearch(params: InventorySearchParams) {
  return useQuery(
    queryKeys.inventory.search(params),
    () => inventoryService.searchInventory(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

export function useInventory(inventoryId: string) {
  return useQuery(
    queryKeys.inventory.detail(inventoryId),
    () => inventoryService.getInventoryById(inventoryId),
    {
      enabled: !!inventoryId,
      staleTime: 5 * 60 * 1000,
    }
  );
}

export function useMyInventory(page = 1, size = 20) {
  return useQuery(
    queryKeys.inventory.my(page, size),
    () => inventoryService.getMyInventory(page, size),
    {
      staleTime: 2 * 60 * 1000,
    }
  );
}

export function useInventoryStats(dealershipId?: string) {
  return useQuery(
    queryKeys.inventory.stats(dealershipId),
    () => inventoryService.getInventoryStats(dealershipId),
    {
      staleTime: 5 * 60 * 1000,
    }
  );
}

export function useLowStockItems(threshold = 5) {
  return useQuery(
    queryKeys.inventory.lowStock(threshold),
    () => inventoryService.getLowStockItems(threshold),
    {
      staleTime: 5 * 60 * 1000,
    }
  );
}

// Inventory mutations
export function useCreateInventory() {
  const queryClient = useQueryClient();
  
  return useMutation(
    (data: InventoryCreate) => inventoryService.createInventory(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKeys.inventory.all);
      },
    }
  );
}

export function useUpdateInventory() {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ id, data }: { id: string; data: InventoryUpdate }) => 
      inventoryService.updateInventory(id, data),
    {
      onSuccess: (_, { id }) => {
        queryClient.invalidateQueries(queryKeys.inventory.detail(id));
        queryClient.invalidateQueries(queryKeys.inventory.all);
      },
    }
  );
}

export function useDeleteInventory() {
  const queryClient = useQueryClient();
  
  return useMutation(
    (id: string) => inventoryService.deleteInventory(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKeys.inventory.all);
      },
    }
  );
}

// Dealership hooks
export function useDealershipsSearch(params: DealershipSearchParams) {
  return useQuery(
    queryKeys.dealerships.search(params),
    () => dealershipsService.searchDealerships(params),
    {
      staleTime: 10 * 60 * 1000,
    }
  );
}

export function useDealership(dealershipId: string) {
  return useQuery(
    queryKeys.dealerships.detail(dealershipId),
    () => dealershipsService.getDealershipById(dealershipId),
    {
      enabled: !!dealershipId,
      staleTime: 10 * 60 * 1000,
    }
  );
}

export function useMyDealershipProfile() {
  return useQuery(
    queryKeys.dealerships.profile,
    () => dealershipsService.getMyProfile(),
    {
      staleTime: 10 * 60 * 1000,
    }
  );
}

export function useDealershipStates() {
  return useQuery(
    queryKeys.dealerships.states,
    () => dealershipsService.getStates(),
    {
      staleTime: 60 * 60 * 1000, // 1 hour
    }
  );
}

export function useDealershipCities(state: string) {
  return useQuery(
    queryKeys.dealerships.cities(state),
    () => dealershipsService.getCitiesByState(state),
    {
      enabled: !!state,
      staleTime: 30 * 60 * 1000,
    }
  );
}

// Dealership mutations
export function useCreateDealership() {
  const queryClient = useQueryClient();
  
  return useMutation(
    (data: DealershipCreate) => dealershipsService.createDealership(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKeys.dealerships.all);
      },
    }
  );
}

export function useUpdateDealership() {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ id, data }: { id: string; data: DealershipUpdate }) => 
      dealershipsService.updateDealership(id, data),
    {
      onSuccess: (_, { id }) => {
        queryClient.invalidateQueries(queryKeys.dealerships.detail(id));
        queryClient.invalidateQueries(queryKeys.dealerships.profile);
      },
    }
  );
}

export function useUpdateMyProfile() {
  const queryClient = useQueryClient();
  
  return useMutation(
    (data: DealershipUpdate) => dealershipsService.updateMyProfile(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKeys.dealerships.profile);
      },
    }
  );
}
