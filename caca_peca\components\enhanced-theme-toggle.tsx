"use client"

import * as React from "react"
import { <PERSON>, Sun, Monitor } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface EnhancedThemeToggleProps {
  className?: string
}

export function EnhancedThemeToggle({ className }: EnhancedThemeToggleProps) {
  const { setTheme, theme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)
  const [isRotating, setIsRotating] = React.useState(false)

  // Only render the toggle on the client to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)

    // Add class to prevent transitions on page load
    document.documentElement.classList.add("no-transitions")

    // Remove the class after a short delay
    const timer = setTimeout(() => {
      document.documentElement.classList.remove("no-transitions")
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  const handleThemeChange = (newTheme: string) => {
    setIsRotating(true)
    setTheme(newTheme)
    setTimeout(() => setIsRotating(false), 500)
  }

  if (!mounted) {
    return <Button variant="ghost" size="icon" className={cn("w-10 h-10", className)} />
  }

  const currentTheme = theme === "system" ? resolvedTheme : theme

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "w-10 h-10 rounded-full border-2",
            isRotating && "theme-toggle-rotate",
            currentTheme === "dark" ? "border-blue-400 bg-slate-800" : "border-yellow-400 bg-sky-50",
            className,
          )}
        >
          {currentTheme === "dark" ? (
            <Moon className="h-5 w-5 text-blue-300" />
          ) : (
            <Sun className="h-5 w-5 text-yellow-500" />
          )}
          <span className="sr-only">Alternar tema</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="animate-in fade-in-0 zoom-in-95">
        <DropdownMenuItem onClick={() => handleThemeChange("light")} className="flex items-center gap-2 cursor-pointer">
          <Sun className="h-4 w-4 text-yellow-500" />
          <span>Claro</span>
          {currentTheme === "light" && <span className="ml-auto text-xs">✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleThemeChange("dark")} className="flex items-center gap-2 cursor-pointer">
          <Moon className="h-4 w-4 text-blue-400" />
          <span>Escuro</span>
          {currentTheme === "dark" && <span className="ml-auto text-xs">✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleThemeChange("system")}
          className="flex items-center gap-2 cursor-pointer"
        >
          <Monitor className="h-4 w-4" />
          <span>Sistema</span>
          {theme === "system" && <span className="ml-auto text-xs">✓</span>}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

