"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  CreditCard, 
  FileText, 
  Settings, 
  TrendingUp,
  Bell,
  Loader2,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { useCurrentSubscription, useSubscriptionNotifications } from "@/lib/hooks/useSubscription"
import { SubscriptionCard } from "@/components/dealership/subscription-card"
import { PlanSelector } from "@/components/dealership/plan-selector"
import { BillingHistory } from "@/components/dealership/billing-history"
import { SubscriptionStatus } from "@/lib/types/api"

export default function SubscriptionPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [showPlanSelector, setShowPlanSelector] = useState(false)

  const { 
    data: subscriptionResponse, 
    isLoading: subscriptionLoading,
    error: subscriptionError 
  } = useCurrentSubscription()

  const { 
    data: notificationsResponse 
  } = useSubscriptionNotifications()

  const subscription = subscriptionResponse?.data?.success ? subscriptionResponse.data.data : null
  const notifications = notificationsResponse?.data?.success ? notificationsResponse.data.data : []
  const unreadNotifications = notifications.filter(n => !n.read)

  const hasActiveSubscription = subscription && subscription.status === SubscriptionStatus.ACTIVE

  if (subscriptionLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">Carregando informações da assinatura...</p>
        </div>
      </div>
    )
  }

  if (subscriptionError) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md">
          <CardContent className="p-6 text-center space-y-4">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
            <div className="space-y-2">
              <h3 className="font-semibold">Erro ao carregar assinatura</h3>
              <p className="text-sm text-muted-foreground">
                Não foi possível carregar as informações da sua assinatura. 
                Tente novamente em alguns instantes.
              </p>
            </div>
            <Button onClick={() => window.location.reload()}>
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Assinatura</h1>
          <p className="text-muted-foreground">
            Gerencie sua assinatura, faturas e configurações de pagamento
          </p>
        </div>

        {/* Notifications Badge */}
        {unreadNotifications.length > 0 && (
          <div className="flex items-center gap-2">
            <Bell className="h-4 w-4 text-orange-500" />
            <Badge variant="destructive">
              {unreadNotifications.length} notificação{unreadNotifications.length > 1 ? 'ões' : ''}
            </Badge>
          </div>
        )}
      </div>

      {/* Notifications */}
      {unreadNotifications.length > 0 && (
        <div className="space-y-3">
          {unreadNotifications.slice(0, 3).map((notification) => (
            <Card key={notification.id} className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    {notification.severity === 'error' && <AlertTriangle className="h-4 w-4 text-red-500" />}
                    {notification.severity === 'warning' && <AlertTriangle className="h-4 w-4 text-orange-500" />}
                    {notification.severity === 'info' && <CheckCircle className="h-4 w-4 text-blue-500" />}
                  </div>
                  <div className="space-y-1 flex-1">
                    <h4 className="font-semibold text-sm">{notification.title}</h4>
                    <p className="text-sm text-muted-foreground">{notification.message}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Main Content */}
      {!subscription || showPlanSelector ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {!subscription ? 'Escolha seu Plano' : 'Alterar Plano'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!subscription ? (
                <p className="text-muted-foreground mb-6">
                  Você ainda não possui uma assinatura ativa. Escolha o plano que melhor 
                  atende às necessidades da sua concessionária.
                </p>
              ) : (
                <p className="text-muted-foreground mb-6">
                  Faça upgrade ou downgrade do seu plano atual. As alterações serão 
                  aplicadas no próximo ciclo de cobrança.
                </p>
              )}
              
              <PlanSelector 
                onPlanSelected={() => setShowPlanSelector(false)}
              />
              
              {subscription && (
                <div className="mt-6 pt-6 border-t">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowPlanSelector(false)}
                  >
                    Cancelar Alteração
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Visão Geral
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Faturas
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configurações
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <SubscriptionCard 
              subscription={subscription}
              onManage={() => setActiveTab("settings")}
              onUpgrade={() => setShowPlanSelector(true)}
            />
          </TabsContent>

          <TabsContent value="billing" className="space-y-6">
            <BillingHistory />
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Plan Management */}
              <Card>
                <CardHeader>
                  <CardTitle>Gerenciar Plano</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Plano atual: <span className="font-semibold">{subscription.plan_type}</span>
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Próximo vencimento: {subscription.next_due_date || 'Não definido'}
                    </p>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <Button 
                      className="w-full" 
                      onClick={() => setShowPlanSelector(true)}
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Alterar Plano
                    </Button>
                    
                    <Button variant="outline" className="w-full">
                      <Settings className="h-4 w-4 mr-2" />
                      Configurar Cobrança
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Payment Method */}
              <Card>
                <CardHeader>
                  <CardTitle>Método de Pagamento</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Método atual: <span className="font-semibold">{subscription.billing_type}</span>
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Ciclo: <span className="font-semibold">
                        {subscription.cycle === 'YEARLY' ? 'Anual' : 'Mensal'}
                      </span>
                    </p>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full">
                      <CreditCard className="h-4 w-4 mr-2" />
                      Alterar Método
                    </Button>
                    
                    <Button variant="outline" className="w-full">
                      <FileText className="h-4 w-4 mr-2" />
                      Aplicar Cupom
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Account Status */}
              <Card>
                <CardHeader>
                  <CardTitle>Status da Conta</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Status da Assinatura</span>
                    <Badge className={
                      hasActiveSubscription 
                        ? "bg-green-100 text-green-800" 
                        : "bg-red-100 text-red-800"
                    }>
                      {subscription.status}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Renovação Automática</span>
                    <Badge variant="outline">
                      {hasActiveSubscription ? 'Ativa' : 'Inativa'}
                    </Badge>
                  </div>
                  
                  <Separator />
                  
                  <Button variant="outline" className="w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Configurações Avançadas
                  </Button>
                </CardContent>
              </Card>

              {/* Support */}
              <Card>
                <CardHeader>
                  <CardTitle>Suporte</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Precisa de ajuda com sua assinatura? Nossa equipe está pronta para ajudar.
                  </p>
                  
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full">
                      Contatar Suporte
                    </Button>
                    
                    <Button variant="outline" className="w-full">
                      Central de Ajuda
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
