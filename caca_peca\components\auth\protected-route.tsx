/**
 * Protected Route Component
 * Wrapper component for protecting routes based on authentication and roles
 */

'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/lib/types/api';
import { Loader2, Lock } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole | UserRole[];
  fallbackPath?: string;
  showUnauthorized?: boolean;
  loadingComponent?: React.ReactNode;
  unauthorizedComponent?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  requiredRole,
  fallbackPath = '/auth',
  showUnauthorized = true,
  loadingComponent,
  unauthorizedComponent,
}: ProtectedRouteProps) {
  const router = useRouter();
  const { isAuthenticated, isLoading, user, hasRole } = useAuth();

  // Show loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-sm text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname + window.location.search;
      const redirectUrl = `${fallbackPath}?redirect=${encodeURIComponent(currentPath)}`;
      router.push(redirectUrl);
    }
    return null;
  }

  // Check role-based access
  if (requiredRole && user) {
    const requiredRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    const hasRequiredRole = requiredRoles.some(role => hasRole(role));

    if (!hasRequiredRole) {
      if (!showUnauthorized) {
        router.push('/unauthorized');
        return null;
      }

      if (unauthorizedComponent) {
        return <>{unauthorizedComponent}</>;
      }

      return <UnauthorizedAccess userRole={user.role} requiredRoles={requiredRoles} />;
    }
  }

  // Render protected content
  return <>{children}</>;
}

// Default unauthorized access component
function UnauthorizedAccess({ 
  userRole, 
  requiredRoles 
}: { 
  userRole: UserRole; 
  requiredRoles: UserRole[] 
}) {
  const router = useRouter();

  const getRoleDisplayName = (role: UserRole): string => {
    switch (role) {
      case UserRole.ADMIN:
        return 'Administrador';
      case UserRole.DEALERSHIP:
        return 'Concessionária';
      case UserRole.CUSTOMER:
        return 'Cliente';
      default:
        return role;
    }
  };

  const getRedirectPath = (role: UserRole): string => {
    switch (role) {
      case UserRole.ADMIN:
        return '/admin';
      case UserRole.DEALERSHIP:
        return '/painel';
      case UserRole.CUSTOMER:
        return '/';
      default:
        return '/';
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Lock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <CardTitle>Acesso Negado</CardTitle>
          <CardDescription>
            Você não tem permissão para acessar esta página
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              <strong>Seu perfil:</strong> {getRoleDisplayName(userRole)}
            </p>
            <p className="text-sm text-muted-foreground">
              <strong>Perfis necessários:</strong>{' '}
              {requiredRoles.map(getRoleDisplayName).join(', ')}
            </p>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button 
              onClick={() => router.push(getRedirectPath(userRole))}
              className="w-full"
            >
              Ir para Minha Área
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.back()}
              className="w-full"
            >
              Voltar
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Higher-order component version for class components or other use cases
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Hook for checking route access programmatically
export function useRouteProtection() {
  const { isAuthenticated, user, hasRole } = useAuth();

  const canAccess = (requiredRole?: UserRole | UserRole[]): boolean => {
    if (!isAuthenticated || !user) {
      return false;
    }

    if (!requiredRole) {
      return true;
    }

    const requiredRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    return requiredRoles.some(role => hasRole(role));
  };

  const getAccessLevel = (): 'none' | 'authenticated' | 'admin' | 'dealership' | 'customer' => {
    if (!isAuthenticated || !user) {
      return 'none';
    }

    if (hasRole(UserRole.ADMIN)) {
      return 'admin';
    }

    if (hasRole(UserRole.DEALERSHIP)) {
      return 'dealership';
    }

    if (hasRole(UserRole.CUSTOMER)) {
      return 'customer';
    }

    return 'authenticated';
  };

  return {
    canAccess,
    getAccessLevel,
    isAuthenticated,
    user,
  };
}