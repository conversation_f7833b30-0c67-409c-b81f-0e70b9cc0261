"""
Pydantic schemas for inventory management.
"""
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
from decimal import Decimal
from uuid import UUID

from app.schemas.part import PartSummary
from app.schemas.dealership import DealershipSummary


class InventoryBase(BaseModel):
    """Base inventory schema."""
    quantity: int = Field(..., ge=0, description="Available quantity in stock")
    price: Decimal = Field(..., gt=0, description="Selling price")
    cost: Optional[Decimal] = Field(None, gt=0, description="Cost price (for dealership)")
    location: Optional[str] = Field(None, max_length=100, description="Storage location")
    condition: str = Field(default="new", description="Part condition")
    warranty_months: Optional[int] = Field(None, ge=0, le=120, description="Warranty period in months")
    is_available: bool = Field(default=True, description="Whether the part is available for sale")
    minimum_stock: Optional[int] = Field(None, ge=0, description="Minimum stock level for alerts")
    notes: Optional[str] = Field(None, max_length=500, description="Additional notes")
    supplier: Optional[str] = Field(None, max_length=255, description="Supplier information")

    @validator('condition')
    def validate_condition(cls, v):
        """Validate part condition."""
        valid_conditions = ["new", "used", "refurbished", "damaged"]
        if v.lower() not in valid_conditions:
            raise ValueError(f'Condition must be one of: {", ".join(valid_conditions)}')
        return v.lower()

    @validator('price', 'cost')
    def validate_prices(cls, v):
        """Validate price values."""
        if v is not None and v <= 0:
            raise ValueError('Price must be greater than 0')
        return v


class InventoryCreate(InventoryBase):
    """Schema for creating a new inventory item."""
    dealership_id: UUID = Field(..., description="Dealership UUID")
    part_id: UUID = Field(..., description="Part UUID")


class InventoryUpdate(BaseModel):
    """Schema for updating inventory information."""
    quantity: Optional[int] = Field(None, ge=0)
    price: Optional[Decimal] = Field(None, gt=0)
    cost: Optional[Decimal] = Field(None, gt=0)
    location: Optional[str] = Field(None, max_length=100)
    condition: Optional[str] = None
    warranty_months: Optional[int] = Field(None, ge=0, le=120)
    is_available: Optional[bool] = None
    minimum_stock: Optional[int] = Field(None, ge=0)
    notes: Optional[str] = Field(None, max_length=500)
    supplier: Optional[str] = Field(None, max_length=255)

    @validator('condition')
    def validate_condition(cls, v):
        """Validate part condition."""
        if v is None:
            return v
        valid_conditions = ["new", "used", "refurbished", "damaged"]
        if v.lower() not in valid_conditions:
            raise ValueError(f'Condition must be one of: {", ".join(valid_conditions)}')
        return v.lower()


class InventoryInDB(InventoryBase):
    """Schema for inventory in database."""
    id: UUID
    dealership_id: UUID
    part_id: UUID
    last_updated: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Inventory(InventoryInDB):
    """Public inventory schema."""
    pass


class InventoryWithDetails(Inventory):
    """Inventory schema with part and dealership details."""
    part: Optional[PartSummary] = None
    dealership: Optional[DealershipSummary] = None
    is_low_stock: bool = False
    is_in_stock: bool = False


class InventorySummary(BaseModel):
    """Summary schema for inventory listings."""
    id: UUID
    dealership_id: UUID
    part_id: UUID
    quantity: int
    price: Decimal
    condition: str
    is_available: bool
    last_updated: datetime
    
    class Config:
        from_attributes = True


class InventoryListResponse(BaseModel):
    """Response schema for inventory listing."""
    inventories: List[InventoryWithDetails]
    total: int
    page: int
    per_page: int
    pages: int


class InventorySearchFilters(BaseModel):
    """Schema for inventory search filters."""
    part_code: Optional[str] = Field(None, description="Filter by part code")
    part_name: Optional[str] = Field(None, description="Filter by part name")
    dealership_id: Optional[UUID] = Field(None, description="Filter by dealership")
    brand: Optional[str] = Field(None, description="Filter by vehicle brand")
    model: Optional[str] = Field(None, description="Filter by vehicle model")
    year: Optional[int] = Field(None, ge=1900, le=2030, description="Filter by vehicle year")
    category: Optional[str] = Field(None, description="Filter by part category")
    condition: Optional[str] = Field(None, description="Filter by condition")
    min_price: Optional[Decimal] = Field(None, gt=0, description="Minimum price")
    max_price: Optional[Decimal] = Field(None, gt=0, description="Maximum price")
    min_quantity: Optional[int] = Field(None, ge=0, description="Minimum quantity")
    available_only: bool = Field(True, description="Only show available items")
    in_stock_only: bool = Field(True, description="Only show items in stock")
    low_stock_only: bool = Field(False, description="Only show low stock items")
    city: Optional[str] = Field(None, description="Filter by dealership city")
    state: Optional[str] = Field(None, description="Filter by dealership state")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: str = Field("last_updated", description="Sort field")
    sort_order: str = Field("desc", description="Sort order (asc/desc)")

    @validator('sort_by')
    def validate_sort_by(cls, v):
        """Validate sort field."""
        valid_fields = ['price', 'quantity', 'last_updated', 'created_at', 'part_code', 'dealership_name']
        if v not in valid_fields:
            raise ValueError(f'Sort field must be one of: {", ".join(valid_fields)}')
        return v

    @validator('sort_order')
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v.lower() not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v.lower()

    @validator('condition')
    def validate_condition(cls, v):
        """Validate condition filter."""
        if v is None:
            return v
        valid_conditions = ["new", "used", "refurbished", "damaged"]
        if v.lower() not in valid_conditions:
            raise ValueError(f'Condition must be one of: {", ".join(valid_conditions)}')
        return v.lower()


class InventoryStats(BaseModel):
    """Schema for inventory statistics."""
    total_items: int
    total_value: Decimal
    low_stock_items: int
    out_of_stock_items: int
    items_by_condition: List[dict]
    items_by_category: List[dict]
    top_dealerships: List[dict]
    recent_updates: int


class DealershipInventoryStats(BaseModel):
    """Schema for dealership-specific inventory statistics."""
    dealership_id: UUID
    total_items: int
    total_value: Decimal
    average_price: Decimal
    low_stock_items: int
    out_of_stock_items: int
    items_by_condition: List[dict]
    items_by_category: List[dict]
    last_update: Optional[datetime] = None


class InventoryAlert(BaseModel):
    """Schema for inventory alerts."""
    id: UUID
    inventory_id: UUID
    alert_type: str  # "low_stock", "out_of_stock", "price_change"
    message: str
    severity: str  # "low", "medium", "high"
    created_at: datetime
    is_read: bool = False


class BulkInventoryUpdate(BaseModel):
    """Schema for bulk inventory updates."""
    updates: List[dict]  # List of {inventory_id, field, value}
    operation: str = Field(..., description="Operation type: update, delete, adjust_quantity")

    @validator('operation')
    def validate_operation(cls, v):
        """Validate operation type."""
        valid_operations = ["update", "delete", "adjust_quantity", "set_availability"]
        if v not in valid_operations:
            raise ValueError(f'Operation must be one of: {", ".join(valid_operations)}')
        return v


class BulkInventoryResponse(BaseModel):
    """Response schema for bulk inventory operations."""
    processed: int
    updated: int
    errors: List[dict]
    total_requested: int


class InventoryValuation(BaseModel):
    """Schema for inventory valuation."""
    dealership_id: UUID
    total_cost: Decimal
    total_selling_value: Decimal
    potential_profit: Decimal
    margin_percentage: float
    items_count: int
    valuation_date: datetime


class InventoryMovement(BaseModel):
    """Schema for inventory movement tracking."""
    inventory_id: UUID
    movement_type: str  # "sale", "adjustment", "return", "damage"
    quantity_change: int
    previous_quantity: int
    new_quantity: int
    reason: Optional[str] = None
    created_at: datetime
    created_by: Optional[UUID] = None  # User ID
