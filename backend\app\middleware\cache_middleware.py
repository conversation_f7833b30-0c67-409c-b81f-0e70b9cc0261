"""
Cache middleware for automatic cache warming and monitoring.
"""
import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.cache import cache_manager, CacheKeys
from app.core.cache_decorators import CacheService

logger = logging.getLogger(__name__)


class CacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware for cache monitoring and automatic warming.
    """
    
    def __init__(self, app, warm_up_on_startup: bool = True):
        super().__init__(app)
        self.warm_up_on_startup = warm_up_on_startup
        self.startup_completed = False
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with cache monitoring.
        
        Args:
            request: FastAPI request
            call_next: Next middleware/endpoint
            
        Returns:
            Response
        """
        # Perform startup warm-up once
        if not self.startup_completed and self.warm_up_on_startup:
            await self._warm_up_cache()
            self.startup_completed = True
        
        # Track cache performance for search endpoints
        start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Log cache performance for search endpoints
        if self._is_search_endpoint(request.url.path):
            processing_time = time.time() - start_time
            await self._log_search_performance(request, processing_time)
        
        # Add cache headers
        if cache_manager.is_available():
            response.headers["X-Cache-Status"] = "available"
        else:
            response.headers["X-Cache-Status"] = "unavailable"
        
        return response
    
    def _is_search_endpoint(self, path: str) -> bool:
        """Check if the endpoint is a search endpoint."""
        search_paths = [
            "/api/v1/parts/search",
            "/api/v1/inventory/search",
            "/api/v1/parts/",
            "/api/v1/inventory/"
        ]
        return any(search_path in path for search_path in search_paths)
    
    async def _log_search_performance(self, request: Request, processing_time: float):
        """Log search performance metrics."""
        if processing_time > 1.0:  # Log slow searches (>1 second)
            logger.warning(
                f"Slow search detected: {request.url.path} took {processing_time:.2f}s"
            )
        elif processing_time < 0.1:  # Likely cache hit
            logger.debug(
                f"Fast search (likely cache hit): {request.url.path} took {processing_time:.3f}s"
            )
    
    async def _warm_up_cache(self):
        """Warm up cache with popular data."""
        if not cache_manager.is_available():
            logger.warning("Cache not available for warm-up")
            return
        
        try:
            logger.info("Starting cache warm-up...")
            
            # Warm up popular searches
            CacheService.warm_up_popular_searches()
            
            # You could add more warm-up logic here
            # For example, cache popular parts, dealership info, etc.
            
            logger.info("Cache warm-up completed")
            
        except Exception as e:
            logger.error(f"Error during cache warm-up: {e}")


class CacheMetricsCollector:
    """
    Collector for cache metrics and statistics.
    """
    
    def __init__(self):
        self.search_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.slow_searches = 0
        
    def record_search(self, cache_hit: bool, processing_time: float):
        """Record search metrics."""
        self.search_count += 1
        
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
            
        if processing_time > 1.0:
            self.slow_searches += 1
    
    def get_metrics(self) -> dict:
        """Get collected metrics."""
        hit_rate = (self.cache_hits / self.search_count * 100) if self.search_count > 0 else 0
        
        return {
            "total_searches": self.search_count,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate_percentage": round(hit_rate, 2),
            "slow_searches": self.slow_searches,
            "slow_search_rate": round((self.slow_searches / self.search_count * 100), 2) if self.search_count > 0 else 0
        }
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.search_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.slow_searches = 0


# Global metrics collector
metrics_collector = CacheMetricsCollector()


def get_cache_metrics() -> dict:
    """Get current cache metrics."""
    return metrics_collector.get_metrics()


def reset_cache_metrics():
    """Reset cache metrics."""
    metrics_collector.reset_metrics()


class CacheHealthChecker:
    """
    Health checker for cache system.
    """
    
    @staticmethod
    def check_cache_health() -> dict:
        """
        Perform comprehensive cache health check.
        
        Returns:
            Health check results
        """
        health_status = {
            "cache_available": False,
            "connection_test": False,
            "read_test": False,
            "write_test": False,
            "delete_test": False,
            "overall_status": "unhealthy",
            "errors": []
        }
        
        try:
            # Test cache availability
            if cache_manager.is_available():
                health_status["cache_available"] = True
                
                # Test connection
                try:
                    cache_manager.redis_client.ping()
                    health_status["connection_test"] = True
                except Exception as e:
                    health_status["errors"].append(f"Connection test failed: {e}")
                
                # Test write operation
                test_key = "health_check_test"
                test_value = "test_value"
                
                try:
                    if cache_manager.set(test_key, test_value, ttl=60):
                        health_status["write_test"] = True
                    else:
                        health_status["errors"].append("Write test failed")
                except Exception as e:
                    health_status["errors"].append(f"Write test error: {e}")
                
                # Test read operation
                try:
                    retrieved_value = cache_manager.get(test_key)
                    if retrieved_value == test_value:
                        health_status["read_test"] = True
                    else:
                        health_status["errors"].append("Read test failed - value mismatch")
                except Exception as e:
                    health_status["errors"].append(f"Read test error: {e}")
                
                # Test delete operation
                try:
                    if cache_manager.delete(test_key):
                        health_status["delete_test"] = True
                    else:
                        health_status["errors"].append("Delete test failed")
                except Exception as e:
                    health_status["errors"].append(f"Delete test error: {e}")
                
                # Determine overall status
                if all([
                    health_status["cache_available"],
                    health_status["connection_test"],
                    health_status["read_test"],
                    health_status["write_test"],
                    health_status["delete_test"]
                ]):
                    health_status["overall_status"] = "healthy"
                else:
                    health_status["overall_status"] = "degraded"
            
            else:
                health_status["errors"].append("Cache is not available")
                
        except Exception as e:
            health_status["errors"].append(f"Health check error: {e}")
        
        return health_status
