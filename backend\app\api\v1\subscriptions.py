"""
Subscription management endpoints for the AutoParts API.
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.subscription import SubscriptionService
from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
    get_current_dealership_user
)
from app.schemas.subscription import (
    SubscriptionCreate,
    SubscriptionUpdate,
    Subscription,
    SubscriptionSummary,
    SubscriptionListResponse,
    SubscriptionStats,
    SubscriptionStatusUpdate
)
from app.models.user import User
from app.models.subscription import SubscriptionStatus, PlanType

router = APIRouter()


def get_subscription_service(db: Session = Depends(get_db)) -> SubscriptionService:
    """Get subscription service instance."""
    return SubscriptionService(db)


@router.post("/", response_model=Subscription)
async def create_subscription(
    subscription_data: SubscriptionCreate,
    current_user: User = Depends(get_current_admin_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Create a new subscription (admin only).
    
    Args:
        subscription_data: Subscription creation data
        current_user: Current admin user
        subscription_service: Subscription service instance
        
    Returns:
        Created subscription
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        subscription = await subscription_service.create_subscription_for_dealership(
            subscription_data.dealership_id,
            subscription_data
        )
        return Subscription.from_orm(subscription)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create subscription: {str(e)}"
        )


@router.get("/", response_model=SubscriptionListResponse)
async def list_subscriptions(
    status_filter: Optional[SubscriptionStatus] = Query(None, alias="status", description="Filter by status"),
    plan_type: Optional[PlanType] = Query(None, description="Filter by plan type"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_active_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    List subscriptions with filtering and pagination.
    
    Args:
        status_filter: Filter by subscription status
        plan_type: Filter by plan type
        page: Page number
        per_page: Items per page
        current_user: Current authenticated user
        subscription_service: Subscription service instance
        
    Returns:
        Paginated list of subscriptions
    """
    skip = (page - 1) * per_page
    
    # If user is not admin, only show their dealership's subscriptions
    if not current_user.is_admin:
        if not current_user.dealership_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: no dealership associated"
            )
        
        # Get only this dealership's subscription
        subscription = subscription_service.get_subscription_by_dealership(
            current_user.dealership_id
        )
        
        subscriptions = [subscription] if subscription else []
        total = len(subscriptions)
    else:
        # Admin can see all subscriptions
        subscriptions, total = subscription_service.list_subscriptions(
            skip=skip,
            limit=per_page,
            status=status_filter,
            plan_type=plan_type
        )
    
    # Convert to summary format
    subscription_summaries = [
        SubscriptionSummary(
            id=s.id,
            dealership_id=s.dealership_id,
            plan_type=s.plan_type,
            status=s.status,
            value=s.value,
            next_due_date=s.next_due_date,
            is_active=s.is_active
        )
        for s in subscriptions
    ]
    
    pages = (total + per_page - 1) // per_page
    
    return SubscriptionListResponse(
        subscriptions=subscription_summaries,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/stats", response_model=SubscriptionStats)
async def get_subscription_stats(
    current_user: User = Depends(get_current_admin_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Get subscription statistics (admin only).
    
    Args:
        current_user: Current admin user
        subscription_service: Subscription service instance
        
    Returns:
        Subscription statistics
    """
    return subscription_service.get_subscription_stats()


@router.get("/{subscription_id}", response_model=Subscription)
async def get_subscription(
    subscription_id: UUID,
    current_user: User = Depends(get_current_active_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Get subscription by ID.
    
    Args:
        subscription_id: Subscription UUID
        current_user: Current authenticated user
        subscription_service: Subscription service instance
        
    Returns:
        Subscription details
        
    Raises:
        HTTPException: If subscription not found or access denied
    """
    subscription = subscription_service.get_subscription_by_id(subscription_id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )
    
    # Check access permissions
    if not current_user.is_admin:
        if (current_user.role.value != "dealership" or 
            current_user.dealership_id != subscription.dealership_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions for this subscription"
            )
    
    return Subscription.from_orm(subscription)


@router.put("/{subscription_id}", response_model=Subscription)
async def update_subscription(
    subscription_id: UUID,
    update_data: SubscriptionUpdate,
    current_user: User = Depends(get_current_admin_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Update subscription information (admin only).
    
    Args:
        subscription_id: Subscription UUID
        update_data: Updated subscription data
        current_user: Current admin user
        subscription_service: Subscription service instance
        
    Returns:
        Updated subscription
        
    Raises:
        HTTPException: If subscription not found or update fails
    """
    try:
        subscription = await subscription_service.update_subscription(
            subscription_id, update_data
        )
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        return Subscription.from_orm(subscription)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update subscription: {str(e)}"
        )


@router.post("/{subscription_id}/cancel", response_model=Subscription)
async def cancel_subscription(
    subscription_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Cancel a subscription (admin only).
    
    Args:
        subscription_id: Subscription UUID
        current_user: Current admin user
        subscription_service: Subscription service instance
        
    Returns:
        Cancelled subscription
        
    Raises:
        HTTPException: If subscription not found or cancellation fails
    """
    try:
        subscription = await subscription_service.cancel_subscription(subscription_id)
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        return Subscription.from_orm(subscription)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel subscription: {str(e)}"
        )


@router.post("/{subscription_id}/sync", response_model=Subscription)
async def sync_subscription_status(
    subscription_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Sync subscription status with Asaas (admin only).
    
    Args:
        subscription_id: Subscription UUID
        current_user: Current admin user
        subscription_service: Subscription service instance
        
    Returns:
        Synced subscription
        
    Raises:
        HTTPException: If subscription not found or sync fails
    """
    try:
        subscription = await subscription_service.sync_subscription_status(subscription_id)
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        return Subscription.from_orm(subscription)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync subscription: {str(e)}"
        )


@router.get("/dealership/{dealership_id}", response_model=Subscription)
async def get_dealership_subscription(
    dealership_id: UUID,
    current_user: User = Depends(get_current_active_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """
    Get subscription for a specific dealership.
    
    Args:
        dealership_id: Dealership UUID
        current_user: Current authenticated user
        subscription_service: Subscription service instance
        
    Returns:
        Dealership subscription
        
    Raises:
        HTTPException: If subscription not found or access denied
    """
    # Check access permissions
    if not current_user.is_admin:
        if (current_user.role.value != "dealership" or 
            current_user.dealership_id != dealership_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions for this dealership"
            )
    
    subscription = subscription_service.get_subscription_by_dealership(dealership_id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No subscription found for this dealership"
        )
    
    return Subscription.from_orm(subscription)
