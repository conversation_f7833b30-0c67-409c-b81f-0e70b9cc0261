"""
Simple test script for Excel file validation without database.
"""
import pandas as pd
import os
from typing import Dict, List, Any


def validate_excel_file(file_path: str) -> Dict[str, Any]:
    """
    Validate Excel file and return validation results.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        Validation results with detected columns and suggestions
    """
    try:
        # Read Excel file
        print(f"📖 Reading Excel file: {file_path}")
        df = pd.read_excel(file_path)
        
        # Get basic information
        total_rows = len(df)
        detected_columns = df.columns.tolist()
        
        print(f"✅ File read successfully!")
        print(f"   - Total rows: {total_rows}")
        print(f"   - Total columns: {len(detected_columns)}")
        
        # Get sample data (first 3 rows)
        sample_data = df.head(3).to_dict('records')
        
        # Clean sample data (convert NaN to None)
        for row in sample_data:
            for key, value in row.items():
                if pd.isna(value):
                    row[key] = None
        
        # Generate mapping suggestions based on column names
        mapping_suggestions = generate_mapping_suggestions(detected_columns)
        
        # Validate data types and content
        validation_errors = []
        warnings = []
        
        # Check for empty file
        if total_rows == 0:
            validation_errors.append("File is empty")
        
        # Check for required columns
        required_columns = ['CodigoReferenciaProduto', 'Produto_Descricao', 'QtdeSaldo']
        missing_required = [col for col in required_columns if col not in detected_columns]
        if missing_required:
            validation_errors.append(f"Missing required columns: {', '.join(missing_required)}")
        
        # Check for duplicate columns
        if len(detected_columns) != len(set(detected_columns)):
            warnings.append("Duplicate column names detected")
        
        # Validate data types in sample
        validate_sample_data_types(sample_data, warnings)
        
        return {
            'is_valid': len(validation_errors) == 0,
            'total_rows': total_rows,
            'sample_data': sample_data,
            'detected_columns': detected_columns,
            'mapping_suggestions': mapping_suggestions,
            'validation_errors': validation_errors,
            'warnings': warnings
        }
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        return {
            'is_valid': False,
            'total_rows': 0,
            'sample_data': [],
            'detected_columns': [],
            'mapping_suggestions': {},
            'validation_errors': [f"Error reading file: {str(e)}"],
            'warnings': []
        }


def generate_mapping_suggestions(columns: List[str]) -> Dict[str, str]:
    """Generate mapping suggestions based on column names."""
    # Mapping from Excel columns to database fields
    column_mappings = {
        # Dealership fields
        'EmpresaNome': 'dealership.name',
        
        # Part fields
        'CodigoReferenciaProduto': 'part.code',
        'Produto_Descricao': 'part.name',
        'Marca_Descricao': 'part.brand',
        
        # Inventory fields
        'QtdeSaldo': 'inventory.quantity',
        'ValorCustoMedio': 'inventory.cost',
        'ValorPublico': 'inventory.price',
        'ValorPublicoSugerido': 'inventory.suggested_price',
        'ValorReposicao': 'inventory.replacement_value',
        'ValorGarantia': 'inventory.warranty_value',
        'ClasABCLetraPopularidade': 'inventory.abc_classification',
        'LocalizacaoProdutoId': 'inventory.location'
    }
    
    suggestions = {}
    for col in columns:
        if col in column_mappings:
            suggestions[col] = column_mappings[col]
        else:
            # Try to find partial matches
            col_lower = col.lower()
            for excel_col, db_field in column_mappings.items():
                if excel_col.lower() in col_lower or col_lower in excel_col.lower():
                    suggestions[col] = db_field
                    break
    
    return suggestions


def validate_sample_data_types(sample_data: List[Dict], warnings: List[str]):
    """Validate data types in sample data."""
    if not sample_data:
        return
    
    # Check for numeric fields
    numeric_fields = ['QtdeSaldo', 'ValorCustoMedio', 'ValorPublico', 'ValorPublicoSugerido', 
                     'ValorReposicao', 'ValorGarantia']
    
    for field in numeric_fields:
        if field in sample_data[0]:
            for row in sample_data:
                value = row.get(field)
                if value is not None and not isinstance(value, (int, float)):
                    try:
                        float(str(value).replace(',', '.'))
                    except (ValueError, TypeError):
                        warnings.append(f"Non-numeric value found in {field}: {value}")
                        break


def analyze_data_patterns(df: pd.DataFrame):
    """Analyze data patterns in the Excel file."""
    print("\n📊 Data Analysis:")
    
    # Check for null values
    null_counts = df.isnull().sum()
    if null_counts.any():
        print("   Null values found:")
        for col, count in null_counts.items():
            if count > 0:
                percentage = (count / len(df)) * 100
                print(f"     - {col}: {count} ({percentage:.1f}%)")
    
    # Check data types
    print("\n   Data types:")
    for col, dtype in df.dtypes.items():
        print(f"     - {col}: {dtype}")
    
    # Check for potential inventory items (non-zero quantities)
    if 'QtdeSaldo' in df.columns:
        qty_col = df['QtdeSaldo']
        non_zero_qty = qty_col[qty_col > 0].count()
        total_qty = qty_col.sum()
        print(f"\n   Inventory summary:")
        print(f"     - Items with stock: {non_zero_qty}/{len(df)} ({(non_zero_qty/len(df)*100):.1f}%)")
        print(f"     - Total quantity: {total_qty}")
    
    # Check for unique dealerships
    if 'EmpresaNome' in df.columns:
        unique_dealerships = df['EmpresaNome'].nunique()
        print(f"     - Unique dealerships: {unique_dealerships}")
    
    # Check for unique parts
    if 'CodigoReferenciaProduto' in df.columns:
        unique_parts = df['CodigoReferenciaProduto'].nunique()
        print(f"     - Unique parts: {unique_parts}")


def main():
    """Main test function."""
    print("🧪 AutoParts Excel File Analysis")
    print("=" * 50)
    
    # File path
    excel_file_path = r"C:\Users\<USER>\Documents\Cursor\caca-pecas\RPR073_ANALISEITENSEMESTOQUE240725140649.xlsx"
    
    if not os.path.exists(excel_file_path):
        print(f"❌ File not found: {excel_file_path}")
        return
    
    print(f"📁 Analyzing file: {os.path.basename(excel_file_path)}")
    
    # Validate the Excel file
    validation_result = validate_excel_file(excel_file_path)
    
    print(f"\n🔍 Validation Results:")
    print(f"   - Valid: {validation_result['is_valid']}")
    print(f"   - Total rows: {validation_result['total_rows']}")
    print(f"   - Detected columns: {len(validation_result['detected_columns'])}")
    
    if validation_result['detected_columns']:
        print("\n📋 Detected columns:")
        for i, col in enumerate(validation_result['detected_columns'], 1):
            print(f"   {i:2d}. {col}")
    
    if validation_result['mapping_suggestions']:
        print("\n💡 Mapping suggestions:")
        for excel_col, db_field in validation_result['mapping_suggestions'].items():
            print(f"   {excel_col} -> {db_field}")
    
    if validation_result['validation_errors']:
        print("\n❌ Validation errors:")
        for error in validation_result['validation_errors']:
            print(f"   - {error}")
    
    if validation_result['warnings']:
        print("\n⚠️  Warnings:")
        for warning in validation_result['warnings']:
            print(f"   - {warning}")
    
    if validation_result['sample_data']:
        print("\n📊 Sample data (first row):")
        sample_row = validation_result['sample_data'][0]
        for key, value in sample_row.items():
            if value is not None:
                print(f"   {key}: {value}")
    
    # Perform detailed analysis
    if validation_result['is_valid']:
        df = pd.read_excel(excel_file_path)
        analyze_data_patterns(df)
    
    print("\n🎉 Analysis completed!")


if __name__ == "__main__":
    main()
