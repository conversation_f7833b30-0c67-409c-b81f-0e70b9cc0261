#!/bin/bash

# AutoParts API Monitoring Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Configuration
COMPOSE_FILE="docker-compose.yml"
API_URL="http://localhost:8000"
ALERT_EMAIL=""
SLACK_WEBHOOK=""

# Load environment variables
if [ -f ".env" ]; then
    set -a
    source ".env"
    set +a
    API_URL="http://localhost:${API_PORT:-8000}"
fi

# Check service health
check_service_health() {
    local service=$1
    local container_name=$2
    
    log "Checking $service health..."
    
    if docker ps --filter "name=$container_name" --filter "status=running" | grep -q "$container_name"; then
        # Check if container is healthy
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
        
        case $health_status in
            "healthy")
                success "$service is healthy"
                return 0
                ;;
            "unhealthy")
                error "$service is unhealthy"
                return 1
                ;;
            "starting")
                warning "$service is starting"
                return 2
                ;;
            "none")
                warning "$service has no health check"
                return 0
                ;;
            *)
                warning "$service health status unknown: $health_status"
                return 2
                ;;
        esac
    else
        error "$service is not running"
        return 1
    fi
}

# Check API endpoints
check_api_endpoints() {
    log "Checking API endpoints..."
    
    local endpoints=(
        "/health:Health Check"
        "/api/v1/auth/me:Authentication (requires token)"
        "/docs:API Documentation"
    )
    
    for endpoint_info in "${endpoints[@]}"; do
        local endpoint=$(echo "$endpoint_info" | cut -d: -f1)
        local description=$(echo "$endpoint_info" | cut -d: -f2)
        local url="$API_URL$endpoint"
        
        log "Testing $description ($endpoint)..."
        
        if [[ "$endpoint" == "/api/v1/auth/me" ]]; then
            # This endpoint requires authentication, expect 401
            local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
            if [ "$response" = "401" ]; then
                success "$description - Correctly requires authentication"
            else
                warning "$description - Unexpected response: $response"
            fi
        else
            local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
            if [ "$response" = "200" ]; then
                success "$description - OK"
            else
                error "$description - Failed (HTTP $response)"
            fi
        fi
    done
}

# Check database connectivity
check_database() {
    log "Checking database connectivity..."
    
    local db_container="autoparts_postgres"
    local db_name=${POSTGRES_DB:-autoparts}
    local db_user=${POSTGRES_USER:-autoparts}
    
    if docker exec "$db_container" pg_isready -U "$db_user" -d "$db_name" >/dev/null 2>&1; then
        success "Database is accessible"
        
        # Check database size
        local db_size=$(docker exec "$db_container" psql -U "$db_user" -d "$db_name" -t -c "SELECT pg_size_pretty(pg_database_size('$db_name'));" 2>/dev/null | xargs)
        log "Database size: $db_size"
        
        # Check connection count
        local connections=$(docker exec "$db_container" psql -U "$db_user" -d "$db_name" -t -c "SELECT count(*) FROM pg_stat_activity;" 2>/dev/null | xargs)
        log "Active connections: $connections"
        
        return 0
    else
        error "Database is not accessible"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "Checking Redis connectivity..."
    
    local redis_container="autoparts_redis"
    
    if docker exec "$redis_container" redis-cli ping >/dev/null 2>&1; then
        success "Redis is accessible"
        
        # Check Redis info
        local memory_usage=$(docker exec "$redis_container" redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')
        local connected_clients=$(docker exec "$redis_container" redis-cli info clients | grep "connected_clients" | cut -d: -f2 | tr -d '\r')
        
        log "Redis memory usage: $memory_usage"
        log "Connected clients: $connected_clients"
        
        return 0
    else
        error "Redis is not accessible"
        return 1
    fi
}

# Check disk space
check_disk_space() {
    log "Checking disk space..."
    
    local threshold=80
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -lt "$threshold" ]; then
        success "Disk usage: ${usage}% (OK)"
    else
        warning "Disk usage: ${usage}% (Above ${threshold}% threshold)"
    fi
    
    # Check Docker volumes
    log "Docker volume usage:"
    docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}\t{{.Reclaimable}}"
}

# Check memory usage
check_memory() {
    log "Checking memory usage..."
    
    # System memory
    local mem_info=$(free -h | awk 'NR==2{printf "Used: %s/%s (%.1f%%)", $3, $2, $3*100/$2}')
    log "System memory - $mem_info"
    
    # Docker container memory
    log "Container memory usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
}

# Check logs for errors
check_logs() {
    log "Checking recent logs for errors..."
    
    local containers=("autoparts_api" "autoparts_postgres" "autoparts_redis")
    local error_patterns=("ERROR" "FATAL" "CRITICAL" "Exception" "Traceback")
    
    for container in "${containers[@]}"; do
        if docker ps --filter "name=$container" | grep -q "$container"; then
            log "Checking $container logs..."
            
            for pattern in "${error_patterns[@]}"; do
                local error_count=$(docker logs "$container" --since="1h" 2>&1 | grep -c "$pattern" || echo "0")
                if [ "$error_count" -gt 0 ]; then
                    warning "$container: Found $error_count '$pattern' entries in last hour"
                fi
            done
        fi
    done
}

# Performance metrics
check_performance() {
    log "Checking performance metrics..."
    
    # API response time
    local api_response_time=$(curl -s -o /dev/null -w "%{time_total}" "$API_URL/health" || echo "0")
    log "API response time: ${api_response_time}s"
    
    if (( $(echo "$api_response_time > 2.0" | bc -l) )); then
        warning "API response time is slow (>${api_response_time}s)"
    fi
    
    # Database query performance
    local db_container="autoparts_postgres"
    local db_name=${POSTGRES_DB:-autoparts}
    local db_user=${POSTGRES_USER:-autoparts}
    
    if docker exec "$db_container" psql -U "$db_user" -d "$db_name" -c "SELECT 1;" >/dev/null 2>&1; then
        local slow_queries=$(docker exec "$db_container" psql -U "$db_user" -d "$db_name" -t -c "
            SELECT count(*) FROM pg_stat_activity 
            WHERE state = 'active' AND query_start < now() - interval '30 seconds';
        " 2>/dev/null | xargs)
        
        if [ "$slow_queries" -gt 0 ]; then
            warning "Found $slow_queries slow database queries"
        else
            success "No slow database queries detected"
        fi
    fi
}

# Send alert
send_alert() {
    local message="$1"
    local severity="$2"
    
    log "Sending alert: $message"
    
    # Email alert
    if [ -n "$ALERT_EMAIL" ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "AutoParts API Alert - $severity" "$ALERT_EMAIL"
    fi
    
    # Slack alert
    if [ -n "$SLACK_WEBHOOK" ] && command -v curl >/dev/null 2>&1; then
        local color="warning"
        [ "$severity" = "CRITICAL" ] && color="danger"
        [ "$severity" = "OK" ] && color="good"
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"title\":\"AutoParts API Alert\",\"text\":\"$message\",\"footer\":\"$(hostname)\",\"ts\":$(date +%s)}]}" \
            "$SLACK_WEBHOOK" >/dev/null 2>&1
    fi
}

# Generate health report
generate_report() {
    local report_file="./logs/health_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log "Generating health report: $report_file"
    
    {
        echo "AutoParts API Health Report"
        echo "=========================="
        echo "Generated: $(date)"
        echo "Host: $(hostname)"
        echo ""
        
        echo "Service Status:"
        check_service_health "API" "autoparts_api" && echo "  ✓ API: Healthy" || echo "  ✗ API: Unhealthy"
        check_service_health "Database" "autoparts_postgres" && echo "  ✓ Database: Healthy" || echo "  ✗ Database: Unhealthy"
        check_service_health "Redis" "autoparts_redis" && echo "  ✓ Redis: Healthy" || echo "  ✗ Redis: Unhealthy"
        
        echo ""
        echo "System Resources:"
        echo "  Disk: $(df / | awk 'NR==2 {print $5}') used"
        echo "  Memory: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
        echo "  Load: $(uptime | awk -F'load average:' '{print $2}')"
        
        echo ""
        echo "Docker Containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
    } > "$report_file"
    
    success "Health report saved: $report_file"
}

# Main monitoring function
main() {
    log "AutoParts API Health Monitor"
    log "============================"
    
    local issues=0
    
    # Check all services
    check_service_health "API" "autoparts_api" || ((issues++))
    check_service_health "Database" "autoparts_postgres" || ((issues++))
    check_service_health "Redis" "autoparts_redis" || ((issues++))
    
    # Check connectivity
    check_api_endpoints || ((issues++))
    check_database || ((issues++))
    check_redis || ((issues++))
    
    # Check resources
    check_disk_space
    check_memory
    check_performance
    check_logs
    
    # Summary
    echo ""
    if [ $issues -eq 0 ]; then
        success "All systems operational"
        send_alert "AutoParts API: All systems operational" "OK"
    else
        error "Found $issues issues"
        send_alert "AutoParts API: Found $issues issues requiring attention" "WARNING"
    fi
    
    # Generate report if requested
    if [ "${1:-}" = "--report" ]; then
        generate_report
    fi
}

# Handle script arguments
case "${1:-monitor}" in
    "monitor"|"")
        main "$@"
        ;;
    "report")
        generate_report
        ;;
    "logs")
        check_logs
        ;;
    "performance")
        check_performance
        ;;
    *)
        echo "Usage: $0 [monitor|report|logs|performance]"
        echo "  monitor     - Run full health check (default)"
        echo "  report      - Generate detailed health report"
        echo "  logs        - Check logs for errors"
        echo "  performance - Check performance metrics"
        exit 1
        ;;
esac
