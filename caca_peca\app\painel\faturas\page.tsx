"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  FileText,
  Download,
  TrendingUp,
  Calendar,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  BarChart3
} from "lucide-react"
import { BillingHistory } from "@/components/dealership/billing-history"
import {
  useBillingHistory,
  useCurrentSubscription,
  useSubscriptionAnalytics
} from "@/lib/hooks/useSubscription"
import { PaymentStatus } from "@/lib/types/api"
import { formatCurrency } from "@/lib/utils"

export default function BillingPage() {
  const { data: subscriptionResponse } = useCurrentSubscription()
  const { data: billingResponse } = useBillingHistory(1, 5) // Last 5 invoices for summary
  const { data: analyticsResponse } = useSubscriptionAnalytics('month')

  const subscription = subscriptionResponse?.data?.success ? subscriptionResponse.data.data : null
  const recentInvoices = billingResponse?.data?.success ? billingResponse.data.data.items : []
  const analytics = analyticsResponse?.data?.success ? analyticsResponse.data.data : null

  // Calculate summary statistics
  const totalPaid = recentInvoices
    .filter(invoice =>
      invoice.status === PaymentStatus.RECEIVED ||
      invoice.status === PaymentStatus.CONFIRMED ||
      invoice.status === PaymentStatus.RECEIVED_IN_CASH
    )
    .reduce((sum, invoice) => sum + invoice.value, 0)

  const pendingAmount = recentInvoices
    .filter(invoice =>
      invoice.status === PaymentStatus.PENDING ||
      invoice.status === PaymentStatus.OVERDUE ||
      invoice.status === PaymentStatus.AWAITING_RISK_ANALYSIS
    )
    .reduce((sum, invoice) => sum + invoice.value, 0)

  const overdueInvoices = recentInvoices.filter(invoice =>
    invoice.status === PaymentStatus.OVERDUE
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Faturas e Pagamentos</h1>
          <p className="text-muted-foreground">
            Acompanhe seu histórico de faturas, pagamentos e estatísticas financeiras
          </p>
        </div>

        <Button>
          <Download className="h-4 w-4 mr-2" />
          Exportar Relatório
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        {/* Current Subscription */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Assinatura Atual
                </p>
                <p className="text-2xl font-bold">
                  {subscription ? formatCurrency(subscription.value) : '--'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {subscription?.cycle === 'YEARLY' ? 'por ano' : 'por mês'}
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        {/* Total Paid */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Pago (Últimas 5)
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalPaid)}
                </p>
                <p className="text-xs text-muted-foreground">
                  Faturas pagas
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        {/* Pending Amount */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Valor Pendente
                </p>
                <p className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(pendingAmount)}
                </p>
                <p className="text-xs text-muted-foreground">
                  Aguardando pagamento
                </p>
              </div>
              <Calendar className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        {/* Overdue Count */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Faturas Vencidas
                </p>
                <p className="text-2xl font-bold text-red-600">
                  {overdueInvoices.length}
                </p>
                <p className="text-xs text-muted-foreground">
                  Requer atenção
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts for Overdue Invoices */}
      {overdueInvoices.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Atenção: Faturas Vencidas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700 mb-4">
              Você possui {overdueInvoices.length} fatura{overdueInvoices.length > 1 ? 's' : ''} vencida{overdueInvoices.length > 1 ? 's' : ''}.
              Para evitar a suspensão dos serviços, regularize os pagamentos o quanto antes.
            </p>
            <div className="space-y-2">
              {overdueInvoices.map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-3 bg-white rounded border">
                  <div>
                    <p className="font-medium">
                      {invoice.description || 'Assinatura AutoParts'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Vencimento: {new Date(invoice.due_date).toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-red-600">
                      {formatCurrency(invoice.value)}
                    </p>
                    <Button size="sm" className="mt-1">
                      Pagar Agora
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analytics Summary */}
      {analytics && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Estatísticas do Mês
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Receita Total</p>
                  <p className="text-xl font-bold text-green-600">
                    {formatCurrency(analytics.total_revenue)}
                  </p>
                </div>

                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Taxa de Sucesso</p>
                  <p className="text-xl font-bold">
                    {(analytics.payment_success_rate * 100).toFixed(1)}%
                  </p>
                </div>

                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Receita Média</p>
                  <p className="text-xl font-bold">
                    {formatCurrency(analytics.average_revenue_per_user)}
                  </p>
                </div>

                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Taxa de Churn</p>
                  <p className="text-xl font-bold">
                    {(analytics.churn_rate * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Métodos de Pagamento</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(analytics.payment_methods_distribution).map(([method, count]) => (
                  <div key={method} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {method === 'CREDIT_CARD' && <CreditCard className="h-4 w-4" />}
                      {method === 'BOLETO' && <FileText className="h-4 w-4" />}
                      {method === 'PIX' && <DollarSign className="h-4 w-4" />}
                      <span className="text-sm">
                        {method === 'CREDIT_CARD' ? 'Cartão de Crédito' :
                         method === 'BOLETO' ? 'Boleto' :
                         method === 'PIX' ? 'PIX' : method}
                      </span>
                    </div>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Separator />

      {/* Full Billing History */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Histórico Completo</h2>
        <BillingHistory />
      </div>
    </div>
  )
}

