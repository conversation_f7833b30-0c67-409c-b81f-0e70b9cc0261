"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

export function EnhancedModeToggle({ className }: { className?: string }) {
  const { setTheme, theme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Avoid hydration mismatch by only rendering after mount
  React.useEffect(() => {
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(resolvedTheme === "dark" ? "light" : "dark")
  }

  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="icon"
        className={cn("relative h-9 w-9", className)}
        aria-label="Carregando preferências de tema"
      >
        <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100" />
      </Button>
    )
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleTheme}
      className={cn(
        "relative h-9 w-9 overflow-hidden transition-colors",
        resolvedTheme === "dark" ? "bg-slate-800 hover:bg-slate-700" : "bg-white hover:bg-slate-100",
        className,
      )}
      aria-label={`Mudar para modo ${resolvedTheme === "dark" ? "claro" : "escuro"}`}
    >
      <Sun
        className={cn(
          "absolute h-[1.2rem] w-[1.2rem] transition-all duration-300",
          resolvedTheme === "dark" ? "rotate-90 scale-0 opacity-0" : "rotate-0 scale-100 opacity-100",
        )}
      />
      <Moon
        className={cn(
          "absolute h-[1.2rem] w-[1.2rem] transition-all duration-300",
          resolvedTheme === "dark" ? "rotate-0 scale-100 opacity-100" : "-rotate-90 scale-0 opacity-0",
        )}
      />
    </Button>
  )
}

