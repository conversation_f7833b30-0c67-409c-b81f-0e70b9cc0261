/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"945d91939972\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQ2xpZW50ZVxcRG9jdW1lbnRzXFxDdXJzb3JcXGNhY2EtcGVjYXNcXGNhY2FfcGVjYVxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk0NWQ5MTkzOTk3MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_scroll_to_top__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/scroll-to-top */ \"(rsc)/./components/scroll-to-top.tsx\");\n/* harmony import */ var _components_floating_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/floating-theme-toggle */ \"(rsc)/./components/floating-theme-toggle.tsx\");\n/* harmony import */ var _lib_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/providers/QueryProvider */ \"(rsc)/./lib/providers/QueryProvider.tsx\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/auth/context */ \"(rsc)/./lib/auth/context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"AutoParts - Encontre peças para seu veículo\",\n    description: \"Plataforma de busca de peças automotivas\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_5__.QueryProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_6__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"light\",\n                        enableSystem: true,\n                        disableTransitionOnChange: false,\n                        storageKey: \"autoparts-theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex min-h-screen flex-col bg-background text-foreground transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_scroll_to_top__WEBPACK_IMPORTED_MODULE_3__.ScrollToTop, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 17\n                                }, this),\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_floating_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.FloatingThemeToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                                    richColors: true,\n                                    position: \"top-right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQVdNQTtBQVJnQjtBQUNxQztBQUNIO0FBQ2dCO0FBQ1g7QUFDWjtBQUNqQjtBQUl6QixNQUFNTyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQVFDLHdCQUF3QjtrQkFDekMsNEVBQUNDO1lBQUtDLFdBQVdoQiwySkFBZTtzQkFDOUIsNEVBQUNJLHVFQUFhQTswQkFDWiw0RUFBQ0MsMkRBQVlBOzhCQUNYLDRFQUFDSixxRUFBYUE7d0JBQ1pnQixXQUFVO3dCQUNWQyxjQUFhO3dCQUNiQyxZQUFZO3dCQUNaQywyQkFBMkI7d0JBQzNCQyxZQUFXO2tDQUVYLDRFQUFDQzs0QkFBSU4sV0FBVTs7OENBQ2IsOERBQUNkLGtFQUFXQTs7Ozs7Z0NBQ1hTOzhDQUNELDhEQUFDUixrRkFBbUJBOzs7Ozs4Q0FDcEIsOERBQUNHLDJDQUFPQTtvQ0FBQ2lCLFVBQVU7b0NBQUNDLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRN0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQ2xpZW50ZVxcRG9jdW1lbnRzXFxDdXJzb3JcXGNhY2EtcGVjYXNcXGNhY2FfcGVjYVxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIlxuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyXCJcbmltcG9ydCB7IFNjcm9sbFRvVG9wIH0gZnJvbSBcIkAvY29tcG9uZW50cy9zY3JvbGwtdG8tdG9wXCJcbmltcG9ydCB7IEZsb2F0aW5nVGhlbWVUb2dnbGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Zsb2F0aW5nLXRoZW1lLXRvZ2dsZVwiXG5pbXBvcnQgeyBRdWVyeVByb3ZpZGVyIH0gZnJvbSBcIkAvbGliL3Byb3ZpZGVycy9RdWVyeVByb3ZpZGVyXCJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2xpYi9hdXRoL2NvbnRleHRcIlxuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gXCJzb25uZXJcIlxuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQXV0b1BhcnRzIC0gRW5jb250cmUgcGXDp2FzIHBhcmEgc2V1IHZlw61jdWxvXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlBsYXRhZm9ybWEgZGUgYnVzY2EgZGUgcGXDp2FzIGF1dG9tb3RpdmFzXCIsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwicHQtQlJcIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxRdWVyeVByb3ZpZGVyPlxuICAgICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAgICA8VGhlbWVQcm92aWRlclxuICAgICAgICAgICAgICBhdHRyaWJ1dGU9XCJjbGFzc1wiXG4gICAgICAgICAgICAgIGRlZmF1bHRUaGVtZT1cImxpZ2h0XCJcbiAgICAgICAgICAgICAgZW5hYmxlU3lzdGVtXG4gICAgICAgICAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2U9e2ZhbHNlfVxuICAgICAgICAgICAgICBzdG9yYWdlS2V5PVwiYXV0b3BhcnRzLXRoZW1lXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLXNjcmVlbiBmbGV4LWNvbCBiZy1iYWNrZ3JvdW5kIHRleHQtZm9yZWdyb3VuZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICA8U2Nyb2xsVG9Ub3AgLz5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgPEZsb2F0aW5nVGhlbWVUb2dnbGUgLz5cbiAgICAgICAgICAgICAgICA8VG9hc3RlciByaWNoQ29sb3JzIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICAgIDwvUXVlcnlQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cblxuIl0sIm5hbWVzIjpbImludGVyIiwiVGhlbWVQcm92aWRlciIsIlNjcm9sbFRvVG9wIiwiRmxvYXRpbmdUaGVtZVRvZ2dsZSIsIlF1ZXJ5UHJvdmlkZXIiLCJBdXRoUHJvdmlkZXIiLCJUb2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiYXR0cmlidXRlIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSIsInN0b3JhZ2VLZXkiLCJkaXYiLCJyaWNoQ29sb3JzIiwicG9zaXRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxDbGllbnRlXFxEb2N1bWVudHNcXEN1cnNvclxcY2FjYS1wZWNhc1xcY2FjYV9wZWNhXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiBudWxsXG59XG5cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./components/floating-theme-toggle.tsx":
/*!**********************************************!*\
  !*** ./components/floating-theme-toggle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FloatingThemeToggle: () => (/* binding */ FloatingThemeToggle)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const FloatingThemeToggle = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call FloatingThemeToggle() from the server but FloatingThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Cursor\\caca-pecas\\caca_peca\\components\\floating-theme-toggle.tsx",
"FloatingThemeToggle",
);

/***/ }),

/***/ "(rsc)/./components/scroll-to-top.tsx":
/*!**************************************!*\
  !*** ./components/scroll-to-top.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScrollToTop: () => (/* binding */ ScrollToTop)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ScrollToTop = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ScrollToTop() from the server but ScrollToTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Cursor\\caca-pecas\\caca_peca\\components\\scroll-to-top.tsx",
"ScrollToTop",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Cursor\\caca-pecas\\caca_peca\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./lib/auth/context.tsx":
/*!******************************!*\
  !*** ./lib/auth/context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRole: () => (/* binding */ useRole),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n\"AuthProvider\",\n);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n\"useAuth\",\n);const withAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n\"withAuth\",\n);const useRole = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useRole() from the server but useRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n\"useRole\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth/context.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/providers/QueryProvider.tsx":
/*!*****************************************!*\
  !*** ./lib/providers/QueryProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\nconst QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\providers\\\\QueryProvider.tsx\",\n\"QueryProvider\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cfloating-theme-toggle.tsx%22%2C%22ids%22%3A%5B%22FloatingThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cscroll-to-top.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cauth%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cfloating-theme-toggle.tsx%22%2C%22ids%22%3A%5B%22FloatingThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cscroll-to-top.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cauth%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/floating-theme-toggle.tsx */ \"(rsc)/./components/floating-theme-toggle.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/scroll-to-top.tsx */ \"(rsc)/./components/scroll-to-top.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth/context.tsx */ \"(rsc)/./lib/auth/context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/providers/QueryProvider.tsx */ \"(rsc)/./lib/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cfloating-theme-toggle.tsx%22%2C%22ids%22%3A%5B%22FloatingThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cscroll-to-top.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cauth%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/floating-theme-toggle.tsx":
/*!**********************************************!*\
  !*** ./components/floating-theme-toggle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingThemeToggle: () => (/* binding */ FloatingThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _theme_toggle_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./theme-toggle-button */ \"(ssr)/./components/theme-toggle-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ FloatingThemeToggle auto */ \n\n\nfunction FloatingThemeToggle() {\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"FloatingThemeToggle.useEffect\": ()=>{\n            // Show the floating toggle after scrolling down\n            const handleScroll = {\n                \"FloatingThemeToggle.useEffect.handleScroll\": ()=>{\n                    if (window.scrollY > 300) {\n                        setVisible(true);\n                    } else {\n                        setVisible(false);\n                    }\n                }\n            }[\"FloatingThemeToggle.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"FloatingThemeToggle.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"FloatingThemeToggle.useEffect\"];\n        }\n    }[\"FloatingThemeToggle.useEffect\"], []);\n    if (!visible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 md:bottom-6 md:right-6 transition-opacity duration-300 opacity-80 hover:opacity-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_toggle_button__WEBPACK_IMPORTED_MODULE_2__.ThemeToggleButton, {\n            className: \"shadow-lg border border-border\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\floating-theme-toggle.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\floating-theme-toggle.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/floating-theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./components/scroll-to-top.tsx":
/*!**************************************!*\
  !*** ./components/scroll-to-top.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollToTop: () => (/* binding */ ScrollToTop)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ ScrollToTop auto */ \n\nfunction ScrollToTop() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ScrollToTop.useEffect\": ()=>{\n            // Quando o pathname mudar, role para o topo\n            window.scrollTo(0, 0);\n        }\n    }[\"ScrollToTop.useEffect\"], [\n        pathname\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Njcm9sbC10by10b3AudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7aUVBRWlDO0FBQ1k7QUFFdEMsU0FBU0U7SUFDZCxNQUFNQyxXQUFXRiw0REFBV0E7SUFFNUJELGdEQUFTQTtpQ0FBQztZQUNSLDRDQUE0QztZQUM1Q0ksT0FBT0MsUUFBUSxDQUFDLEdBQUc7UUFDckI7Z0NBQUc7UUFBQ0Y7S0FBUztJQUViLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxDbGllbnRlXFxEb2N1bWVudHNcXEN1cnNvclxcY2FjYS1wZWNhc1xcY2FjYV9wZWNhXFxjb21wb25lbnRzXFxzY3JvbGwtdG8tdG9wLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFNjcm9sbFRvVG9wKCkge1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFF1YW5kbyBvIHBhdGhuYW1lIG11ZGFyLCByb2xlIHBhcmEgbyB0b3BvXG4gICAgd2luZG93LnNjcm9sbFRvKDAsIDApXG4gIH0sIFtwYXRobmFtZV0pXG5cbiAgcmV0dXJuIG51bGxcbn1cblxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVBhdGhuYW1lIiwiU2Nyb2xsVG9Ub3AiLCJwYXRobmFtZSIsIndpbmRvdyIsInNjcm9sbFRvIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/scroll-to-top.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNpRTtBQUcxRCxTQUFTQSxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQ2xpZW50ZVxcRG9jdW1lbnRzXFxDdXJzb3JcXGNhY2EtcGVjYXNcXGNhY2FfcGVjYVxcY29tcG9uZW50c1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5pbXBvcnQgdHlwZSB7IFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cblxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-toggle-button.tsx":
/*!********************************************!*\
  !*** ./components/theme-toggle-button.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggleButton: () => (/* binding */ ThemeToggleButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggleButton auto */ \n\n\n\n\n\nfunction ThemeToggleButton({ className, variant = \"icon\" }) {\n    const { setTheme, theme, resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Only render the toggle on the client to avoid hydration mismatch\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ThemeToggleButton.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeToggleButton.useEffect\"], []);\n    if (!mounted) {\n        return variant === \"icon\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n            variant: \"ghost\",\n            size: \"icon\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-10 h-10\", className)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n            lineNumber: 24,\n            columnNumber: 33\n        }, this) : null;\n    }\n    const isDark = resolvedTheme === \"dark\";\n    const toggleTheme = ()=>{\n        console.log(\"Current theme:\", theme);\n        console.log(\"Resolved theme:\", resolvedTheme);\n        setTheme(isDark ? \"light\" : \"dark\");\n        console.log(\"Setting theme to:\", isDark ? \"light\" : \"dark\");\n    };\n    if (variant === \"minimal\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: toggleTheme,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"p-2 rounded-md hover:bg-accent\", className),\n            \"aria-label\": isDark ? \"Mudar para modo claro\" : \"Mudar para modo escuro\",\n            children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n                lineNumber: 43,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n                lineNumber: 43,\n                columnNumber: 49\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    if (variant === \"button\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n            variant: \"outline\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center gap-2\", className),\n            onClick: toggleTheme,\n            children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Modo Claro\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Modo Escuro\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    // Default icon variant\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-10 h-10 rounded-full\", isDark ? \"hover:bg-slate-800\" : \"hover:bg-slate-200\", className),\n        onClick: toggleTheme,\n        \"aria-label\": isDark ? \"Mudar para modo claro\" : \"Mudar para modo escuro\",\n        children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5 text-yellow-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n            lineNumber: 75,\n            columnNumber: 17\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5 text-slate-700\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n            lineNumber: 75,\n            columnNumber: 63\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\theme-toggle-button.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-toggle-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api/auth.ts":
/*!*************************!*\
  !*** ./lib/api/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./lib/api/client.ts\");\n/**\n * Authentication API Service\n * Handles all authentication-related API calls\n */ \nclass AuthService {\n    /**\n   * Login user with email and password\n   */ async login(credentials) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.login(credentials);\n    }\n    /**\n   * Logout current user\n   */ async logout() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.logout();\n    }\n    /**\n   * Register new user\n   */ async register(userData) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.register(userData);\n    }\n    /**\n   * Get current authenticated user\n   */ async getCurrentUser() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.getCurrentUser();\n    }\n    /**\n   * Refresh access token\n   */ async refreshToken(refreshToken) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'POST',\n            url: '/auth/refresh',\n            data: {\n                refresh_token: refreshToken\n            }\n        });\n    }\n    /**\n   * Request password reset\n   */ async requestPasswordReset(email) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'POST',\n            url: '/auth/password-reset',\n            data: {\n                email\n            }\n        });\n    }\n    /**\n   * Confirm password reset with token\n   */ async confirmPasswordReset(token, newPassword) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'POST',\n            url: '/auth/password-reset/confirm',\n            data: {\n                token,\n                new_password: newPassword\n            }\n        });\n    }\n    /**\n   * Change password for authenticated user\n   */ async changePassword(currentPassword, newPassword) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'POST',\n            url: '/auth/change-password',\n            data: {\n                current_password: currentPassword,\n                new_password: newPassword\n            }\n        });\n    }\n    /**\n   * Generate API key for authenticated user\n   */ async generateApiKey() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'POST',\n            url: '/auth/api-key'\n        });\n    }\n    /**\n   * Revoke API key for authenticated user\n   */ async revokeApiKey() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'DELETE',\n            url: '/auth/api-key'\n        });\n    }\n    /**\n   * Update user profile\n   */ async updateProfile(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.request({\n            method: 'PUT',\n            url: '/auth/me',\n            data\n        });\n    }\n    /**\n   * Check if user is authenticated\n   */ isAuthenticated() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.isAuthenticated();\n    }\n    /**\n   * Get current access token\n   */ getAccessToken() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.getAccessToken();\n    }\n}\n// Export singleton instance\nconst authService = new AuthService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api/client.ts":
/*!***************************!*\
  !*** ./lib/api/client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../auth/jwt-service */ \"(ssr)/./lib/auth/jwt-service.ts\");\n/**\n * AutoParts API Client\n * Comprehensive TypeScript client for the AutoParts backend API\n */ \n\n// Configuration\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = '/api/v1';\nclass AutoPartsAPIClient {\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: `${API_BASE_URL}${API_VERSION}`,\n            timeout: 30000,\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const authHeader = _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.getAuthorizationHeader();\n            if (authHeader) {\n                config.headers.Authorization = authHeader;\n            }\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Response interceptor for token refresh\n        this.client.interceptors.response.use((response)=>response, async (error)=>{\n            const originalRequest = error.config;\n            if (error.response?.status === 401 && !originalRequest._retry) {\n                originalRequest._retry = true;\n                try {\n                    // Check if token should be refreshed\n                    if (_auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.shouldRefreshToken()) {\n                        await _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.refreshAccessToken();\n                        const newAuthHeader = _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.getAuthorizationHeader();\n                        if (newAuthHeader) {\n                            originalRequest.headers.Authorization = newAuthHeader;\n                        }\n                        return this.client(originalRequest);\n                    } else {\n                        throw new Error('Token refresh not needed or failed');\n                    }\n                } catch (refreshError) {\n                    _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.clearTokens();\n                    // Redirect to login or emit auth error event\n                    if (false) {}\n                    return Promise.reject(refreshError);\n                }\n            }\n            return Promise.reject(error);\n        });\n    }\n    // Authentication methods\n    async login(credentials) {\n        try {\n            const response = await this.client.post('/auth/login', credentials);\n            const loginData = response.data;\n            // Store tokens using JWT service\n            _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.setTokens(loginData.access_token, loginData.refresh_token);\n            // Schedule automatic token refresh\n            _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.scheduleTokenRefresh();\n            return {\n                data: loginData,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async logout() {\n        try {\n            await this.client.post('/auth/logout');\n            _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.clearTokens();\n            return {\n                success: true\n            };\n        } catch (error) {\n            _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.clearTokens(); // Clear tokens even if logout fails\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async register(userData) {\n        try {\n            const response = await this.client.post('/auth/register', userData);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async getCurrentUser() {\n        try {\n            const response = await this.client.get('/auth/me');\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    // Utility methods\n    isAuthenticated() {\n        return _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.isAuthenticated();\n    }\n    getAccessToken() {\n        return _auth_jwt_service__WEBPACK_IMPORTED_MODULE_0__.jwtService.getAccessToken();\n    }\n    // HTTP methods\n    async get(url, config) {\n        try {\n            const response = await this.client.get(url, config);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async post(url, data, config) {\n        try {\n            const response = await this.client.post(url, data, config);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async put(url, data, config) {\n        try {\n            const response = await this.client.put(url, data, config);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async patch(url, data, config) {\n        try {\n            const response = await this.client.patch(url, data, config);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    async delete(url, config) {\n        try {\n            const response = await this.client.delete(url, config);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    // Generic request method\n    async request(config) {\n        try {\n            const response = await this.client(config);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n    // Error handling\n    handleError(error) {\n        if (error.response) {\n            // Server responded with error status\n            return {\n                error: error.response.data?.error || 'API Error',\n                message: error.response.data?.message || error.message,\n                details: error.response.data?.details,\n                timestamp: Date.now()\n            };\n        } else if (error.request) {\n            // Request was made but no response received\n            return {\n                error: 'Network Error',\n                message: 'Unable to connect to the server. Please check your internet connection.',\n                timestamp: Date.now()\n            };\n        } else {\n            // Something else happened\n            return {\n                error: 'Unknown Error',\n                message: error.message || 'An unexpected error occurred',\n                timestamp: Date.now()\n            };\n        }\n    }\n    // Health check\n    async healthCheck() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`${API_BASE_URL}/health`);\n            return {\n                data: response.data,\n                success: true\n            };\n        } catch (error) {\n            return {\n                error: this.handleError(error),\n                success: false\n            };\n        }\n    }\n}\n// Export singleton instance\nconst apiClient = new AutoPartsAPIClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth/context.tsx":
/*!******************************!*\
  !*** ./lib/auth/context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRole: () => (/* binding */ useRole),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../stores/auth-store */ \"(ssr)/./lib/stores/auth-store.ts\");\n/**\n * Authentication Context\n * Provides authentication state and methods throughout the app\n * Now integrated with Zustand store for better state management\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useRole auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const { user, isLoading, isAuthenticated, error, login, logout, register, refreshUser, clearError, getRedirectPath, hasRole, isAdmin, isDealership, isCustomer } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    // Initialize auth state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initialize = {\n                \"AuthProvider.useEffect.initialize\": async ()=>{\n                    await (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.initializeAuth)();\n                    (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.startTokenRefresh)();\n                }\n            }[\"AuthProvider.useEffect.initialize\"];\n            initialize();\n            // Cleanup on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.stopTokenRefresh)();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        error,\n        login,\n        logout,\n        register,\n        refreshUser,\n        clearError,\n        getRedirectPath,\n        hasRole,\n        isAdmin,\n        isDealership,\n        isCustomer\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, isLoading } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            // Redirect to login page\n            if (false) {}\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\auth\\\\context.tsx\",\n            lineNumber: 122,\n            columnNumber: 12\n        }, this);\n    };\n}\n// Hook for role-based access control\nfunction useRole() {\n    const { user, hasRole, isAdmin, isDealership, isCustomer } = useAuth();\n    return {\n        isAdmin: isAdmin(),\n        isDealership: isDealership(),\n        isCustomer: isCustomer(),\n        role: user?.role,\n        hasRole\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth/context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/auth/jwt-service.ts":
/*!*********************************!*\
  !*** ./lib/auth/jwt-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JWTService: () => (/* binding */ JWTService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   jwtService: () => (/* binding */ jwtService)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(ssr)/./node_modules/jwt-decode/build/esm/index.js\");\n/**\r\n * JWT Authentication Service\r\n * Enhanced JWT token management with automatic refresh and secure storage\r\n */ \n\n// Token storage configuration\nconst TOKEN_CONFIG = {\n    ACCESS_TOKEN_KEY: 'autoparts_access_token',\n    REFRESH_TOKEN_KEY: 'autoparts_refresh_token',\n    ACCESS_TOKEN_EXPIRY: 1 / 24,\n    REFRESH_TOKEN_EXPIRY: 7,\n    REFRESH_THRESHOLD: 5 * 60 * 1000\n};\nclass JWTService {\n    constructor(){\n        this.accessToken = null;\n        this.refreshToken = null;\n        this.refreshPromise = null;\n        this.loadTokensFromStorage();\n    }\n    /**\r\n   * Load tokens from secure storage (cookies)\r\n   */ loadTokensFromStorage() {\n        if (false) {}\n    }\n    /**\r\n   * Save tokens to secure storage with appropriate security settings\r\n   */ setTokens(accessToken, refreshToken) {\n        this.accessToken = accessToken;\n        this.refreshToken = refreshToken;\n        if (false) {}\n    }\n    /**\r\n   * Clear all tokens from storage\r\n   */ clearTokens() {\n        this.accessToken = null;\n        this.refreshToken = null;\n        if (false) {}\n    }\n    /**\r\n   * Get current access token\r\n   */ getAccessToken() {\n        return this.accessToken;\n    }\n    /**\r\n   * Get current refresh token\r\n   */ getRefreshToken() {\n        return this.refreshToken;\n    }\n    /**\r\n   * Check if user is authenticated (has valid tokens)\r\n   */ isAuthenticated() {\n        return !!this.accessToken && !!this.refreshToken && !this.isTokenExpired(this.accessToken);\n    }\n    /**\r\n   * Check if a token is expired\r\n   */ isTokenExpired(token) {\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            const currentTime = Date.now() / 1000;\n            return decoded.exp < currentTime;\n        } catch (error) {\n            return true; // Consider invalid tokens as expired\n        }\n    }\n    /**\r\n   * Check if access token needs refresh (expires within threshold)\r\n   */ shouldRefreshToken() {\n        if (!this.accessToken) return false;\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(this.accessToken);\n            const currentTime = Date.now() / 1000;\n            const timeUntilExpiry = (decoded.exp - currentTime) * 1000;\n            return timeUntilExpiry < TOKEN_CONFIG.REFRESH_THRESHOLD;\n        } catch (error) {\n            return true; // Refresh if token is invalid\n        }\n    }\n    /**\r\n   * Get user information from access token\r\n   */ getTokenPayload() {\n        if (!this.accessToken) return null;\n        try {\n            return (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(this.accessToken);\n        } catch (error) {\n            console.error('Failed to decode token:', error);\n            return null;\n        }\n    }\n    /**\r\n   * Get user ID from token\r\n   */ getUserId() {\n        const payload = this.getTokenPayload();\n        return payload?.sub || null;\n    }\n    /**\r\n   * Get user email from token\r\n   */ getUserEmail() {\n        const payload = this.getTokenPayload();\n        return payload?.email || null;\n    }\n    /**\r\n   * Get user role from token\r\n   */ getUserRole() {\n        const payload = this.getTokenPayload();\n        return payload?.role || null;\n    }\n    /**\r\n   * Get token expiration time\r\n   */ getTokenExpiration() {\n        const payload = this.getTokenPayload();\n        if (!payload) return null;\n        return new Date(payload.exp * 1000);\n    }\n    /**\r\n   * Get time until token expires (in milliseconds)\r\n   */ getTimeUntilExpiry() {\n        const expiration = this.getTokenExpiration();\n        if (!expiration) return null;\n        return expiration.getTime() - Date.now();\n    }\n    /**\r\n   * Refresh access token using refresh token\r\n   * Returns a promise that resolves to the new access token\r\n   */ async refreshAccessToken() {\n        // Prevent multiple simultaneous refresh requests\n        if (this.refreshPromise) {\n            return this.refreshPromise;\n        }\n        if (!this.refreshToken) {\n            throw new Error('No refresh token available');\n        }\n        this.refreshPromise = this.performTokenRefresh();\n        try {\n            const newAccessToken = await this.refreshPromise;\n            return newAccessToken;\n        } finally{\n            this.refreshPromise = null;\n        }\n    }\n    /**\r\n   * Perform the actual token refresh API call\r\n   */ async performTokenRefresh() {\n        const response = await fetch(`${\"http://localhost:8000\"}/api/v1/auth/refresh`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                refresh_token: this.refreshToken\n            })\n        });\n        if (!response.ok) {\n            throw new Error('Token refresh failed');\n        }\n        const data = await response.json();\n        // Update access token\n        this.accessToken = data.access_token;\n        if (false) {}\n        return this.accessToken;\n    }\n    /**\r\n   * Schedule automatic token refresh\r\n   */ scheduleTokenRefresh() {\n        const timeUntilExpiry = this.getTimeUntilExpiry();\n        if (!timeUntilExpiry) return;\n        // Schedule refresh 5 minutes before expiry\n        const refreshTime = Math.max(timeUntilExpiry - TOKEN_CONFIG.REFRESH_THRESHOLD, 0);\n        setTimeout(async ()=>{\n            try {\n                await this.refreshAccessToken();\n                // Schedule next refresh\n                this.scheduleTokenRefresh();\n            } catch (error) {\n                console.error('Scheduled token refresh failed:', error);\n                // Clear tokens if refresh fails\n                this.clearTokens();\n            }\n        }, refreshTime);\n    }\n    /**\r\n   * Validate token format and structure\r\n   */ validateToken(token) {\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n            // Check required fields\n            return !!(decoded.sub && decoded.email && decoded.role && decoded.exp && decoded.iat);\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\r\n   * Get authorization header value\r\n   */ getAuthorizationHeader() {\n        if (!this.accessToken) return null;\n        return `Bearer ${this.accessToken}`;\n    }\n    /**\r\n   * Check if current user has specific role\r\n   */ hasRole(role) {\n        const userRole = this.getUserRole();\n        return userRole === role;\n    }\n    /**\r\n   * Check if current user is admin\r\n   */ isAdmin() {\n        return this.hasRole('admin');\n    }\n    /**\r\n   * Check if current user is dealership\r\n   */ isDealership() {\n        return this.hasRole('dealership');\n    }\n    /**\r\n   * Check if current user is customer\r\n   */ isCustomer() {\n        return this.hasRole('customer');\n    }\n}\n// Export singleton instance\nconst jwtService = new JWTService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (jwtService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth/jwt-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/providers/QueryProvider.tsx":
/*!*****************************************!*\
  !*** ./lib/providers/QueryProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/**\n * React Query Provider\n * Configures React Query for the application\n */ /* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\n\n// Create a client\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401, 403, 404\n                if (error?.response?.status === 401 || error?.response?.status === 403 || error?.response?.status === 404) {\n                    return false;\n                }\n                // Retry up to 3 times for other errors\n                return failureCount < 3;\n            },\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000,\n            refetchOnWindowFocus: false,\n            refetchOnReconnect: true\n        },\n        mutations: {\n            retry: false\n        }\n    }\n});\nfunction QueryProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\providers\\\\QueryProvider.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cursor\\\\caca-pecas\\\\caca_peca\\\\lib\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvcHJvdmlkZXJzL1F1ZXJ5UHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7OztDQUdDO0FBSXdDO0FBQ2dDO0FBQ0w7QUFFcEUsa0JBQWtCO0FBQ2xCLE1BQU1JLGNBQWMsSUFBSUgsOERBQVdBLENBQUM7SUFDbENJLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLE9BQU8sQ0FBQ0MsY0FBY0M7Z0JBQ3BCLCtCQUErQjtnQkFDL0IsSUFBSUEsT0FBT0MsVUFBVUMsV0FBVyxPQUM1QkYsT0FBT0MsVUFBVUMsV0FBVyxPQUM1QkYsT0FBT0MsVUFBVUMsV0FBVyxLQUFLO29CQUNuQyxPQUFPO2dCQUNUO2dCQUNBLHVDQUF1QztnQkFDdkMsT0FBT0gsZUFBZTtZQUN4QjtZQUNBSSxXQUFXLElBQUksS0FBSztZQUNwQkMsUUFBUSxLQUFLLEtBQUs7WUFDbEJDLHNCQUFzQjtZQUN0QkMsb0JBQW9CO1FBQ3RCO1FBQ0FDLFdBQVc7WUFDVFQsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQU1PLFNBQVNVLGNBQWMsRUFBRUMsUUFBUSxFQUFzQjtJQUM1RCxxQkFDRSw4REFBQ2hCLHNFQUFtQkE7UUFBQ2lCLFFBQVFmOztZQUMxQmM7WUFmUSxLQWdCOEIsa0JBQ3JDLDhEQUFDZiw4RUFBa0JBO2dCQUFDaUIsZUFBZTs7Ozs7Ozs7Ozs7O0FBSTNDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXENsaWVudGVcXERvY3VtZW50c1xcQ3Vyc29yXFxjYWNhLXBlY2FzXFxjYWNhX3BlY2FcXGxpYlxccHJvdmlkZXJzXFxRdWVyeVByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlYWN0IFF1ZXJ5IFByb3ZpZGVyXG4gKiBDb25maWd1cmVzIFJlYWN0IFF1ZXJ5IGZvciB0aGUgYXBwbGljYXRpb25cbiAqL1xuXG4ndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnO1xuXG4vLyBDcmVhdGUgYSBjbGllbnRcbmNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICByZXRyeTogKGZhaWx1cmVDb3VudCwgZXJyb3I6IGFueSkgPT4ge1xuICAgICAgICAvLyBEb24ndCByZXRyeSBvbiA0MDEsIDQwMywgNDA0XG4gICAgICAgIGlmIChlcnJvcj8ucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxIHx8XG4gICAgICAgICAgICBlcnJvcj8ucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAzIHx8XG4gICAgICAgICAgICBlcnJvcj8ucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIC8vIFJldHJ5IHVwIHRvIDMgdGltZXMgZm9yIG90aGVyIGVycm9yc1xuICAgICAgICByZXR1cm4gZmFpbHVyZUNvdW50IDwgMztcbiAgICAgIH0sXG4gICAgICBzdGFsZVRpbWU6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xuICAgICAgZ2NUaW1lOiAxMCAqIDYwICogMTAwMCwgLy8gMTAgbWludXRlcyAocmVuYW1lZCBmcm9tIGNhY2hlVGltZSlcbiAgICAgIHJlZmV0Y2hPbldpbmRvd0ZvY3VzOiBmYWxzZSxcbiAgICAgIHJlZmV0Y2hPblJlY29ubmVjdDogdHJ1ZSxcbiAgICB9LFxuICAgIG11dGF0aW9uczoge1xuICAgICAgcmV0cnk6IGZhbHNlLFxuICAgIH0sXG4gIH0sXG59KTtcblxuaW50ZXJmYWNlIFF1ZXJ5UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBRdWVyeVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogUXVlcnlQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgICB7cHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgKFxuICAgICAgICA8UmVhY3RRdWVyeURldnRvb2xzIGluaXRpYWxJc09wZW49e2ZhbHNlfSAvPlxuICAgICAgKX1cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJSZWFjdFF1ZXJ5RGV2dG9vbHMiLCJxdWVyeUNsaWVudCIsImRlZmF1bHRPcHRpb25zIiwicXVlcmllcyIsInJldHJ5IiwiZmFpbHVyZUNvdW50IiwiZXJyb3IiLCJyZXNwb25zZSIsInN0YXR1cyIsInN0YWxlVGltZSIsImdjVGltZSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwicmVmZXRjaE9uUmVjb25uZWN0IiwibXV0YXRpb25zIiwiUXVlcnlQcm92aWRlciIsImNoaWxkcmVuIiwiY2xpZW50IiwiaW5pdGlhbElzT3BlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./lib/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeAuth: () => (/* binding */ initializeAuth),\n/* harmony export */   startTokenRefresh: () => (/* binding */ startTokenRefresh),\n/* harmony export */   stopTokenRefresh: () => (/* binding */ stopTokenRefresh),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api/auth */ \"(ssr)/./lib/api/auth.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/api */ \"(ssr)/./lib/types/api.ts\");\n/**\r\n * Authentication Store using Zustand\r\n * Manages authentication state and provides auth actions\r\n */ \n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        // Initial state\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        // Actions\n        login: async (credentials)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.login(credentials);\n                if (response.success && response.data) {\n                    const user = response.data.user;\n                    set({\n                        user,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        error: null\n                    });\n                    const redirectTo = get().getRedirectPath(user.role);\n                    return {\n                        success: true,\n                        redirectTo\n                    };\n                } else {\n                    const errorMessage = response.error?.message || 'Login failed';\n                    set({\n                        isLoading: false,\n                        error: errorMessage,\n                        isAuthenticated: false,\n                        user: null\n                    });\n                    return {\n                        success: false,\n                        error: errorMessage\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error.message || 'An unexpected error occurred';\n                set({\n                    isLoading: false,\n                    error: errorMessage,\n                    isAuthenticated: false,\n                    user: null\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        logout: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                await _api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.logout();\n            } catch (error) {\n                console.error('Logout error:', error);\n            } finally{\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: null\n                });\n            }\n        },\n        register: async (userData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.register(userData);\n                if (response.success) {\n                    set({\n                        isLoading: false,\n                        error: null\n                    });\n                    return {\n                        success: true\n                    };\n                } else {\n                    const errorMessage = response.error?.message || 'Registration failed';\n                    set({\n                        isLoading: false,\n                        error: errorMessage\n                    });\n                    return {\n                        success: false,\n                        error: errorMessage\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error.message || 'An unexpected error occurred';\n                set({\n                    isLoading: false,\n                    error: errorMessage\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        refreshUser: async ()=>{\n            if (!_api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.isAuthenticated()) {\n                set({\n                    user: null,\n                    isAuthenticated: false\n                });\n                return;\n            }\n            try {\n                const response = await _api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                if (response.success && response.data) {\n                    set({\n                        user: response.data,\n                        isAuthenticated: true,\n                        error: null\n                    });\n                } else {\n                    // Token might be invalid, clear auth state\n                    set({\n                        user: null,\n                        isAuthenticated: false,\n                        error: 'Session expired'\n                    });\n                }\n            } catch (error) {\n                console.error('Failed to refresh user:', error);\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    error: 'Failed to refresh user session'\n                });\n            }\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        // Utilities\n        getRedirectPath: (role)=>{\n            switch(role){\n                case _types_api__WEBPACK_IMPORTED_MODULE_1__.UserRole.ADMIN:\n                    return '/admin';\n                case _types_api__WEBPACK_IMPORTED_MODULE_1__.UserRole.DEALERSHIP:\n                    return '/painel';\n                case _types_api__WEBPACK_IMPORTED_MODULE_1__.UserRole.CUSTOMER:\n                    return '/';\n                default:\n                    return '/';\n            }\n        },\n        hasRole: (role)=>{\n            const { user } = get();\n            return user?.role === role;\n        },\n        isAdmin: ()=>{\n            return get().hasRole(_types_api__WEBPACK_IMPORTED_MODULE_1__.UserRole.ADMIN);\n        },\n        isDealership: ()=>{\n            return get().hasRole(_types_api__WEBPACK_IMPORTED_MODULE_1__.UserRole.DEALERSHIP);\n        },\n        isCustomer: ()=>{\n            return get().hasRole(_types_api__WEBPACK_IMPORTED_MODULE_1__.UserRole.CUSTOMER);\n        }\n    }), {\n    name: 'autoparts-auth-storage',\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n// Initialize auth state on app load\nconst initializeAuth = async ()=>{\n    const { refreshUser, setLoading } = useAuthStore.getState();\n    setLoading(true);\n    try {\n        if (_api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.isAuthenticated()) {\n            await refreshUser();\n        }\n    } catch (error) {\n        console.error('Failed to initialize auth:', error);\n    } finally{\n        setLoading(false);\n    }\n};\n// Set up automatic token refresh\nlet refreshInterval = null;\nconst startTokenRefresh = ()=>{\n    if (refreshInterval) {\n        clearInterval(refreshInterval);\n    }\n    refreshInterval = setInterval(async ()=>{\n        const { isAuthenticated, refreshUser } = useAuthStore.getState();\n        if (isAuthenticated && _api_auth__WEBPACK_IMPORTED_MODULE_0__.authService.isAuthenticated()) {\n            try {\n                await refreshUser();\n            } catch (error) {\n                console.error('Token refresh failed:', error);\n            }\n        }\n    }, 15 * 60 * 1000); // Refresh every 15 minutes\n};\nconst stopTokenRefresh = ()=>{\n    if (refreshInterval) {\n        clearInterval(refreshInterval);\n        refreshInterval = null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/auth-store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/types/api.ts":
/*!**************************!*\
  !*** ./lib/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BillingCycle: () => (/* binding */ BillingCycle),\n/* harmony export */   BillingType: () => (/* binding */ BillingType),\n/* harmony export */   ImportStatus: () => (/* binding */ ImportStatus),\n/* harmony export */   InventoryCondition: () => (/* binding */ InventoryCondition),\n/* harmony export */   PaymentStatus: () => (/* binding */ PaymentStatus),\n/* harmony export */   SubscriptionPlan: () => (/* binding */ SubscriptionPlan),\n/* harmony export */   SubscriptionStatus: () => (/* binding */ SubscriptionStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   UserStatus: () => (/* binding */ UserStatus)\n/* harmony export */ });\n/**\n * TypeScript types for AutoParts API\n * Generated from backend Pydantic schemas\n */ // Base types\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"DEALERSHIP\"] = \"dealership\";\n    UserRole[\"CUSTOMER\"] = \"customer\";\n    return UserRole;\n}({});\nvar UserStatus = /*#__PURE__*/ function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n    return UserStatus;\n}({});\nvar InventoryCondition = /*#__PURE__*/ function(InventoryCondition) {\n    InventoryCondition[\"NEW\"] = \"new\";\n    InventoryCondition[\"USED\"] = \"used\";\n    InventoryCondition[\"REFURBISHED\"] = \"refurbished\";\n    InventoryCondition[\"DAMAGED\"] = \"damaged\";\n    return InventoryCondition;\n}({});\nvar SubscriptionPlan = /*#__PURE__*/ function(SubscriptionPlan) {\n    SubscriptionPlan[\"BASIC\"] = \"BASIC\";\n    SubscriptionPlan[\"PREMIUM\"] = \"PREMIUM\";\n    SubscriptionPlan[\"ENTERPRISE\"] = \"ENTERPRISE\";\n    return SubscriptionPlan;\n}({});\nvar BillingType = /*#__PURE__*/ function(BillingType) {\n    BillingType[\"BOLETO\"] = \"BOLETO\";\n    BillingType[\"CREDIT_CARD\"] = \"CREDIT_CARD\";\n    BillingType[\"PIX\"] = \"PIX\";\n    return BillingType;\n}({});\nvar BillingCycle = /*#__PURE__*/ function(BillingCycle) {\n    BillingCycle[\"MONTHLY\"] = \"MONTHLY\";\n    BillingCycle[\"YEARLY\"] = \"YEARLY\";\n    return BillingCycle;\n}({});\nvar PaymentStatus = /*#__PURE__*/ function(PaymentStatus) {\n    PaymentStatus[\"PENDING\"] = \"PENDING\";\n    PaymentStatus[\"RECEIVED\"] = \"RECEIVED\";\n    PaymentStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n    PaymentStatus[\"OVERDUE\"] = \"OVERDUE\";\n    PaymentStatus[\"REFUNDED\"] = \"REFUNDED\";\n    PaymentStatus[\"RECEIVED_IN_CASH\"] = \"RECEIVED_IN_CASH\";\n    PaymentStatus[\"REFUND_REQUESTED\"] = \"REFUND_REQUESTED\";\n    PaymentStatus[\"REFUND_IN_PROGRESS\"] = \"REFUND_IN_PROGRESS\";\n    PaymentStatus[\"CHARGEBACK_REQUESTED\"] = \"CHARGEBACK_REQUESTED\";\n    PaymentStatus[\"CHARGEBACK_DISPUTE\"] = \"CHARGEBACK_DISPUTE\";\n    PaymentStatus[\"AWAITING_CHARGEBACK_REVERSAL\"] = \"AWAITING_CHARGEBACK_REVERSAL\";\n    PaymentStatus[\"DUNNING_REQUESTED\"] = \"DUNNING_REQUESTED\";\n    PaymentStatus[\"DUNNING_RECEIVED\"] = \"DUNNING_RECEIVED\";\n    PaymentStatus[\"AWAITING_RISK_ANALYSIS\"] = \"AWAITING_RISK_ANALYSIS\";\n    return PaymentStatus;\n}({});\nvar SubscriptionStatus = /*#__PURE__*/ function(SubscriptionStatus) {\n    SubscriptionStatus[\"ACTIVE\"] = \"active\";\n    SubscriptionStatus[\"INACTIVE\"] = \"inactive\";\n    SubscriptionStatus[\"PAST_DUE\"] = \"past_due\";\n    SubscriptionStatus[\"CANCELED\"] = \"canceled\";\n    SubscriptionStatus[\"UNPAID\"] = \"unpaid\";\n    return SubscriptionStatus;\n}({});\nvar ImportStatus = /*#__PURE__*/ function(ImportStatus) {\n    ImportStatus[\"PENDING\"] = \"pending\";\n    ImportStatus[\"PROCESSING\"] = \"processing\";\n    ImportStatus[\"COMPLETED\"] = \"completed\";\n    ImportStatus[\"FAILED\"] = \"failed\";\n    return ImportStatus;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/types/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value) {\n    return new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n    }).format(value);\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('pt-BR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n    }).format(dateObj);\n}\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('pt-BR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(dateObj);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cfloating-theme-toggle.tsx%22%2C%22ids%22%3A%5B%22FloatingThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cscroll-to-top.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cauth%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cfloating-theme-toggle.tsx%22%2C%22ids%22%3A%5B%22FloatingThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cscroll-to-top.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cauth%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/floating-theme-toggle.tsx */ \"(ssr)/./components/floating-theme-toggle.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/scroll-to-top.tsx */ \"(ssr)/./components/scroll-to-top.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth/context.tsx */ \"(ssr)/./lib/auth/context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/providers/QueryProvider.tsx */ \"(ssr)/./lib/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cfloating-theme-toggle.tsx%22%2C%22ids%22%3A%5B%22FloatingThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Cscroll-to-top.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cauth%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Clib%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCliente%5C%5CDocuments%5C%5CCursor%5C%5Ccaca-pecas%5C%5Ccaca_peca%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/@radix-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/next-themes","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/jwt-decode","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCliente%5CDocuments%5CCursor%5Ccaca-pecas%5Ccaca_peca&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();