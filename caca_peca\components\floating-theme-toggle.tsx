"use client"

import * as React from "react"
import { ThemeToggleButton } from "./theme-toggle-button"

export function FloatingThemeToggle() {
  const [visible, setVisible] = React.useState(false)

  React.useEffect(() => {
    // Show the floating toggle after scrolling down
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setVisible(true)
      } else {
        setVisible(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  if (!visible) return null

  return (
    <div className="fixed bottom-4 right-4 z-50 md:bottom-6 md:right-6 transition-opacity duration-300 opacity-80 hover:opacity-100">
      <ThemeToggleButton className="shadow-lg border border-border" />
    </div>
  )
}

