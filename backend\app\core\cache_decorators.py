"""
Cache decorators for automatic caching of function results.
"""
import functools
import inspect
from typing import Any, Callable, Optional, Union
import logging

from app.core.cache import cache_manager, CacheTTL

logger = logging.getLogger(__name__)


def cached(
    key_prefix: str,
    ttl: int = CacheTTL.PARTS_SEARCH,
    key_params: Optional[list] = None,
    skip_cache_if: Optional[Callable] = None
):
    """
    Decorator for caching function results.
    
    Args:
        key_prefix: Prefix for cache key
        ttl: Time to live in seconds
        key_params: List of parameter names to include in cache key
        skip_cache_if: Function to determine if cache should be skipped
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Skip cache if condition is met
            if skip_cache_if and skip_cache_if(*args, **kwargs):
                return func(*args, **kwargs)
            
            # Generate cache key
            cache_key = _generate_function_cache_key(
                func, key_prefix, args, kwargs, key_params
            )
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            
            # Cache the result
            if result is not None:
                cache_manager.set(cache_key, result, ttl)
                logger.debug(f"Cached result for key: {cache_key}")
            
            return result
        
        # Add cache management methods to the function
        wrapper.cache_invalidate = lambda *args, **kwargs: _invalidate_function_cache(
            func, key_prefix, args, kwargs, key_params
        )
        wrapper.cache_key = lambda *args, **kwargs: _generate_function_cache_key(
            func, key_prefix, args, kwargs, key_params
        )
        
        return wrapper
    return decorator


def cache_invalidate_pattern(pattern: str):
    """
    Decorator to invalidate cache patterns after function execution.
    
    Args:
        pattern: Cache key pattern to invalidate
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # Invalidate cache pattern after successful execution
            deleted_count = cache_manager.delete_pattern(pattern)
            if deleted_count > 0:
                logger.info(f"Invalidated {deleted_count} cache keys with pattern: {pattern}")
            
            return result
        return wrapper
    return decorator


def cache_warm_up(
    key_prefix: str,
    ttl: int = CacheTTL.PARTS_SEARCH,
    warm_up_data: list = None
):
    """
    Decorator to warm up cache with predefined data.
    
    Args:
        key_prefix: Prefix for cache key
        ttl: Time to live in seconds
        warm_up_data: List of parameter sets to warm up
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # Warm up cache with predefined data
            if warm_up_data:
                for params in warm_up_data:
                    try:
                        if isinstance(params, dict):
                            warm_result = func(**params)
                        else:
                            warm_result = func(*params)
                        
                        cache_key = _generate_function_cache_key(
                            func, key_prefix, params if not isinstance(params, dict) else (), 
                            params if isinstance(params, dict) else {}
                        )
                        cache_manager.set(cache_key, warm_result, ttl)
                        
                    except Exception as e:
                        logger.warning(f"Failed to warm up cache for {params}: {e}")
            
            return result
        return wrapper
    return decorator


def _generate_function_cache_key(
    func: Callable,
    key_prefix: str,
    args: tuple,
    kwargs: dict,
    key_params: Optional[list] = None
) -> str:
    """
    Generate cache key for a function call.
    
    Args:
        func: Function being cached
        key_prefix: Cache key prefix
        args: Function positional arguments
        kwargs: Function keyword arguments
        key_params: Specific parameters to include in key
        
    Returns:
        Generated cache key
    """
    # Get function signature
    sig = inspect.signature(func)
    bound_args = sig.bind(*args, **kwargs)
    bound_args.apply_defaults()
    
    # Build key parameters
    key_data = {}
    
    if key_params:
        # Use only specified parameters
        for param in key_params:
            if param in bound_args.arguments:
                value = bound_args.arguments[param]
                # Convert complex objects to string representation
                if hasattr(value, '__dict__'):
                    key_data[param] = str(value.__dict__)
                else:
                    key_data[param] = value
    else:
        # Use all parameters except 'self' and database sessions
        for param_name, param_value in bound_args.arguments.items():
            if param_name in ['self', 'db', 'session']:
                continue
            
            # Convert complex objects to string representation
            if hasattr(param_value, '__dict__'):
                key_data[param_name] = str(param_value.__dict__)
            else:
                key_data[param_name] = param_value
    
    return cache_manager.generate_cache_key(key_prefix, **key_data)


def _invalidate_function_cache(
    func: Callable,
    key_prefix: str,
    args: tuple,
    kwargs: dict,
    key_params: Optional[list] = None
) -> bool:
    """
    Invalidate cache for a specific function call.
    
    Args:
        func: Function being cached
        key_prefix: Cache key prefix
        args: Function positional arguments
        kwargs: Function keyword arguments
        key_params: Specific parameters to include in key
        
    Returns:
        True if cache was invalidated, False otherwise
    """
    cache_key = _generate_function_cache_key(func, key_prefix, args, kwargs, key_params)
    return cache_manager.delete(cache_key)


class CacheService:
    """
    Service class for cache management operations.
    """
    
    @staticmethod
    def invalidate_parts_cache():
        """Invalidate all parts-related cache."""
        patterns = [
            "parts:search:*",
            "part:details:*",
            "search:suggestions:*"
        ]
        
        total_deleted = 0
        for pattern in patterns:
            deleted = cache_manager.delete_pattern(pattern)
            total_deleted += deleted
            
        logger.info(f"Invalidated {total_deleted} parts cache entries")
        return total_deleted
    
    @staticmethod
    def invalidate_inventory_cache():
        """Invalidate all inventory-related cache."""
        patterns = [
            "inventory:stats:*",
            "inventory:availability:*",
            "parts:search:*"  # Parts search includes inventory data
        ]
        
        total_deleted = 0
        for pattern in patterns:
            deleted = cache_manager.delete_pattern(pattern)
            total_deleted += deleted
            
        logger.info(f"Invalidated {total_deleted} inventory cache entries")
        return total_deleted
    
    @staticmethod
    def invalidate_dealership_cache(dealership_id: Optional[str] = None):
        """Invalidate dealership-related cache."""
        if dealership_id:
            patterns = [
                f"dealership:info:*{dealership_id}*",
                f"inventory:stats:*{dealership_id}*"
            ]
        else:
            patterns = [
                "dealership:info:*",
                "inventory:stats:*"
            ]
        
        total_deleted = 0
        for pattern in patterns:
            deleted = cache_manager.delete_pattern(pattern)
            total_deleted += deleted
            
        logger.info(f"Invalidated {total_deleted} dealership cache entries")
        return total_deleted
    
    @staticmethod
    def warm_up_popular_searches():
        """Warm up cache with popular search terms."""
        popular_terms = [
            "filtro",
            "pastilha",
            "oleo",
            "pneu",
            "bateria",
            "vela",
            "correia",
            "amortecedor"
        ]
        
        # This would typically call the search service
        # For now, we'll just log the warm-up attempt
        logger.info(f"Warming up cache for {len(popular_terms)} popular search terms")
        return len(popular_terms)
    
    @staticmethod
    def get_cache_statistics() -> dict:
        """Get comprehensive cache statistics."""
        cache_info = cache_manager.get_cache_info()
        
        # Add custom statistics
        cache_info.update({
            "cache_patterns": {
                "parts_search": len(cache_manager.redis_client.keys("parts:search:*")) if cache_manager.is_available() else 0,
                "part_details": len(cache_manager.redis_client.keys("part:details:*")) if cache_manager.is_available() else 0,
                "inventory_stats": len(cache_manager.redis_client.keys("inventory:stats:*")) if cache_manager.is_available() else 0,
                "dealership_info": len(cache_manager.redis_client.keys("dealership:info:*")) if cache_manager.is_available() else 0,
            }
        })
        
        return cache_info
