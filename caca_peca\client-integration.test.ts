import { describe, it, expect, vi, beforeEach } from "vitest"; import { apiClient } from "./lib/api/client"; describe("API Client", () => { beforeEach(() => { vi.clearAllMocks(); }); it("should be defined", () => { expect(apiClient).toBeDefined(); }); it("should have authentication methods", () => { expect(apiClient.login).toBeDefined(); expect(apiClient.logout).toBeDefined(); expect(apiClient.isAuthenticated).toBeDefined(); }); it("should have utility methods", () => { expect(apiClient.healthCheck).toBeDefined(); expect(apiClient.getCurrentUser).toBeDefined(); }); });
