"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, CheckCircle, Mail, MapPin, Phone } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function ContatoPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })

  const [formState, setFormState] = useState<"idle" | "submitting" | "success" | "error">("idle")
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Nome é obrigatório"
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email é obrigatório"
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      newErrors.email = "Email inválido"
    }

    if (!formData.subject.trim()) {
      newErrors.subject = "Assunto é obrigatório"
    }

    if (!formData.message.trim()) {
      newErrors.message = "Mensagem é obrigatória"
    } else if (formData.message.length < 10) {
      newErrors.message = "Mensagem deve ter pelo menos 10 caracteres"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when user types
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setFormState("submitting")

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // In a real application, you would send the form data to your backend
      // const response = await fetch('/api/contact', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData),
      // })

      // if (!response.ok) throw new Error('Failed to send message')

      setFormState("success")
      setFormData({ name: "", email: "", subject: "", message: "" })
    } catch (error) {
      console.error("Error sending message:", error)
      setFormState("error")
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1">
        <div className="bg-gradient-to-b from-blue-50 to-white py-12 dark:from-gray-900 dark:to-gray-950">
          <div className="container">
            <div className="mx-auto max-w-4xl">
              <div className="mb-8 text-center">
                <h1 className="mb-2 text-3xl font-bold tracking-tight md:text-4xl">Entre em Contato</h1>
                <p className="text-lg text-muted-foreground">
                  Estamos aqui para ajudar. Envie-nos uma mensagem e responderemos o mais breve possível.
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                <div className="md:col-span-1">
                  <Card>
                    <CardHeader>
                      <CardTitle>Informações de Contato</CardTitle>
                      <CardDescription>Outras formas de nos contatar</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start gap-3">
                        <Mail className="mt-0.5 h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">Email</p>
                          <a
                            href="mailto:<EMAIL>"
                            className="text-sm text-muted-foreground hover:text-primary"
                          >
                            <EMAIL>
                          </a>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Phone className="mt-0.5 h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">Telefone</p>
                          <a href="tel:+551199999999" className="text-sm text-muted-foreground hover:text-primary">
                            (11) 9999-9999
                          </a>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <MapPin className="mt-0.5 h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">Endereço</p>
                          <p className="text-sm text-muted-foreground">
                            Av. Paulista, 1000 - Bela Vista
                            <br />
                            São Paulo - SP, 01310-100
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="md:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Envie uma Mensagem</CardTitle>
                      <CardDescription>Preencha o formulário abaixo para entrar em contato</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {formState === "success" && (
                        <Alert className="mb-6 bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                          <CheckCircle className="h-4 w-4" />
                          <AlertTitle>Mensagem enviada com sucesso!</AlertTitle>
                          <AlertDescription>
                            Agradecemos seu contato. Responderemos o mais breve possível.
                          </AlertDescription>
                        </Alert>
                      )}

                      {formState === "error" && (
                        <Alert className="mb-6 bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle>Erro ao enviar mensagem</AlertTitle>
                          <AlertDescription>
                            Ocorreu um erro ao enviar sua mensagem. Por favor, tente novamente mais tarde.
                          </AlertDescription>
                        </Alert>
                      )}

                      <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="name">Nome</Label>
                            <Input
                              id="name"
                              name="name"
                              value={formData.name}
                              onChange={handleChange}
                              placeholder="Seu nome completo"
                              className={errors.name ? "border-red-500" : ""}
                              disabled={formState === "submitting" || formState === "success"}
                            />
                            {errors.name && <p className="text-xs text-red-500">{errors.name}</p>}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <Input
                              id="email"
                              name="email"
                              type="email"
                              value={formData.email}
                              onChange={handleChange}
                              placeholder="<EMAIL>"
                              className={errors.email ? "border-red-500" : ""}
                              disabled={formState === "submitting" || formState === "success"}
                            />
                            {errors.email && <p className="text-xs text-red-500">{errors.email}</p>}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="subject">Assunto</Label>
                          <Input
                            id="subject"
                            name="subject"
                            value={formData.subject}
                            onChange={handleChange}
                            placeholder="Assunto da mensagem"
                            className={errors.subject ? "border-red-500" : ""}
                            disabled={formState === "submitting" || formState === "success"}
                          />
                          {errors.subject && <p className="text-xs text-red-500">{errors.subject}</p>}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="message">Mensagem</Label>
                          <Textarea
                            id="message"
                            name="message"
                            value={formData.message}
                            onChange={handleChange}
                            placeholder="Digite sua mensagem aqui..."
                            rows={5}
                            className={errors.message ? "border-red-500" : ""}
                            disabled={formState === "submitting" || formState === "success"}
                          />
                          {errors.message && <p className="text-xs text-red-500">{errors.message}</p>}
                        </div>

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={formState === "submitting" || formState === "success"}
                        >
                          {formState === "submitting" ? "Enviando..." : "Enviar Mensagem"}
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

