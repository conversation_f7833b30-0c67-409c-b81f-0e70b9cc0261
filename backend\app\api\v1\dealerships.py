"""
Dealership management endpoints for the AutoParts API.
"""
from typing import List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.dealership import DealershipService
from app.api.deps import (
    get_current_active_user,
    get_current_admin_user,
    get_current_dealership_user
)
from app.schemas.dealership import (
    DealershipCreate,
    DealershipUpdate,
    Dealership,
    DealershipRegistration,
    DealershipRegistrationResponse,
    DealershipListResponse,
    DealershipSummary,
    DealershipSearchFilters,
    DealershipStats
)
from app.schemas.auth import User as UserSchema
from app.models.user import User

router = APIRouter()


def get_dealership_service(db: Session = Depends(get_db)) -> DealershipService:
    """Get dealership service instance."""
    return DealershipService(db)


@router.post("/register", response_model=DealershipRegistrationResponse)
async def register_dealership(
    registration_data: DealershipRegistration,
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Register a new dealership with admin user.
    
    This endpoint creates both a dealership and its admin user in a single transaction.
    
    Args:
        registration_data: Dealership registration data including admin user info
        dealership_service: Dealership service instance
        
    Returns:
        Registration response with dealership and admin user info
        
    Raises:
        HTTPException: If registration fails due to validation or duplicate data
    """
    try:
        dealership, admin_user = dealership_service.register_dealership_with_admin(
            registration_data
        )
        
        return DealershipRegistrationResponse(
            dealership=Dealership.from_orm(dealership),
            admin_user_id=admin_user.id,
            message="Dealership registered successfully"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/", response_model=Dealership)
async def create_dealership(
    dealership_data: DealershipCreate,
    current_user: User = Depends(get_current_admin_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Create a new dealership (admin only).
    
    Args:
        dealership_data: Dealership creation data
        current_user: Current admin user
        dealership_service: Dealership service instance
        
    Returns:
        Created dealership
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        dealership = dealership_service.create_dealership(dealership_data)
        return Dealership.from_orm(dealership)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=DealershipListResponse)
async def list_dealerships(
    search: str = Query(None, description="Search term for name, trading name, or city"),
    city: str = Query(None, description="Filter by city"),
    state: str = Query(None, description="Filter by state"),
    active_only: bool = Query(True, description="Include only active dealerships"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_active_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    List dealerships with filtering and pagination.
    
    Args:
        search: Search term for name, trading name, or city
        city: Filter by city
        state: Filter by state
        active_only: Include only active dealerships
        page: Page number
        per_page: Items per page
        current_user: Current authenticated user
        dealership_service: Dealership service instance
        
    Returns:
        Paginated list of dealerships
    """
    filters = DealershipSearchFilters(
        search=search,
        city=city,
        state=state,
        active_only=active_only,
        page=page,
        per_page=per_page
    )
    
    dealerships, total = dealership_service.list_dealerships(filters)
    
    # Convert to summary format
    dealership_summaries = [
        DealershipSummary(
            id=d.id,
            name=d.name,
            city=d.city,
            state=d.state,
            is_active=d.is_active,
            inventory_count=0  # TODO: Add inventory count
        )
        for d in dealerships
    ]
    
    pages = (total + per_page - 1) // per_page
    
    return DealershipListResponse(
        dealerships=dealership_summaries,
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/stats", response_model=DealershipStats)
async def get_dealership_stats(
    current_user: User = Depends(get_current_admin_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Get dealership statistics (admin only).
    
    Args:
        current_user: Current admin user
        dealership_service: Dealership service instance
        
    Returns:
        Dealership statistics
    """
    return dealership_service.get_dealership_stats()


@router.get("/{dealership_id}", response_model=Dealership)
async def get_dealership(
    dealership_id: UUID,
    current_user: User = Depends(get_current_active_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Get dealership by ID.

    Args:
        dealership_id: Dealership UUID
        current_user: Current user with dealership access
        dealership_service: Dealership service instance

    Returns:
        Dealership details

    Raises:
        HTTPException: If dealership not found or access denied
    """
    # Check dealership access
    if not current_user.is_admin:
        if (current_user.role.value != "dealership" or
            str(current_user.dealership_id) != str(dealership_id)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions for this dealership"
            )

    dealership = dealership_service.get_dealership_by_id(dealership_id)

    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found"
        )

    return Dealership.from_orm(dealership)


@router.put("/{dealership_id}", response_model=Dealership)
async def update_dealership(
    dealership_id: UUID,
    update_data: DealershipUpdate,
    current_user: User = Depends(get_current_active_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Update dealership information.

    Args:
        dealership_id: Dealership UUID
        update_data: Updated dealership data
        current_user: Current user with dealership access
        dealership_service: Dealership service instance

    Returns:
        Updated dealership

    Raises:
        HTTPException: If dealership not found, access denied, or update fails
    """
    # Check dealership access
    if not current_user.is_admin:
        if (current_user.role.value != "dealership" or
            str(current_user.dealership_id) != str(dealership_id)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions for this dealership"
            )

    try:
        dealership = dealership_service.update_dealership(dealership_id, update_data)

        if not dealership:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Dealership not found"
            )

        return Dealership.from_orm(dealership)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{dealership_id}/activate", response_model=Dealership)
async def activate_dealership(
    dealership_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Activate a dealership (admin only).
    
    Args:
        dealership_id: Dealership UUID
        current_user: Current admin user
        dealership_service: Dealership service instance
        
    Returns:
        Activated dealership
        
    Raises:
        HTTPException: If dealership not found
    """
    dealership = dealership_service.activate_dealership(dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found"
        )
    
    return Dealership.from_orm(dealership)


@router.post("/{dealership_id}/deactivate", response_model=Dealership)
async def deactivate_dealership(
    dealership_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Deactivate a dealership (admin only).
    
    Args:
        dealership_id: Dealership UUID
        current_user: Current admin user
        dealership_service: Dealership service instance
        
    Returns:
        Deactivated dealership
        
    Raises:
        HTTPException: If dealership not found
    """
    dealership = dealership_service.deactivate_dealership(dealership_id)
    
    if not dealership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dealership not found"
        )
    
    return Dealership.from_orm(dealership)


@router.get("/{dealership_id}/users", response_model=List[UserSchema])
async def get_dealership_users(
    dealership_id: UUID,
    current_user: User = Depends(get_current_active_user),
    dealership_service: DealershipService = Depends(get_dealership_service)
):
    """
    Get users associated with a dealership.

    Args:
        dealership_id: Dealership UUID
        current_user: Current user with dealership access
        dealership_service: Dealership service instance

    Returns:
        List of users associated with the dealership

    Raises:
        HTTPException: If access denied
    """
    # Check dealership access
    if not current_user.is_admin:
        if (current_user.role.value != "dealership" or
            str(current_user.dealership_id) != str(dealership_id)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions for this dealership"
            )

    users = dealership_service.get_dealership_users(dealership_id)
    return [UserSchema.from_orm(user) for user in users]
