"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Check, 
  X, 
  AlertTriangle, 
  Car, 
  Search,
  Loader2,
  Info,
  ExternalLink
} from "lucide-react"
import { useVehicleMakes, useVehicleModels, useVehicleYears, useCheckCompatibility } from "@/lib/hooks/useParts"
import { Part, PartCompatibilityCheck } from "@/lib/types/api"

interface CompatibilityCheckerProps {
  part: Part
  onAlternativeSelect?: (part: Part) => void
}

export function CompatibilityChecker({ part, onAlternativeSelect }: CompatibilityCheckerProps) {
  const [selectedVehicle, setSelectedVehicle] = useState({
    make: "",
    model: "",
    year: 0
  })
  const [showResults, setShowResults] = useState(false)

  // API hooks
  const { data: makesResponse } = useVehicleMakes()
  const { data: modelsResponse } = useVehicleModels(selectedVehicle.make, !!selectedVehicle.make)
  const { data: yearsResponse } = useVehicleYears(
    selectedVehicle.make, 
    selectedVehicle.model, 
    !!selectedVehicle.make && !!selectedVehicle.model
  )
  
  const checkCompatibilityMutation = useCheckCompatibility()

  // Extract data from API responses
  const makes = makesResponse?.data?.success ? makesResponse.data.data : []
  const models = modelsResponse?.data?.success ? modelsResponse.data.data : []
  const years = yearsResponse?.data?.success ? yearsResponse.data.data : []

  const handleCheck = async () => {
    if (!selectedVehicle.make || !selectedVehicle.model || !selectedVehicle.year) {
      return
    }

    try {
      await checkCompatibilityMutation.mutateAsync({
        partId: part.id,
        vehicle: selectedVehicle
      })
      setShowResults(true)
    } catch (error) {
      console.error('Error checking compatibility:', error)
    }
  }

  const handleReset = () => {
    setSelectedVehicle({ make: "", model: "", year: 0 })
    setShowResults(false)
    checkCompatibilityMutation.reset()
  }

  const compatibilityResult = checkCompatibilityMutation.data?.data?.success 
    ? checkCompatibilityMutation.data.data.data 
    : null

  const getCompatibilityIcon = (compatible: boolean, confidence: number) => {
    if (compatible && confidence >= 90) {
      return <Check className="h-5 w-5 text-green-600" />
    } else if (compatible && confidence >= 70) {
      return <AlertTriangle className="h-5 w-5 text-yellow-600" />
    } else {
      return <X className="h-5 w-5 text-red-600" />
    }
  }

  const getCompatibilityColor = (compatible: boolean, confidence: number) => {
    if (compatible && confidence >= 90) {
      return "text-green-600 bg-green-50 border-green-200"
    } else if (compatible && confidence >= 70) {
      return "text-yellow-600 bg-yellow-50 border-yellow-200"
    } else {
      return "text-red-600 bg-red-50 border-red-200"
    }
  }

  const getCompatibilityMessage = (compatible: boolean, confidence: number) => {
    if (compatible && confidence >= 90) {
      return "Totalmente compatível"
    } else if (compatible && confidence >= 70) {
      return "Compatível com ressalvas"
    } else {
      return "Não compatível"
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Verificador de Compatibilidade
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Part Info */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="font-semibold mb-2">Peça Selecionada</h4>
          <div className="space-y-1">
            <p className="font-medium">{part.name}</p>
            <p className="text-sm text-muted-foreground">Código: {part.part_number}</p>
            <p className="text-sm text-muted-foreground">Marca: {part.brand}</p>
          </div>
        </div>

        {/* Vehicle Selection */}
        <div className="space-y-4">
          <h4 className="font-semibold">Selecione seu Veículo</h4>
          
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label>Marca</Label>
              <Select
                value={selectedVehicle.make}
                onValueChange={(value) => {
                  setSelectedVehicle(prev => ({ 
                    make: value, 
                    model: "", 
                    year: 0 
                  }))
                  setShowResults(false)
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a marca" />
                </SelectTrigger>
                <SelectContent>
                  {makes.map((make) => (
                    <SelectItem key={make} value={make}>
                      {make}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Modelo</Label>
              <Select
                value={selectedVehicle.model}
                onValueChange={(value) => {
                  setSelectedVehicle(prev => ({ 
                    ...prev, 
                    model: value, 
                    year: 0 
                  }))
                  setShowResults(false)
                }}
                disabled={!selectedVehicle.make}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o modelo" />
                </SelectTrigger>
                <SelectContent>
                  {models.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Ano</Label>
              <Select
                value={selectedVehicle.year.toString()}
                onValueChange={(value) => {
                  setSelectedVehicle(prev => ({ 
                    ...prev, 
                    year: parseInt(value) 
                  }))
                  setShowResults(false)
                }}
                disabled={!selectedVehicle.model}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o ano" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={handleCheck}
              disabled={!selectedVehicle.make || !selectedVehicle.model || !selectedVehicle.year || checkCompatibilityMutation.isPending}
              className="flex items-center gap-2"
            >
              {checkCompatibilityMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              Verificar Compatibilidade
            </Button>
            
            {showResults && (
              <Button variant="outline" onClick={handleReset}>
                Nova Consulta
              </Button>
            )}
          </div>
        </div>

        {/* Results */}
        {checkCompatibilityMutation.isError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Erro ao verificar compatibilidade. Tente novamente.
            </AlertDescription>
          </Alert>
        )}

        {compatibilityResult && showResults && (
          <div className="space-y-4">
            {/* Main Result */}
            <Card className={`border-2 ${getCompatibilityColor(compatibilityResult.compatible, compatibilityResult.confidence)}`}>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  {getCompatibilityIcon(compatibilityResult.compatible, compatibilityResult.confidence)}
                  <div className="flex-1">
                    <h4 className="font-semibold">
                      {getCompatibilityMessage(compatibilityResult.compatible, compatibilityResult.confidence)}
                    </h4>
                    <p className="text-sm opacity-80">
                      {selectedVehicle.make} {selectedVehicle.model} {selectedVehicle.year}
                    </p>
                  </div>
                  <Badge variant="outline" className="font-mono">
                    {compatibilityResult.confidence}% confiança
                  </Badge>
                </div>
                
                {compatibilityResult.notes && (
                  <div className="mt-3 p-3 bg-background/50 rounded border">
                    <div className="flex items-start gap-2">
                      <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <p className="text-sm">{compatibilityResult.notes}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Required Modifications */}
            {compatibilityResult.required_modifications && compatibilityResult.required_modifications.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Modificações necessárias:</strong>
                  <ul className="mt-2 list-disc list-inside space-y-1">
                    {compatibilityResult.required_modifications.map((modification, index) => (
                      <li key={index} className="text-sm">{modification}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Alternative Parts */}
            {compatibilityResult.alternative_parts && compatibilityResult.alternative_parts.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-semibold">Peças Alternativas Compatíveis</h4>
                <div className="grid gap-3">
                  {compatibilityResult.alternative_parts.map((altPart) => (
                    <Card key={altPart.id} className="border-dashed">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <h5 className="font-medium">{altPart.name}</h5>
                            <p className="text-sm text-muted-foreground">
                              {altPart.part_number} • {altPart.brand}
                            </p>
                            <p className="text-sm font-semibold text-primary">
                              {new Intl.NumberFormat('pt-BR', {
                                style: 'currency',
                                currency: 'BRL'
                              }).format(altPart.price || 0)}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => onAlternativeSelect?.(altPart)}
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              Ver Detalhes
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
