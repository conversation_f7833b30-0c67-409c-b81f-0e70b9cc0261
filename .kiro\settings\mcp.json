{"mcpServers": {"ripgrep": {"command": "npx", "args": ["-y", "mcp-ripgrep@latest"], "disabled": false, "autoApprove": []}, "MCP_FS": {"command": "docker", "args": ["run", "-i", "--rm", "-v", "C:/Users/<USER>/Documents/Cursor/caca-pecas:/workspace", "ghcr.io/mark3labs/mcp-filesystem-server:latest", "/workspace"], "disabled": false, "autoApprove": []}, "sequentialthinking": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/sequentialthinking"], "disabled": false, "autoApprove": []}, "stripe": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "STRIPE_SECRET_KEY", "mcp/stripe", "--tools=all"], "env": {"STRIPE_SECRET_KEY": "sk_test_51RRgcWPldymAioXD20EkXomM0WrYgjKNYDgXv2dLWZxMGtJJy01CGjg7dykjnTTmxm7f6ilON4EGwkWMVp4l1g5600iUlR0utd"}, "disabled": false, "autoApprove": []}, "memory": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/memory"], "disabled": false, "autoApprove": []}, "fetch": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/fetch"], "disabled": false, "autoApprove": ["fetch"]}, "desktop-commander": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/desktop-commander"], "disabled": false, "autoApprove": ["list_directory", "write_file", "write_file", "edit_block", "create_directory"]}, "github-official": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "mcp/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "disabled": false, "autoApprove": []}, "notion": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "INTERNAL_INTEGRATION_TOKEN", "mcp/notion"], "env": {"INTERNAL_INTEGRATION_TOKEN": "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"}, "disabled": false, "autoApprove": []}, "TaskManager": {"command": "npx", "args": ["-y", "@kazuph/mcp-taskmanager"], "env": {"TASK_MANAGER_FILE_PATH": "~/Documents/tasks.json"}, "disabled": false, "autoApprove": []}, "Memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "memory.json"}, "disabled": false, "autoApprove": []}, "Filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Documents/Cursor/caca-pecas/"], "env": {}, "disabled": false, "autoApprove": ["edit_file", "read_file"]}, "file-context": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "CACHE_TTL=3600000", "-e", "MAX_CACHE_SIZE=1000", "-e", "MAX_FILE_SIZE=1048576", "-v", "C:/Users/<USER>/Documents/Cursor/caca-pecas:/workspace", "mcp-file-context-server"], "disabled": false, "autoApprove": []}}}