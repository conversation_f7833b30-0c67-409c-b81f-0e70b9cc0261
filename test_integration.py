#!/usr/bin/env python3
"""
Integration test script for AutoParts API and Frontend.
Tests the connection between Next.js frontend and FastAPI backend.
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

class IntegrationTester:
    def __init__(self):
        self.backend_url = BACKEND_URL
        self.frontend_url = FRONTEND_URL
        self.session = None
        self.test_results = []

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def test_backend_health(self) -> bool:
        """Test backend health endpoint."""
        try:
            async with self.session.get(f"{self.backend_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Backend health check passed: {data.get('status')}")
                    return True
                else:
                    print(f"❌ Backend health check failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Backend health check error: {e}")
            return False

    async def test_backend_api_docs(self) -> bool:
        """Test backend API documentation."""
        try:
            async with self.session.get(f"{self.backend_url}/api/v1/docs") as response:
                if response.status == 200:
                    print("✅ Backend API docs accessible")
                    return True
                else:
                    print(f"❌ Backend API docs failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Backend API docs error: {e}")
            return False

    async def test_backend_openapi(self) -> bool:
        """Test backend OpenAPI schema."""
        try:
            async with self.session.get(f"{self.backend_url}/api/v1/openapi.json") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Backend OpenAPI schema accessible: {data.get('info', {}).get('title')}")
                    return True
                else:
                    print(f"❌ Backend OpenAPI schema failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Backend OpenAPI schema error: {e}")
            return False

    async def test_cors_headers(self) -> bool:
        """Test CORS headers."""
        try:
            headers = {
                'Origin': 'http://localhost:3000',
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            }
            async with self.session.options(f"{self.backend_url}/api/v1/", headers=headers) as response:
                cors_origin = response.headers.get('Access-Control-Allow-Origin')
                if cors_origin:
                    print(f"✅ CORS headers present: {cors_origin}")
                    return True
                else:
                    print("❌ CORS headers missing")
                    return False
        except Exception as e:
            print(f"❌ CORS test error: {e}")
            return False

    async def test_api_endpoints(self) -> bool:
        """Test key API endpoints."""
        endpoints = [
            "/api/v1/",
            "/api/v1/parts/categories",
            "/api/v1/parts/brands",
            "/api/v1/dealerships/states"
        ]
        
        all_passed = True
        for endpoint in endpoints:
            try:
                async with self.session.get(f"{self.backend_url}{endpoint}") as response:
                    if response.status == 200:
                        print(f"✅ Endpoint {endpoint} accessible")
                    else:
                        print(f"❌ Endpoint {endpoint} failed: {response.status}")
                        all_passed = False
            except Exception as e:
                print(f"❌ Endpoint {endpoint} error: {e}")
                all_passed = False
        
        return all_passed

    async def test_frontend_health(self) -> bool:
        """Test frontend accessibility."""
        try:
            async with self.session.get(self.frontend_url) as response:
                if response.status == 200:
                    print("✅ Frontend accessible")
                    return True
                else:
                    print(f"❌ Frontend failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Frontend error: {e}")
            return False

    async def test_api_integration(self) -> bool:
        """Test API integration with sample data."""
        try:
            # Test parts search
            async with self.session.get(f"{self.backend_url}/api/v1/parts/search?query=test") as response:
                if response.status in [200, 404]:  # 404 is OK if no data
                    print("✅ Parts search endpoint working")
                    return True
                else:
                    print(f"❌ Parts search failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ API integration error: {e}")
            return False

    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all integration tests."""
        print("🚀 Starting AutoParts Integration Tests\n")
        
        tests = [
            ("Backend Health", self.test_backend_health),
            ("Backend API Docs", self.test_backend_api_docs),
            ("Backend OpenAPI", self.test_backend_openapi),
            ("CORS Headers", self.test_cors_headers),
            ("API Endpoints", self.test_api_endpoints),
            ("Frontend Health", self.test_frontend_health),
            ("API Integration", self.test_api_integration),
        ]
        
        results = {}
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            try:
                result = await test_func()
                results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results[test_name] = False
        
        return results

    def print_summary(self, results: Dict[str, bool]):
        """Print test summary."""
        print("\n" + "="*50)
        print("📊 INTEGRATION TEST SUMMARY")
        print("="*50)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<20} {status}")
        
        print(f"\nTotal: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Integration is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Please check the configuration.")
            return False

async def main():
    """Main test runner."""
    async with IntegrationTester() as tester:
        results = await tester.run_all_tests()
        success = tester.print_summary(results)
        
        if not success:
            sys.exit(1)

if __name__ == "__main__":
    print("AutoParts Integration Test Suite")
    print("Make sure both backend (port 8000) and frontend (port 3000) are running.\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        sys.exit(1)
