/**
 * Registration Page
 * Dedicated registration page with enhanced form
 */

import React from 'react';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { RegisterForm } from '@/components/auth/register-form';
import { UserRole } from '@/lib/types/api';

export default function RegisterPage() {
  return (
    <div className="container flex min-h-screen flex-col items-center justify-center py-8">
      <Link href="/" className="mb-8 flex items-center space-x-2">
        <ShoppingCart className="h-6 w-6 text-primary" />
        <span className="text-xl font-bold">AutoParts</span>
      </Link>

      <RegisterForm 
        defaultRole={UserRole.CUSTOMER}
        showRoleSelection={true}
      />

      <div className="mt-6 text-center text-sm text-muted-foreground">
        <p>
          Já tem uma conta?{' '}
          <Link href="/auth/login" className="text-primary hover:underline">
            Faça login
          </Link>
        </p>
      </div>
    </div>
  );
}